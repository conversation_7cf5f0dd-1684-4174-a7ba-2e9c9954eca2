import os
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required
from werkzeug.utils import secure_filename
from models.slider import db, Slider
from werkzeug.exceptions import RequestEntityTooLarge

slider_bp = Blueprint('slider', __name__)

def init_app(app):
    pass

# İzin verilen dosya uzantıları
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'webm', 'ogg'}

def allowed_file(filename, file_type='image'):
    allowed_extensions = ALLOWED_IMAGE_EXTENSIONS if file_type == 'image' else ALLOWED_VIDEO_EXTENSIONS
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_file(file, file_type):
    if file and allowed_file(file.filename, file_type):
        filename = secure_filename(file.filename)
        # <PERSON><PERSON><PERSON> dosya adı oluştur
        base, ext = os.path.splitext(filename)
        filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        
        # Uploads klasörünü kontrol et ve oluştur
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        return filename
    return None

@slider_bp.route('/admin/slider')
@login_required
def slider_list():
    sliders = Slider.query.order_by(Slider.order).all()
    return render_template('admin/slider/list.html', sliders=sliders)

@slider_bp.route('/admin/slider/create', methods=['GET', 'POST'])
@login_required
def slider_create():
    try:
        if request.method == 'POST':
            file = request.files.get('file')
            file_type = request.form.get('file_type', 'image')
            category = request.form.get('category', 'intro')
            
            if file:
                filename = save_file(file, file_type)
                if filename:
                    slider = Slider(
                        title=request.form.get('title'),
                        description=request.form.get('description'),
                        file_type=file_type,
                        file_path=filename,
                        category=category,
                        order=request.form.get('order', type=int)
                    )
                    db.session.add(slider)
                    db.session.commit()
                    flash('Slider başarıyla eklendi!')
                    return redirect(url_for('slider.slider_list'))
                else:
                    flash('Geçersiz dosya formatı!')
            else:
                flash('Lütfen bir dosya seçin!')
        
        return render_template('admin/slider/create.html')
    except RequestEntityTooLarge:
        flash('Dosya boyutu çok büyük! Maksimum dosya boyutu: 500MB', 'error')
        return redirect(url_for('slider.slider_create'))

@slider_bp.route('/admin/slider/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def slider_edit(id):
    slider = Slider.query.get_or_404(id)
    
    if request.method == 'POST':
        file = request.files.get('file')
        file_type = request.form.get('file_type', slider.file_type)
        category = request.form.get('category', slider.category)
        
        if file:
            filename = save_file(file, file_type)
            if filename:
                if slider.file_path:
                    old_file = os.path.join(current_app.root_path, 'static', 'uploads', slider.file_path)
                    if os.path.exists(old_file):
                        os.remove(old_file)
                
                slider.file_path = filename
                slider.file_type = file_type
        
        slider.title = request.form.get('title')
        slider.description = request.form.get('description')
        slider.category = category
        slider.order = request.form.get('order', type=int)
        slider.active = request.form.get('active') == 'on'
        
        db.session.commit()
        flash('Slider başarıyla güncellendi!')
        return redirect(url_for('slider.slider_list'))
    
    return render_template('admin/slider/edit.html', slider=slider)

@slider_bp.route('/admin/slider/delete/<int:id>')
@login_required
def slider_delete(id):
    slider = Slider.query.get_or_404(id)
    
    # Dosyayı sil
    if slider.file_path:
        file_path = os.path.join(current_app.root_path, 'static', 'uploads', slider.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    db.session.delete(slider)
    db.session.commit()
    flash('Slider başarıyla silindi!')
    return redirect(url_for('slider.slider_list'))