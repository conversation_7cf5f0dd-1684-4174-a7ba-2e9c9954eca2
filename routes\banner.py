from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required
from models import db
from models.banner import Banner
import os
from werkzeug.utils import secure_filename
from datetime import datetime
import logging

banner_bp = Blueprint('banner', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'mp4', 'webm', 'ogg', 'mov'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def is_video_file(filename):
    video_extensions = {'mp4', 'webm', 'ogg', 'mov'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in video_extensions

@banner_bp.route('/admin/banners')
@login_required
def banner_list():
    try:
        # Tüm bannerları getir, filtreleme yapmadan 
        banners = Banner.query.order_by(Banner.order.asc()).all()
        current_app.logger.info(f"Toplam {len(banners)} banner bulundu.")
        return render_template('admin/banner/list.html', banners=banners)
    except Exception as e:
        current_app.logger.error(f"Banner listesi görüntülenirken hata: {str(e)}")
        flash('Banner listesi yüklenirken bir hata oluştu.', 'error')
        return redirect(url_for('admin.dashboard'))

@banner_bp.route('/admin/banners/create', methods=['GET', 'POST'])
@login_required
def banner_create():
    try:
        if request.method == 'POST':
            file_type = request.form.get('file_type', 'image')
            filename = None
            
            if file_type == 'image' and 'image' in request.files:
                file = request.files['image']
                if file and allowed_file(file.filename) and not is_video_file(file.filename):
                    # Dosya adını güvenli hale getir
                    filename = secure_filename(file.filename)
                    # Özel karakterleri kaldır
                    filename = ''.join(e for e in filename if e.isalnum() or e == '.')
                    # Timestamp ekle
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"banner_{timestamp}_{filename}"
                    
                    # Klasör yolunu kontrol et
                    upload_path = os.path.join('static', 'uploads', 'banners')
                    if not os.path.exists(upload_path):
                        os.makedirs(upload_path)
                    
                    # Dosyayı kaydet
                    file_path = os.path.join(upload_path, filename)
                    file.save(file_path)
            
            elif file_type == 'video' and 'video' in request.files:
                file = request.files['video']
                if file and allowed_file(file.filename) and is_video_file(file.filename):
                    # Dosya adını güvenli hale getir
                    filename = secure_filename(file.filename)
                    # Özel karakterleri kaldır
                    filename = ''.join(e for e in filename if e.isalnum() or e == '.')
                    # Timestamp ekle
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"banner_video_{timestamp}_{filename}"
                    
                    # Klasör yolunu kontrol et
                    upload_path = os.path.join('static', 'uploads', 'banners')
                    if not os.path.exists(upload_path):
                        os.makedirs(upload_path)
                    
                    # Dosyayı kaydet
                    file_path = os.path.join(upload_path, filename)
                    file.save(file_path)
            
            # Banner oluştur
            banner = Banner(
                title=request.form.get('title'),
                subtitle=request.form.get('subtitle'),
                description=request.form.get('description'),
                button_text=request.form.get('button_text'),
                button_link=request.form.get('button_link'),
                category=request.form.get('category', 'main'),
                order=request.form.get('order', type=int, default=0),
                active=bool(request.form.get('active')),
                file_type=file_type
            )
            
            # Dosya tipine göre banner alanlarını ayarla
            if file_type == 'image' and filename:
                banner.image = filename
            elif file_type == 'video':
                if filename:  # Video dosyası yüklendi
                    banner.video = filename
                else:  # Video URL'si girildi
                    banner.video_url = request.form.get('video_url', '')
            
            db.session.add(banner)
            db.session.commit()
            
            flash('Banner başarıyla oluşturuldu.', 'success')
            return redirect(url_for('banner.banner_list'))
            
        return render_template('admin/banner/create.html')
            
    except Exception as e:
        current_app.logger.error(f"Banner oluşturulurken hata: {str(e)}")
        flash('Banner oluşturulurken bir hata oluştu.', 'error')
        return redirect(url_for('banner.banner_list'))

@banner_bp.route('/admin/banners/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def banner_edit(id):
    try:
        banner = Banner.query.get_or_404(id)
        
        # Eski banner verilerini yeni formata uygun hale getir
        if not hasattr(banner, 'file_type') or banner.file_type is None:
            banner.file_type = 'image'
        
        if request.method == 'POST':
            banner.title = request.form.get('title')
            banner.subtitle = request.form.get('subtitle')
            banner.description = request.form.get('description')
            banner.button_text = request.form.get('button_text')
            banner.button_link = request.form.get('button_link')
            banner.order = request.form.get('order', type=int, default=0)
            banner.active = bool(request.form.get('active'))
            banner.category = request.form.get('category', 'main')
            
            file_type = request.form.get('file_type', 'image')
            banner.file_type = file_type
            
            if file_type == 'image':
                # Eğer yeni resim yüklendiyse
                if 'image' in request.files:
                    file = request.files['image']
                    if file and allowed_file(file.filename) and not is_video_file(file.filename):
                        # Eski resmi sil
                        if banner.image:
                            old_image_path = os.path.join('static', 'uploads', 'banners', banner.image)
                            if os.path.exists(old_image_path):
                                os.remove(old_image_path)
                        
                        filename = secure_filename(file.filename)
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"banner_{timestamp}_{filename}"
                        file.save(os.path.join('static', 'uploads', 'banners', filename))
                        banner.image = filename
                
                # Video alanlarını temizle
                if hasattr(banner, 'video'):
                    banner.video = None
                if hasattr(banner, 'video_url'):
                    banner.video_url = None
                
            elif file_type == 'video':
                # Video dosyası yüklendi mi?
                if 'video' in request.files:
                    file = request.files['video']
                    if file and file.filename and allowed_file(file.filename) and is_video_file(file.filename):
                        # Eski videoyu sil
                        if hasattr(banner, 'video') and banner.video:
                            old_video_path = os.path.join('static', 'uploads', 'banners', banner.video)
                            if os.path.exists(old_video_path):
                                os.remove(old_video_path)
                        
                        filename = secure_filename(file.filename)
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"banner_video_{timestamp}_{filename}"
                        file.save(os.path.join('static', 'uploads', 'banners', filename))
                        banner.video = filename
                        banner.video_url = None
                
                # Video URL'si girildiyse
                video_url = request.form.get('video_url')
                if video_url and (not hasattr(banner, 'video') or not banner.video):
                    banner.video_url = video_url
                    # Eğer eski bir video dosyası varsa sil
                    if hasattr(banner, 'video') and banner.video:
                        old_video_path = os.path.join('static', 'uploads', 'banners', banner.video)
                        if os.path.exists(old_video_path):
                            os.remove(old_video_path)
                        banner.video = None
                
                # Resim alanını temizle
                if banner.image and ((hasattr(banner, 'video') and banner.video) or (hasattr(banner, 'video_url') and banner.video_url)):
                    old_image_path = os.path.join('static', 'uploads', 'banners', banner.image)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
                    banner.image = None

            db.session.commit()
            flash('Banner başarıyla güncellendi.', 'success')
            return redirect(url_for('banner.banner_list'))

        return render_template('admin/banner/edit.html', banner=banner)
        
    except Exception as e:
        current_app.logger.error(f"Banner düzenlenirken hata: {str(e)}")
        flash('Banner düzenlenirken bir hata oluştu.', 'error')
        return redirect(url_for('banner.banner_list'))

@banner_bp.route('/admin/banners/delete/<int:id>', methods=['POST'])
@login_required
def banner_delete(id):
    banner = Banner.query.get_or_404(id)
    
    if banner.image:
        image_path = os.path.join('static/uploads/banners', banner.image)
        if os.path.exists(image_path):
            os.remove(image_path)
    
    if banner.video:
        video_path = os.path.join('static/uploads/banners', banner.video)
        if os.path.exists(video_path):
            os.remove(video_path)
    
    db.session.delete(banner)
    db.session.commit()
    flash('Banner başarıyla silindi.', 'success')
    return redirect(url_for('banner.banner_list')) 