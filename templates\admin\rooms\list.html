{% extends "admin/base.html" %}

{% block breadcrumb %}Odalar{% endblock %}
{% block page_title %}Odalar{% endblock %}
{% block page_subtitle %}Tüm odaları listeleyin ve yönetin{% endblock %}

{% block admin_content %}
<div class="p-4">
    <div class="w-full">
        <!-- Üst Kısım -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
                <div class="bg-teal-50 p-2 rounded-lg">
                    <i class="fas fa-bed text-teal-500 text-xl"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Odalar</h2>
                    <p class="text-sm text-gray-500">Toplam {{ rooms|length }} oda</p>
                </div>
            </div>
            <a href="{{ url_for('rooms.room_create') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700">
                <i class="fas fa-plus mr-2"></i>
                Yeni Oda Ekle
            </a>
        </div>

        <!-- Tablo -->
        <div class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Oda
                        </th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Kategori
                        </th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Kapasite
                        </th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Durum
                        </th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            İşlemler
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for room in rooms %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                {% if room.images_list %}
                                <div class="flex-shrink-0 h-16 w-16">
                                    <img class="h-16 w-16 rounded-lg object-cover" 
                                         src="{{ url_for('static', filename='uploads/rooms/' + room.images_list[0]) }}" 
                                         alt="{{ get_lang_text(room, 'title') }}">
                                </div>
                                {% else %}
                                <div class="flex-shrink-0 h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xl"></i>
                                </div>
                                {% endif %}
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ get_lang_text(room, 'title') }}</div>
                                    <div class="text-sm text-gray-500 flex items-center space-x-2">
                                        <span><i class="fas fa-ruler-combined mr-1"></i>{{ room.size }}m²</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            {% if room.room_category %}
                            <div class="flex items-center">
                                <i class="{{ room.room_category.icon }} text-gray-500 mr-2"></i>
                                <span class="text-sm text-gray-900">{{ get_lang_text(room.room_category, 'name') }}</span>
                            </div>
                            {% endif %}
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm text-gray-900">{{ room.capacity }} Kişi</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            {% if room.status == 'active' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                Aktif
                            </span>
                            {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Pasif
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex items-center space-x-3">
                                <a href="{{ url_for('rooms.room_edit', id=room.id) }}" 
                                   class="text-teal-600 hover:text-teal-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('rooms.room_delete', id=room.id) }}" 
                                      method="POST" 
                                      class="inline-block delete-form"
                                      onsubmit="return confirm('Bu odayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')">
                                    <button type="submit" class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 