from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models import db
from models.gallery import Gallery
import os
from werkzeug.utils import secure_filename
from datetime import datetime
from flask import current_app

gallery_bp = Blueprint('gallery', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

UPLOAD_FOLDER = os.path.join('static', 'uploads', 'gallery')

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@gallery_bp.route('/admin/gallery')
@login_required
def admin_gallery_list():
    # Order değeri küçükten büyüğe sıralanır (en küçük order değeri en üstte görünür)
    galleries = Gallery.query.order_by(Gallery.order.asc()).all()
    return render_template('admin/gallery/list.html', galleries=galleries)

@gallery_bp.route('/admin/gallery/create', methods=['GET', 'POST'])
@login_required
def admin_gallery_create():
    if request.method == 'POST':
        try:
            # Debug bilgisi
            print("Form data:", request.form)
            print("Files:", request.files)

            title = request.form.get('title')
            description = request.form.get('description')
            category = request.form.get('category')
            order = int(request.form.get('order', 1))
            image = request.files.get('image')

            print("Upload path:", os.path.join(current_app.root_path, UPLOAD_FOLDER))
            print("File permissions:", oct(os.stat(current_app.root_path).st_mode)[-3:])

            if image and allowed_file(image.filename):
                try:
                    # Upload klasörünü oluştur
                    upload_path = os.path.join(current_app.root_path, UPLOAD_FOLDER)
                    os.makedirs(upload_path, exist_ok=True)

                    # Dosya adını oluştur
                    filename = secure_filename(image.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{timestamp}_{filename}"

                    # Tam yolu belirle ve kaydet
                    file_path = os.path.join(upload_path, filename)
                    image.save(file_path)

                    # Dosya izinlerini ayarla
                    os.chmod(file_path, 0o644)

                    gallery = Gallery(
                        title=title,
                        description=description,
                        category=category,
                        image=filename,
                        order=order  # Kullanıcının belirlediği sıralama numarası
                    )

                    db.session.add(gallery)
                    db.session.commit()
                    flash('Galeri öğesi başarıyla eklendi.', 'success')
                    return redirect(url_for('gallery.admin_gallery_list'))
                except Exception as e:
                    flash(f'Resim yüklenirken bir hata oluştu: {str(e)}', 'error')
                    return redirect(url_for('gallery.admin_gallery_create'))

        except Exception as e:
            print("Error:", str(e))
            flash(f'Bir hata oluştu: {str(e)}', 'error')
            return redirect(url_for('gallery.admin_gallery_create'))

    return render_template('admin/gallery/create.html')

@gallery_bp.route('/admin/gallery/bulk-upload', methods=['GET', 'POST'])
@login_required
def admin_gallery_bulk_upload():
    if request.method == 'POST':
        try:
            category = request.form.get('category')
            
            # Dosyaları al
            if 'images' not in request.files:
                flash('Hiçbir dosya seçilmedi', 'error')
                return redirect(request.url)
            
            files = request.files.getlist('images')
            
            if not files or files[0].filename == '':
                flash('Hiçbir dosya seçilmedi', 'error')
                return redirect(request.url)
            
            # Upload klasörünü oluştur
            upload_path = os.path.join(current_app.root_path, UPLOAD_FOLDER)
            os.makedirs(upload_path, exist_ok=True)
            
            # En düşük order değerini bul (en üstte görünmesi için)
            min_order = db.session.query(db.func.min(Gallery.order)).scalar() or 0
            current_order = min_order - len(files)
            
            successful_uploads = 0
            
            for file in files:
                if file and allowed_file(file.filename):
                    try:
                        # Dosya adını oluştur
                        filename = secure_filename(file.filename)
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"{timestamp}_{filename}"
                        
                        # Tam yolu belirle ve kaydet
                        file_path = os.path.join(upload_path, filename)
                        file.save(file_path)
                        
                        # Dosya izinlerini ayarla
                        os.chmod(file_path, 0o644)
                        
                        # Galeri öğesi oluştur
                        gallery = Gallery(
                            title=filename.split('_')[-1].split('.')[0],  # Dosya adından başlık oluştur
                            description="",
                            category=category,
                            image=filename,
                            order=current_order  # Sıralama düzeni
                        )
                        
                        db.session.add(gallery)
                        current_order += 1  # Bir sonraki resim için sıralamayı artır
                        successful_uploads += 1
                        
                    except Exception as e:
                        print(f"Dosya yükleme hatası: {str(e)}")
                        continue
            
            if successful_uploads > 0:
                db.session.commit()
                flash(f'{successful_uploads} resim başarıyla yüklendi.', 'success')
            else:
                flash('Hiçbir resim yüklenemedi.', 'error')
                
            return redirect(url_for('gallery.admin_gallery_list'))
                
        except Exception as e:
            print("Error:", str(e))
            flash(f'Bir hata oluştu: {str(e)}', 'error')
            return redirect(url_for('gallery.admin_gallery_bulk_upload'))
    
    return render_template('admin/gallery/bulk_upload.html')

@gallery_bp.route('/admin/gallery/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def admin_gallery_edit(id):
    gallery = Gallery.query.get_or_404(id)

    if request.method == 'POST':
        gallery.title = request.form.get('title')
        gallery.description = request.form.get('description')
        gallery.category = request.form.get('category')
        gallery.order = int(request.form.get('order', 0))
        gallery.active = 'active' in request.form

        image = request.files.get('image')
        if image and allowed_file(image.filename):
            # Eski resmi sil
            if gallery.image:
                old_image_path = os.path.join(current_app.root_path, UPLOAD_FOLDER, gallery.image)
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)

            filename = secure_filename(image.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"

            # Tam yolu belirle
            file_path = os.path.join(current_app.root_path, UPLOAD_FOLDER, filename)
            image.save(file_path)
            gallery.image = filename

        db.session.commit()
        flash('Galeri öğesi başarıyla güncellendi.', 'success')
        return redirect(url_for('gallery.admin_gallery_list'))

    return render_template('admin/gallery/edit.html', gallery=gallery)

@gallery_bp.route('/admin/gallery/delete/<int:id>', methods=['POST'])
@login_required
def admin_gallery_delete(id):
    gallery = Gallery.query.get_or_404(id)

    if gallery.image:
        image_path = os.path.join(current_app.root_path, UPLOAD_FOLDER, gallery.image)
        if os.path.exists(image_path):
            os.remove(image_path)

    db.session.delete(gallery)
    db.session.commit()
    flash('Galeri öğesi başarıyla silindi.', 'success')
    return redirect(url_for('gallery.admin_gallery_list'))

@gallery_bp.route('/admin/gallery/update-order', methods=['POST'])
@login_required
def admin_gallery_update_order():
    try:
        data = request.get_json()
        gallery_id = data.get('id')
        new_order = data.get('order')

        gallery = Gallery.query.get_or_404(gallery_id)
        gallery.order = int(new_order)

        db.session.commit()
        return jsonify({'success': True, 'message': 'Sıralama güncellendi'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 400

@gallery_bp.route('/gallery')
def gallery_page():
    # Order değeri küçükten büyüğe sıralanır (en küçük order değeri en üstte görünür)
    galleries = Gallery.query.filter_by(active=True).order_by(Gallery.order.asc()).all()
    gallery_data = [{
        'image': url_for('static', filename=f'uploads/gallery/{g.image}'),
        'title': g.title,
        'description': g.description or '',
        'category': g.category
    } for g in galleries]
    return render_template('gallery/index.html', galleries=galleries, gallery_data=gallery_data)