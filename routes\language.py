from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, g
from models.language import Language, db
from flask_login import login_required
from datetime import datetime
from sqlalchemy.exc import IntegrityError

language_bp = Blueprint('language', __name__)

@language_bp.route('/admin/languages')
@login_required
def language_list():
    try:
        languages = Language.query.order_by(Language.page, Language.key).all()
        return render_template('admin/language/list.html', languages=languages)
    except Exception as e:
        flash(f'Dil kayıtları yüklenirken bir hata oluştu: {str(e)}', 'error')
        return render_template('admin/language/list.html', languages=[])

@language_bp.route('/admin/languages/add', methods=['GET', 'POST'])
@login_required
def language_add():
    if request.method == 'POST':
        try:
            # Önce aynı key'e sahip kayıt var mı kontrol et
            existing = Language.query.filter_by(key=request.form.get('key')).first()
            if existing:
                flash('Bu anahtar zaten kullanılıyor. Lütfen farklı bir anahtar kullanın.', 'error')
                return render_template('admin/language/form.html', 
                                     language=Language(**request.form))

            language = Language(
                key=request.form.get('key'),
                tr=request.form.get('tr'),
                en=request.form.get('en'),
                page=request.form.get('page')
            )
            db.session.add(language)
            db.session.commit()
            flash('Çeviri başarıyla eklendi.', 'success')
            return redirect(url_for('language.language_list'))
        except IntegrityError:
            db.session.rollback()
            flash('Bu anahtar zaten kullanılıyor. Lütfen farklı bir anahtar kullanın.', 'error')
            return render_template('admin/language/form.html', 
                                 language=Language(**request.form))
        except Exception as e:
            db.session.rollback()
            flash(f'Hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/language/form.html')

@language_bp.route('/admin/languages/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def language_edit(id):
    language = Language.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # Eğer key değiştirilmişse ve yeni key başka bir kayıtta kullanılıyorsa
            if language.key != request.form.get('key'):
                existing = Language.query.filter_by(key=request.form.get('key')).first()
                if existing and existing.id != id:
                    flash('Bu anahtar zaten kullanılıyor. Lütfen farklı bir anahtar kullanın.', 'error')
                    return render_template('admin/language/form.html', language=language)

            language.key = request.form.get('key')
            language.tr = request.form.get('tr')
            language.en = request.form.get('en')
            language.page = request.form.get('page')
            language.updated_at = datetime.utcnow()
            
            db.session.commit()
            flash('Çeviri başarıyla güncellendi.', 'success')
            return redirect(url_for('language.language_list'))
        except IntegrityError:
            db.session.rollback()
            flash('Bu anahtar zaten kullanılıyor. Lütfen farklı bir anahtar kullanın.', 'error')
            return render_template('admin/language/form.html', language=language)
        except Exception as e:
            db.session.rollback()
            flash(f'Hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/language/form.html', language=language)

@language_bp.route('/admin/languages/delete/<int:id>', methods=['POST'])
@login_required
def language_delete(id):
    language = Language.query.get_or_404(id)
    try:
        db.session.delete(language)
        db.session.commit()
        flash('Çeviri başarıyla silindi.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('language.language_list'))

@language_bp.route('/api/translations/<lang>')
def get_translations(lang):
    try:
        translations = Language.query.all()
        translation_dict = {item.key: getattr(item, lang) for item in translations}
        return jsonify(translation_dict)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@language_bp.route('/change-language/<lang>')
def change_language(lang):
    if lang in ['tr', 'en']:
        session['language'] = lang
        g.language = lang
    return redirect(request.referrer or url_for('main.main'))