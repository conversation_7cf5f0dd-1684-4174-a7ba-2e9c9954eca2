from urllib.parse import urlparse, parse_qs
from models.keyword_analytics import KeywordAnalytics
from models import db
import re
import requests
from user_agents import parse
from datetime import datetime

class KeywordTracker:
    def __init__(self, wsgi_app):
        self.wsgi_app = wsgi_app
        # Daha fazla arama motoru ekleyelim
        self.search_engines = {
            'google': {
                'domains': ['google.com', 'google.com.tr', 'google.co.uk', 'google.de'],
                'params': ['q', 'query']
            },
            'yandex': {
                'domains': ['yandex.com', 'yandex.com.tr', 'yandex.ru'],
                'params': ['text', 'query']
            },
            'bing': {
                'domains': ['bing.com', 'bing.co.uk'],
                'params': ['q', 'query']
            },
            'duckduckgo': {
                'domains': ['duckduckgo.com'],
                'params': ['q']
            },
            'yahoo': {
                'domains': ['yahoo.com', 'yahoo.co.jp'],
                'params': ['p', 'q']
            },
            'baidu': {
                'domains': ['baidu.com'],
                'params': ['wd', 'word']
            }
        }
        
        # Yasaklı kelimeler listesi
        self.blocked_keywords = [
            'porn', 'hack', 'crack', 'warez',
            # ... diğer yasaklı kelimeler
        ]

    def __call__(self, environ, start_response):
        try:
            # Referrer kontrolü
            referrer = environ.get('HTTP_REFERER')
            if referrer:
                parsed_url = urlparse(referrer)
                domain = parsed_url.netloc.lower().replace('www.', '')
                query_params = parse_qs(parsed_url.query)
                path_info = environ.get('PATH_INFO', '')
                
                # Arama motoru kontrolü
                for engine, data in self.search_engines.items():
                    engine_domains = data['domains']
                    query_param_keys = data['params']
                    
                    for eng_domain in engine_domains:
                        if eng_domain in domain:
                            # Bu bir arama motoru referrer'ı
                            for param in query_param_keys:
                                if param in query_params and query_params[param]:
                                    query = query_params[param][0]
                                    
                                    # Boş değilse
                                    if query and len(query) > 2:
                                        # Temiz anahtar kelimeleri al
                                        keywords = self.clean_keywords(query)
                                        
                                        if keywords:
                                            # Ziyaretçi bilgilerini al
                                            visitor_ip = environ.get('HTTP_X_FORWARDED_FOR', environ.get('REMOTE_ADDR', '127.0.0.1'))
                                            if visitor_ip and ',' in visitor_ip:
                                                visitor_ip = visitor_ip.split(',')[0].strip()
                                                
                                            user_agent = environ.get('HTTP_USER_AGENT', '')
                                            page_url = environ.get('PATH_INFO', '')
                                            
                                            # Her temiz anahtar kelime için kayıt
                                            for keyword in keywords:
                                                print(f"Anahtar kelime izleniyor: {keyword} ({engine}) - Sayfa: {page_url}")
                                                self.track_keyword(
                                                    keyword=keyword,
                                                    source=engine,
                                                    page_url=page_url,
                                                    visitor_ip=visitor_ip,
                                                    user_agent=user_agent,
                                                    referrer=referrer
                                                )
                                            break
            
        except Exception as e:
            print(f"Keyword tracking error in middleware: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
        # İsteği normal şekilde işle
        return self.wsgi_app(environ, start_response)

    def clean_keywords(self, query):
        """Gelişmiş kelime temizleme ve analiz"""
        # Küçük harfe çevir
        query = query.lower()
        
        # Özel karakterleri temizle
        query = re.sub(r'[^\w\s\-]', ' ', query)
        
        # Stop words (gereksiz kelimeler) listesi
        stop_words = {'ve', 'veya', 'ile', 'the', 'a', 'an', 'and', 'or'}
        
        # Kelimeleri ayır
        words = query.split()
        
        # Stop words ve yasaklı kelimeleri çıkar, minimum uzunluk kontrolü yap
        cleaned_words = [
            word for word in words 
            if len(word) > 2  # minimum 3 karakter
            and word not in stop_words
            and word not in self.blocked_keywords
        ]
        
        return cleaned_words

    def track_keyword(self, keyword, source, page_url, visitor_ip, user_agent, referrer):
        try:
            with self.wsgi_app.app_context():
                # Coğrafi konum tespiti
                geo_info = self.get_geo_info(visitor_ip)
                
                # Cihaz/tarayıcı bilgisi analizi
                device_info = self.parse_user_agent(user_agent)
                
                analytics = KeywordAnalytics.query.filter_by(
                    keyword=keyword,
                    source=source,
                    visitor_ip=visitor_ip
                ).first()

                if analytics:
                    analytics.visit_count += 1
                    analytics.page_url = page_url
                    analytics.user_agent = user_agent
                    analytics.referrer = referrer
                    analytics.last_visit = datetime.utcnow()
                    
                    # Ziyaret süresi analizi
                    if hasattr(analytics, 'last_visit'):
                        time_diff = datetime.utcnow() - analytics.last_visit
                        analytics.avg_visit_duration = (
                            (analytics.avg_visit_duration * (analytics.visit_count - 1) + 
                             time_diff.total_seconds()) / analytics.visit_count
                        )
                else:
                    analytics = KeywordAnalytics(
                        keyword=keyword,
                        source=source,
                        page_url=page_url,
                        visitor_ip=visitor_ip,
                        user_agent=user_agent,
                        referrer=referrer,
                        country=geo_info.get('country'),
                        city=geo_info.get('city'),
                        device_type=device_info.get('device_type'),
                        browser=device_info.get('browser'),
                        os=device_info.get('os')
                    )
                    db.session.add(analytics)

                db.session.commit()

        except Exception as e:
            print(f"Keyword tracking error: {str(e)}")
            db.session.rollback()

    def get_geo_info(self, ip):
        """IP adresinden coğrafi konum bilgisi al"""
        try:
            # Ücretsiz IP-API servisi kullanımı
            response = requests.get(f'http://ip-api.com/json/{ip}')
            if response.status_code == 200:
                data = response.json()
                return {
                    'country': data.get('country'),
                    'city': data.get('city'),
                    'region': data.get('regionName'),
                    'lat': data.get('lat'),
                    'lon': data.get('lon')
                }
        except:
            pass
        return {}

    def parse_user_agent(self, user_agent):
        """User-Agent bilgisini analiz et"""
        try:
            # user-agents kütüphanesi kullanımı
            ua = parse(user_agent)
            return {
                'device_type': ua.device.family,
                'browser': ua.browser.family,
                'os': ua.os.family,
                'is_mobile': ua.is_mobile,
                'is_tablet': ua.is_tablet,
                'is_pc': ua.is_pc
            }
        except:
            return {} 