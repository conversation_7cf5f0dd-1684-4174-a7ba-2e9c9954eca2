from datetime import datetime, date, timedelta
from sqlalchemy import func
from models import db
from utils.helpers import get_turkey_time

class IPSettings(db.Model):
    __tablename__ = 'ip_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    ip_range = db.Column(db.String(50), nullable=False)  # CIDR formatında IP aralığı
    description = db.Column(db.String(200), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=lambda: get_turkey_time().replace(tzinfo=None))
    
    def __repr__(self):
        return f'<IPSettings {self.ip_range}>'

class VisitorStats(db.Model):
    __tablename__ = 'visitor_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, default=date.today, nullable=False)
    unique_visitors = db.Column(db.Integer, default=0)
    page_views = db.Column(db.Integer, default=0)
    
    @classmethod
    def get_or_create_today(cls):
        today = date.today()
        stats = cls.query.filter_by(date=today).first()
        if not stats:
            stats = cls(date=today)
            db.session.add(stats)
            db.session.commit()
        return stats
    
    def increment_visitors(self):
        """Ziyaretçi sayısını artır (commit yapmaz)"""
        self.unique_visitors += 1
        
    def increment_page_views(self):
        """Sayfa görüntüleme sayısını artır (commit yapmaz)"""
        self.page_views += 1

class CountryVisitorStats(db.Model):
    __tablename__ = 'country_visitor_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, default=date.today, nullable=False)
    country_code = db.Column(db.String(2), nullable=False)
    country_name = db.Column(db.String(100), nullable=False)
    visitors = db.Column(db.Integer, default=0)
    
    @classmethod
    def get_or_create(cls, country_code, country_name):
        today = date.today()
        stats = cls.query.filter_by(date=today, country_code=country_code).first()
        if not stats:
            stats = cls(date=today, country_code=country_code, country_name=country_name)
            db.session.add(stats)
            db.session.commit()
        return stats
    
    def increment_visitors(self):
        """Ziyaretçi sayısını artır (commit yapmaz)"""
        self.visitors += 1
        
    @classmethod
    def get_country_stats(cls, days=30):
        """Son X gündeki ülke istatistiklerini döndür"""
        from sqlalchemy import func
        
        cutoff_date = date.today() - timedelta(days=days)
        
        # Ülke bazlı toplam ziyaretçi sayısı
        country_stats = db.session.query(
            cls.country_code,
            cls.country_name,
            func.sum(cls.visitors).label('total_visitors')
        ).filter(
            cls.date >= cutoff_date
        ).group_by(
            cls.country_code, cls.country_name
        ).order_by(
            func.sum(cls.visitors).desc()
        ).all()
        
        return country_stats 