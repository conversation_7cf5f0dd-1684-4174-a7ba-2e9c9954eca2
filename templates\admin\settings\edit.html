{% extends "admin/base.html" %}

{% block admin_content %}
<div class="p-6 bg-gray-50 min-h-screen">
    <h1 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-4">Site Ayarları</h1>

    <div class="bg-white rounded-lg shadow-lg p-6 transition-shadow duration-300 hover:shadow-xl">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700 border border-red-200{% else %}bg-green-100 text-green-700 border border-green-200{% endif %} shadow-sm">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" enctype="multipart/form-data">
            <!-- Gene<PERSON> -->
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Gene<PERSON></h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Site Başlığı -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Site Başlığı</label>
                        <input type="text" 
                               name="site_title" 
                               value="{{ settings.site_title or '' }}"
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150"
                               placeholder="Zeppelin Hotel">
                        <p class="text-sm text-gray-500">Sitenizin başlığı tarayıcı sekmesinde görünecektir.</p>
                    </div>

                    <!-- Site Açıklaması -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Site Açıklaması
                        </label>
                        <input type="text" name="site_description" 
                               value="{{ settings.site_description or '' }}"
                               class="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200">
                        <p class="text-sm text-gray-500">SEO için meta açıklaması olarak kullanılacaktır.</p>
                    </div>

                    <!-- Ana Sayfa İçerik Sayfası -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Ana Sayfa İçerik Sayfası (Slug)
                        </label>
                        <input type="text" name="main_page_content" 
                               value="{{ settings.main_page_content or '' }}"
                               placeholder="Örnek: about-us, rooms, blog"
                               class="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200">
                        <p class="text-sm text-gray-500">Ana sayfada görüntülenecek içerik sayfasının slug değeri.</p>
                    </div>
                </div>
            </div>

            <!-- Logo Ayarları -->
            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 text-gray-700 flex items-center">
                    <i class="fas fa-image mr-2"></i>
                    Logo Ayarları
                </h2>

                <div class="space-y-6">
                    <!-- Text Logo Switch -->
                    <div class="flex items-center justify-between max-w-xs">
                        <span class="text-sm font-medium text-gray-700">Text Logo Kullan</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="use_text_logo" class="sr-only peer" 
                                   {% if settings.use_text_logo == 'True' %}checked{% endif %}>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>

                    <!-- Text Logo Input -->
                    <div id="textLogoSection" class="{% if settings.use_text_logo != 'True' %}hidden{% endif %} max-w-xs">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Site Text Logo
                        </label>
                        <input type="text" name="site_text_logo" value="{{ settings.site_text_logo }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>

                    <!-- Logo Upload Bölümü -->
                    <div id="imageLogoSection" class="{% if settings.use_text_logo == 'True' %}hidden{% endif %}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Ana Logo -->
                            <div class="space-y-4">
                                <label class="block text-sm font-medium text-gray-700">Ana Logo (Büyük)</label>
                                <div class="flex flex-col space-y-4">
                                    {% if settings.site_logo %}
                                        <div class="relative w-full h-40 border rounded-lg overflow-hidden bg-gray-50">
                                            <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo) }}" 
                                                 alt="Mevcut Logo" 
                                                 class="w-full h-full object-contain p-4">
                                            <button type="button" onclick="removeLogo('main')"
                                                    class="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                    <input type="file" name="site_logo" accept="image/*"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100">
                                    <p class="text-sm text-gray-500">PNG, JPG, GIF - max 2MB</p>
                                </div>
                            </div>

                            <!-- Küçük Logo -->
                            <div class="space-y-4">
                                <label class="block text-sm font-medium text-gray-700">Scroll Logo (Küçük)</label>
                                <div class="flex flex-col space-y-4">
                                    {% if settings.site_logo_small %}
                                        <div class="relative w-full h-40 border rounded-lg overflow-hidden bg-gray-50">
                                            <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo_small) }}" 
                                                 alt="Mevcut Küçük Logo" 
                                                 class="w-full h-full object-contain p-4">
                                            <button type="button" onclick="removeLogo('small')"
                                                    class="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                    <input type="file" name="site_logo_small" accept="image/*"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100">
                                    <p class="text-sm text-gray-500">PNG, JPG, GIF - max 2MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sosyal Medya Ayarları -->
            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 text-gray-700 flex items-center">
                    <i class="fas fa-share-alt mr-2"></i>
                    Sosyal Medya Ayarları
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- WhatsApp -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="block text-sm font-medium text-gray-700">WhatsApp</label>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="whatsapp_active" class="sr-only peer" 
                                       {% if settings.whatsapp_active == 'True' %}checked{% endif %}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                        </div>
                        <input type="text" name="whatsapp_link" 
                               value="{{ settings.whatsapp_link or '' }}"
                               placeholder="https://wa.me/905xxxxxxxxx"
                               class="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <p class="text-sm text-gray-500">WhatsApp numaranızı uluslararası formatta girin (Örn: 905xxxxxxxxx)</p>
                    </div>

                    <!-- Instagram -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="block text-sm font-medium text-gray-700">Instagram</label>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="instagram_active" class="sr-only peer" 
                                       {% if settings.instagram_active == 'True' %}checked{% endif %}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
                            </label>
                        </div>
                        <input type="text" name="instagram_link" 
                               value="{{ settings.instagram_link or '' }}"
                               placeholder="https://instagram.com/kullaniciadi"
                               class="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        <p class="text-sm text-gray-500">Instagram profil linkinizi girin</p>
                    </div>
                </div>
            </div>

            <!-- Google Analytics Hızlı Erişim -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100 mt-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fab fa-google text-blue-600 mr-2"></i>
                            Google Analytics & Tag Manager
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Analytics ayarlarını ayrı sayfadan yönetin</p>
                    </div>
                    <a href="{{ url_for('integration.google_analytics') }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Analytics Ayarları
                    </a>
                </div>
            </div>

            <!-- Kaydet Butonu -->
            <div class="flex justify-end">
                <button type="submit" 
                        class="px-6 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Kaydet
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function removeLogo(type) {
    if (confirm('Logo silinecek. Emin misiniz?')) {
        fetch(`/admin/settings/remove-logo/${type}`, {
            method: 'POST',
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            }
        });
    }
}

// Text/Image logo toggle
document.querySelector('input[name="use_text_logo"]').addEventListener('change', function() {
    const textSection = document.getElementById('textLogoSection');
    const imageSection = document.getElementById('imageLogoSection');
    
    if (this.checked) {
        textSection.classList.remove('hidden');
        imageSection.classList.add('hidden');
    } else {
        textSection.classList.add('hidden');
        imageSection.classList.remove('hidden');
    }
});

function updateFontPreview(selectElement, previewId) {
    const preview = document.getElementById(previewId);
    const selectedFont = selectElement.value;
    preview.style.fontFamily = `'${selectedFont}', sans-serif`;
}
</script>
{% endblock %} 