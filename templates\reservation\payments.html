{% extends "base.html" %}

{% block title %}{{ _('<PERSON><PERSON><PERSON><PERSON>') }}{% endblock title %}

{% block head %}
<style>
    /* <PERSON><PERSON> stiller */
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 16px;
    }
    
    .page-title {
        font-size: 2rem;
        color: #333;
        margin-bottom: 1.5rem;
        font-weight: 600;
        text-align: center;
    }
    
    .page-description {
        text-align: center;
        max-width: 600px;
        margin: 0 auto 2rem;
        color: #666;
    }
    
    /* Filtre ve Arama */
    .filters {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .filter-form {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    @media (min-width: 768px) {
        .filter-form {
            grid-template-columns: 1fr 1fr auto;
        }
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-label {
        font-size: 0.8rem;
        font-weight: 500;
        color: #555;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        padding: 0.75rem 1rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.9rem;
        background-color: #fff;
    }
    
    .form-input:focus {
        border-color: #C6A87D;
        box-shadow: 0 0 0 2px rgba(198, 168, 125, 0.2);
        outline: none;
    }
    
    .search-button {
        align-self: flex-end;
        height: 42px;
        padding: 0 1.5rem;
        background-color: #C6A87D;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .search-button:hover {
        background-color: #b39669;
    }
    
    /* Tablo Stili */
    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .payments-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .payments-table th,
    .payments-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    
    .payments-table th {
        background-color: #f9f9f9;
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }
    
    .payments-table tbody tr:hover {
        background-color: #f9f7f4;
    }
    
    .payments-table td {
        font-size: 0.9rem;
        color: #555;
    }
    
    /* Durum badgeleri */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-success {
        background-color: #d1fae5;
        color: #064e3b;
    }
    
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-failed {
        background-color: #fee2e2;
        color: #b91c1c;
    }
    
    .status-refunded {
        background-color: #e0e7ff;
        color: #3730a3;
    }
    
    /* Butonlar */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn-primary {
        background-color: #C6A87D;
        color: white;
        border: none;
    }
    
    .btn-primary:hover {
        background-color: #b39669;
    }
    
    .btn-outline {
        background-color: transparent;
        border: 1px solid #C6A87D;
        color: #C6A87D;
    }
    
    .btn-outline:hover {
        background-color: #f9f7f4;
    }
    
    .btn-icon {
        width: 16px;
        height: 16px;
        margin-right: 0.4rem;
    }
    
    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        gap: 0.5rem;
    }
    
    .page-item {
        display: inline-flex;
    }
    
    .page-link {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: 1px solid #ddd;
        color: #666;
        text-decoration: none;
        transition: all 0.2s;
    }
    
    .page-link:hover {
        background-color: #f9f9f9;
        border-color: #ccc;
    }
    
    .page-item.active .page-link {
        background-color: #C6A87D;
        border-color: #C6A87D;
        color: white;
    }
    
    /* Boş durum */
    .empty-state {
        text-align: center;
        padding: 3rem;
    }
    
    .empty-state-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        color: #C6A87D;
        opacity: 0.7;
    }
    
    .empty-state-text {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-gray-50 py-12 min-h-screen">
    <div class="container">
        <h1 class="page-title">{{ _('Ödemeler') }}</h1>
        <p class="page-description">{{ _('Rezervasyon ödemeleri ve işlem geçmişi') }}</p>
        
        <!-- Filtre ve Arama -->
        <div class="filters">
            <form class="filter-form" id="filterForm">
                <div class="form-group">
                    <label class="form-label" for="payment_status">{{ _('Ödeme Durumu') }}</label>
                    <select class="form-input" id="payment_status" name="status">
                        <option value="">{{ _('Hepsi') }}</option>
                        <option value="paid" {% if status == 'paid' %}selected{% endif %}>{{ _('Ödenmiş') }}</option>
                        <option value="processing" {% if status == 'processing' %}selected{% endif %}>{{ _('İşleniyor') }}</option>
                        <option value="failed" {% if status == 'failed' %}selected{% endif %}>{{ _('Başarısız') }}</option>
                        <option value="refunded" {% if status == 'refunded' %}selected{% endif %}>{{ _('İade Edildi') }}</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="payment_code">{{ _('Rezervasyon/İşlem Kodu') }}</label>
                    <input type="text" class="form-input" id="payment_code" name="code" placeholder="{{ _('Kod ile ara...') }}" value="{{ code or '' }}">
                </div>
                
                <button type="submit" class="search-button">{{ _('Filtrele') }}</button>
            </form>
        </div>
        
        <!-- Ödemeler Tablosu -->
        <div class="table-container">
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>{{ _('Tarih') }}</th>
                        <th>{{ _('İşlem No') }}</th>
                        <th>{{ _('Rezervasyon Kodu') }}</th>
                        <th>{{ _('Misafir') }}</th>
                        <th>{{ _('Tutar') }}</th>
                        <th>{{ _('Ödeme Yöntemi') }}</th>
                        <th>{{ _('Durum') }}</th>
                        <th>{{ _('İşlemler') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% if payments %}
                        {% for payment in payments %}
                        <tr>
                            <td>{{ payment.created_at.strftime('%d.%m.%Y %H:%M') }}</td>
                            <td>{{ payment.transaction_id }}</td>
                            <td>
                                {% if payment.reservation %}
                                    {{ payment.reservation.reservation_code }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.reservation %}
                                    {{ payment.reservation.name }} {{ payment.reservation.surname }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.currency == 'TRY' %}
                                    {{ payment.amount|round|int }} ₺
                                {% elif payment.currency == 'USD' %}
                                    ${{ payment.amount|round|int }}
                                {% elif payment.currency == 'EUR' %}
                                    €{{ payment.amount|round|int }}
                                {% else %}
                                    {{ payment.amount|round|int }} {{ payment.currency }}
                                {% endif %}
                            </td>
                            <td>{{ payment.payment_method.name if payment.payment_method else '-' }}</td>
                            <td>
                                <span class="status-badge 
                                    {% if payment.status == 'SUCCESS' %}status-success
                                    {% elif payment.status == 'PENDING' %}status-pending
                                    {% elif payment.status == 'FAILED' %}status-failed
                                    {% elif payment.status == 'REFUNDED' %}status-refunded{% endif %}">
                                    {% if payment.status == 'SUCCESS' %}{{ _('Başarılı') }}
                                    {% elif payment.status == 'PENDING' %}{{ _('İşleniyor') }}
                                    {% elif payment.status == 'FAILED' %}{{ _('Başarısız') }}
                                    {% elif payment.status == 'REFUNDED' %}{{ _('İade Edildi') }}
                                    {% else %}{{ payment.status }}{% endif %}
                                </span>
                            </td>
                            <td>
                                {% if payment.reservation %}
                                <a href="{{ url_for('admin.reservation_detail', id=payment.reservation.id) }}" class="btn btn-outline">
                                    <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    {{ _('Detay') }}
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="8">
                                <div class="empty-state">
                                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    <div class="empty-state-text">{{ _('Henüz bir ödeme işlemi bulunmamaktadır.') }}</div>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if payments and pagination.pages > 1 %}
        <div class="pagination">
            {% if pagination.has_prev %}
            <div class="page-item">
                <a class="page-link" href="{{ url_for('reservation.payment_list', page=pagination.prev_num, status=status, code=code) }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
            </div>
            {% endif %}
            
            {% for page in pagination.iter_pages() %}
                {% if page %}
                <div class="page-item {% if page == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('reservation.payment_list', page=page, status=status, code=code) }}">{{ page }}</a>
                </div>
                {% else %}
                <div class="page-item">
                    <span class="page-link">...</span>
                </div>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <div class="page-item">
                <a class="page-link" href="{{ url_for('reservation.payment_list', page=pagination.next_num, status=status, code=code) }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %} 