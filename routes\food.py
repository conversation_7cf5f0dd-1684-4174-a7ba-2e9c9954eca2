from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, abort
from flask_login import login_required, current_user
from models import db
from models.food import FoodCategory, Food, Order, OrderItem
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from middleware.hotel_network import require_hotel_network
from models.ip_settings import IPSettings

# İki ayrı blueprint oluştur
food_bp = Blueprint('food', __name__)
admin_food_bp = Blueprint('admin_food', __name__)

# Admin route'ları admin_food_bp'ye taşı
@admin_food_bp.route('/categories')
@login_required
def category_list():
    if not current_user.is_admin:
        return redirect(url_for('index'))
    categories = FoodCategory.query.order_by(FoodCategory.order).all()
    return render_template('admin/food/category_list.html', categories=categories)

@admin_food_bp.route('/foods')
@login_required
def food_list():
    if not current_user.is_admin:
        return redirect(url_for('index'))
    foods = Food.query.join(FoodCategory).all()
    return render_template('admin/food/food_list.html', foods=foods)

@admin_food_bp.route('/orders')
@login_required
def order_list():
    if not current_user.is_admin:
        return redirect(url_for('index'))
    orders = Order.query.order_by(Order.created_at.desc()).all()
    return render_template('admin/food/order_list.html', orders=orders)

@admin_food_bp.route('/categories/create', methods=['GET', 'POST'])
@login_required
def category_create():
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    if request.method == 'POST':
        category = FoodCategory(
            name_tr=request.form.get('name_tr'),
            name_en=request.form.get('name_en'),
            description_tr=request.form.get('description_tr'),
            description_en=request.form.get('description_en'),
            order=request.form.get('order', 0),
            active=bool(request.form.get('active'))
        )
        db.session.add(category)
        db.session.commit()
        flash('Kategori başarıyla oluşturuldu.', 'success')
        return redirect(url_for('admin_food.category_list'))
        
    return render_template('admin/food/category_create.html')

@admin_food_bp.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def category_edit(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    category = FoodCategory.query.get_or_404(id)
    
    if request.method == 'POST':
        category.name_tr = request.form.get('name_tr')
        category.name_en = request.form.get('name_en')
        category.description_tr = request.form.get('description_tr')
        category.description_en = request.form.get('description_en')
        category.order = request.form.get('order', 0)
        category.active = bool(request.form.get('active'))
        db.session.commit()
        flash('Kategori başarıyla güncellendi.', 'success')
        return redirect(url_for('admin_food.category_list'))
        
    return render_template('admin/food/category_edit.html', category=category)

@admin_food_bp.route('/categories/<int:id>/delete', methods=['POST'])
@login_required
def category_delete(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    category = FoodCategory.query.get_or_404(id)
    db.session.delete(category)
    db.session.commit()
    flash('Kategori başarıyla silindi.', 'success')
    return redirect(url_for('admin_food.category_list'))

def ensure_upload_folder():
    # Upload klasörünü kontrol et ve oluştur
    upload_folder = 'static/uploads/food'  # Doğrudan forward slash kullan
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder, mode=0o777)
    return upload_folder

@admin_food_bp.route('/foods/create', methods=['GET', 'POST'])
@login_required
def food_create():
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    categories = FoodCategory.query.filter_by(active=True).all()
    
    if request.method == 'POST':
        image = request.files.get('image')
        image_path = None
        
        if image and image.filename:
            try:
                # Upload klasörünü hazırla
                upload_folder = 'static/uploads/food'  # Doğrudan forward slash kullan
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                # Benzersiz dosya adı oluştur
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = secure_filename(f"{timestamp}_{image.filename}")
                
                # URL için kullanılacak path (veritabanına kaydedilecek)
                image_path = f"uploads/food/{filename}"
                
                # Dosya sisteminde kullanılacak path
                save_path = os.path.join('static', image_path).replace('\\', '/')
                
                # Dosyayı kaydet
                image.save(save_path)
                print(f"Resim kaydedildi: {save_path}")
                
            except Exception as e:
                print(f"Resim yükleme hatası: {str(e)}")
                flash('Resim yüklenirken bir hata oluştu.', 'error')
                image_path = None
        
        try:
            food = Food(
                name_tr=request.form.get('name_tr'),
                name_en=request.form.get('name_en'),
                description_tr=request.form.get('description_tr'),
                description_en=request.form.get('description_en'),
                price=float(request.form.get('price', 0)),
                category_id=request.form.get('category_id'),
                active=bool(request.form.get('active')),
                image=image_path
            )
            db.session.add(food)
            db.session.commit()
            flash('Yemek başarıyla eklendi.', 'success')
            return redirect(url_for('admin_food.food_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Hata oluştu: {str(e)}', 'error')
        
    return render_template('admin/food/food_create.html', categories=categories)

@admin_food_bp.route('/foods/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def food_edit(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    food = Food.query.get_or_404(id)
    categories = FoodCategory.query.filter_by(active=True).all()
    
    if request.method == 'POST':
        food.name_tr = request.form.get('name_tr')
        food.name_en = request.form.get('name_en')
        food.description_tr = request.form.get('description_tr')
        food.description_en = request.form.get('description_en')
        food.price = float(request.form.get('price', 0))
        food.category_id = request.form.get('category_id')
        food.active = bool(request.form.get('active'))
        
        image = request.files.get('image')
        if image and image.filename:
            # Eski görseli sil
            if food.image:
                old_image_path = f"static/{food.image}"  # Doğrudan forward slash kullan
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
            
            # Upload klasörünü hazırla
            upload_folder = ensure_upload_folder()
            
            # Benzersiz dosya adı oluştur
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = secure_filename(f"{timestamp}_{image.filename}")
            
            # Dosya yollarını oluştur
            save_path = f"{upload_folder}/{filename}"  # Doğrudan forward slash kullan
            image_path = f"uploads/food/{filename}"  # URL için düzgün yol
            
            # Dosyayı kaydet
            image.save(save_path)
            food.image = image_path
            
        db.session.commit()
        flash('Yemek başarıyla güncellendi.', 'success')
        return redirect(url_for('admin_food.food_list'))
        
    return render_template('admin/food/food_edit.html', food=food, categories=categories)

@admin_food_bp.route('/foods/<int:id>/delete', methods=['POST'])
@login_required
def food_delete(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    food = Food.query.get_or_404(id)
    
    try:
        # Önce bu yemeğe ait sipariş öğelerini bul
        order_items = OrderItem.query.filter_by(food_id=id).all()
        
        # Sipariş öğelerini sil
        for item in order_items:
            db.session.delete(item)
        
        # Görseli sil
        if food.image:
            image_path = f"static/{food.image}"  # Doğrudan forward slash kullan
            if os.path.exists(image_path):
                os.remove(image_path)
        
        # Yemeği sil
        db.session.delete(food)
        db.session.commit()
        
        flash('Yemek başarıyla silindi.', 'success')
    except Exception as e:
        print(f"Silme hatası: {str(e)}")
        db.session.rollback()
        flash('Yemek silinirken bir hata oluştu.', 'error')
    
    return redirect(url_for('admin_food.food_list'))

@admin_food_bp.route('/orders/<int:id>')
@login_required
def order_detail(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    order = Order.query.get_or_404(id)
    return render_template('admin/food/order_detail.html', order=order)

@admin_food_bp.route('/orders/<int:id>/status', methods=['POST'])
@login_required
def order_status(id):
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    order = Order.query.get_or_404(id)
    new_status = request.form.get('status')
    
    if new_status in ['pending', 'preparing', 'delivered', 'cancelled']:
        order.status = new_status
        db.session.commit()
        flash('Sipariş durumu güncellendi.', 'success')
    
    return redirect(url_for('admin_food.order_detail', id=order.id))

# Müşteri route'ları food_bp'de kalsın
@food_bp.route('/')
def index():
    """Food ana sayfası - menu'ya yönlendir"""
    return redirect(url_for('food.menu'))

@food_bp.route('/menu')
def menu():
    categories = FoodCategory.query.filter_by(active=True).order_by(FoodCategory.order).all()
    if not categories:
        abort(404)

    foods = Food.query.filter_by(active=True).all()
    return render_template('food/menu.html', categories=categories, foods=foods)

@food_bp.route('/<path:invalid_path>')
def invalid_food_path(invalid_path):
    abort(404)

@food_bp.route('/cart', methods=['GET', 'POST'])
@require_hotel_network()  # Sadece otel ağından erişilebilir
def cart():
    """Sepet sayfası - Sadece otel ağından"""
    if not session.get('cart'):
        flash('Sepetiniz boş.', 'info')
    return render_template('food/cart.html')

@food_bp.route('/order-success/<int:order_id>')
def order_success(order_id):
    order = Order.query.get_or_404(order_id)
    return render_template('food/order_success.html', order=order)

@food_bp.route('/add-to-cart', methods=['POST'])
@require_hotel_network()  # Sadece otel ağından erişilebilir
def add_to_cart():
    """Sepete ürün ekleme - Sadece otel ağından"""
    try:
        food_id = request.form.get('food_id')
        quantity = int(request.form.get('quantity', 1))
        
        if not session.get('is_hotel_network'):
            return jsonify({
                'success': False,
                'error': 'Bu işlem sadece otel misafirleri tarafından yapılabilir.'
            }), 403

        food = Food.query.get_or_404(food_id)
        cart = session.get('cart', [])
        
        # Sepette aynı ürün var mı kontrol et
        found = False
        for item in cart:
            if str(item['food_id']) == str(food_id):
                item['quantity'] += quantity
                found = True
                break
        
        if not found:
            cart.append({
                'food_id': food_id,
                'name': food.name,
                'price': food.price,
                'quantity': quantity
            })
        
        session['cart'] = cart
        return jsonify({'success': True, 'message': 'Ürün sepete eklendi.'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@food_bp.route('/update-cart', methods=['POST'])
@require_hotel_network()  # Sadece otel ağından erişilebilir
def update_cart():
    """Sepet güncelleme - Sadece otel ağından"""
    try:
        food_id = request.form.get('food_id')
        quantity = int(request.form.get('quantity', 0))
        
        cart = session.get('cart', [])
        
        if quantity <= 0:
            cart = [item for item in cart if item['food_id'] != food_id]
        else:
            for item in cart:
                if item['food_id'] == food_id:
                    item['quantity'] = quantity
                    break
        
        session['cart'] = cart
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@food_bp.route('/place-order', methods=['POST'])
@require_hotel_network()  # Sadece otel ağından erişilebilir
def place_order():
    """Sipariş verme - Sadece otel ağından"""
    try:
        if not session.get('cart'):
            flash('Sepetiniz boş.', 'error')
            return redirect(url_for('food.cart'))

        # Form verilerini al
        customer_name = request.form.get('customer_name', 'Misafir')
        room_number = request.form.get('room_number')
        notes = request.form.get('notes')
        
        if not room_number:
            flash('Oda numarası gereklidir!', 'error')
            return redirect(url_for('food.cart'))
        
        # Toplam tutarı hesapla
        total_amount = sum(item['price'] * item['quantity'] for item in session['cart'])
        
        # Siparişi oluştur
        order = Order(
            customer_name=customer_name,
            room_number=room_number,
            notes=notes,
            total_amount=total_amount,
            status='pending'
        )
        db.session.add(order)
        
        # Sipariş öğelerini ekle
        for item in session['cart']:
            order_item = OrderItem(
                order=order,
                food_id=item['food_id'],
                quantity=item['quantity'],
                price=item['price']
            )
            db.session.add(order_item)
        
        db.session.commit()
        
        # Sepeti temizle
        session.pop('cart', None)
        
        return redirect(url_for('food.order_success', order_id=order.id))
    except Exception as e:
        db.session.rollback()
        print(f"Hata: {str(e)}")
        flash(f'Sipariş oluşturulurken bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('food.cart'))

@food_bp.route('/check-network')
def check_network():
    client_ip = request.remote_addr
    is_allowed = is_hotel_network(client_ip)
    return jsonify({'is_hotel_network': is_allowed}) 