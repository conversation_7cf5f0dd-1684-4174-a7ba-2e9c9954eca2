{% extends 'admin/base.html' %}

{% block breadcrumb %}Toplu Resim Yükleme{% endblock %}
{% block page_title %}Toplu Galeri Yükleme{% endblock %}
{% block page_subtitle %}<PERSON><PERSON><PERSON> birden fazla resim ekleyin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <form method="POST" enctype="multipart/form-data" class="space-y-6">
        <div class="grid grid-cols-1 gap-6">
            <!-- Kategori -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Kategori</label>
                <select name="category" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="hotel">Otel</option>
                    <option value="restaurant">Restoran</option>
                    <option value="spa">SPA</option>
                    <option value="room">Odalar</option>
                    <option value="other">Diğer</option>
                </select>
            </div>

            <!-- Re<PERSON>mler -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Resimler (Birden Fazla Seçebilirsiniz)</label>
                <div class="mt-1 flex items-center">
                    <input type="file" name="images" required accept="image/*" multiple
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" 
                           id="imageInput">
                </div>
                <p class="mt-2 text-sm text-gray-500">PNG, JPG, GIF veya WEBP. Her dosya en fazla 5MB olabilir.</p>
            </div>

            <!-- Resim Önizlemesi -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Seçilen Resimler</label>
                <div id="imagePreview" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-2"></div>
            </div>
        </div>

        <!-- Butonlar -->
        <div class="flex justify-end space-x-3">
            <a href="{{ url_for('gallery.admin_gallery_list') }}"
               class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                İptal
            </a>
            <button type="submit"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Toplu Yükle
            </button>
        </div>
    </form>
</div>

<script>
    document.getElementById('imageInput').addEventListener('change', function(event) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = ''; // Clear previous previews
        
        const files = event.target.files;
        if (files) {
            // Limit display to first 12 images for performance
            const maxPreview = Math.min(files.length, 12);
            
            for (let i = 0; i < maxPreview; i++) {
                const file = files[i];
                if (!file.type.startsWith('image/')) continue;
                
                const div = document.createElement('div');
                div.className = 'relative border rounded-lg overflow-hidden';
                
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'h-32 w-full object-cover';
                img.onload = function() {
                    URL.revokeObjectURL(img.src); // Free memory
                };
                
                div.appendChild(img);
                
                // Add file name
                const nameDiv = document.createElement('div');
                nameDiv.className = 'px-2 py-1 bg-white text-xs truncate';
                nameDiv.textContent = file.name;
                div.appendChild(nameDiv);
                
                preview.appendChild(div);
            }
            
            if (files.length > maxPreview) {
                const moreDiv = document.createElement('div');
                moreDiv.className = 'flex items-center justify-center h-32 border rounded-lg bg-gray-50';
                moreDiv.textContent = `+${files.length - maxPreview} daha`;
                preview.appendChild(moreDiv);
            }
        }
    });
</script>
{% endblock %} 