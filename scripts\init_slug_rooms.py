import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.room import Room, db
from slugify import slugify

def update_room_slugs():
    with app.app_context():
        rooms = Room.query.filter((Room.slug == None) | (Room.slug == '')).all()
        print(f'Güncellenecek oda sayısı: {len(rooms)}')
        for room in rooms:
            base_slug = slugify(room.title_tr)
            slug = base_slug
            counter = 1
            while Room.query.filter(Room.slug == slug, Room.id != room.id).first() is not None:
                slug = f"{base_slug}-{counter}"
                counter += 1
            room.slug = slug
            print(f'Oda: {room.title_tr} -> Slug: {room.slug}')
        db.session.commit()
        print('Tüm eksik sluglar güncellendi.')

if __name__ == '__main__':
    update_room_slugs() 