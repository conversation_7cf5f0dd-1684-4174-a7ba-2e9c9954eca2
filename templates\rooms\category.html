{% extends "base.html" %}

{% block title %}{{ get_lang_text(category, 'name') }} - {{ get_lang_text(settings, 'site_name') }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 pt-32 sm:pt-40 pb-12">
    <!-- <PERSON>şlık ve Navigasyon -->
    <div class="flex flex-col md:flex-row items-center justify-between gap-4 sm:gap-6 mb-8 sm:mb-12">
        <!-- Sol taraf: Geri Dön Butonu -->
        <div class="w-full md:w-1/4 text-center md:text-left">
            <a href="javascript:void(0);"
               class="inline-flex items-center justify-center md:justify-start text-gold hover:opacity-80 transition-colors group">
                <svg class="w-4 h-4 mr-1.5 sm:mr-2 transform group-hover:-translate-x-1 transition-transform" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                <span class="font-great-vibes text-2xl sm:text-3xl md:text-4xl" data-translate="back_to_rooms">Geri</span>
            </a>
        </div>

        <!-- Orta: Kategori Başlığı -->
        <div class="text-center flex-1 order-first md:order-none">
            <span class="text-gold font-great-vibes text-3xl sm:text-4xl md:text-5xl block">{{ get_lang_text(category, 'name') }}</span>
            <div class="flex items-center justify-center mt-2 sm:mt-4">
                <div class="h-[2px] w-6 sm:w-8 md:w-12 bg-gold"></div>
                <div class="mx-2 sm:mx-3 md:mx-4 text-gold text-lg sm:text-xl md:text-2xl">⚜</div>
                <div class="h-[2px] w-6 sm:w-8 md:w-12 bg-gold"></div>
            </div>
            {% if category.description_tr %}
            <p class="mt-2 sm:mt-4 text-gray-600 max-w-xl mx-auto text-xs sm:text-sm md:text-base px-4">
                {{ get_lang_text(category, 'description') }}
            </p>
            {% endif %}
        </div>

        <!-- Sağ taraf: Tüm Odalar Linki -->
        <div class="w-full md:w-1/4 text-center md:text-right">
            <a href="{{ url_for('rooms.room_index') }}#rooms" 
               class="inline-flex items-center justify-center md:justify-end text-gold hover:opacity-80 transition-colors group">
                <span class="font-great-vibes text-2xl sm:text-3xl md:text-4xl" data-translate="all_rooms">Tüm Odalar</span>
                <svg class="w-4 h-4 ml-1.5 sm:ml-2 transform group-hover:translate-x-1 transition-transform" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                </svg>
            </a>
        </div>
    </div>

    <!-- Oda Listesi -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
        {% for room in rooms.items %}
        <div class="bg-black/90 rounded-xl sm:rounded-2xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/20 transition-all duration-500">
            {% if room.images_list %}
            {% set first_image = room.images_list[0] %}
            <div class="relative overflow-hidden">
                <img src="{{ url_for('static', filename='uploads/rooms/' + first_image) }}"
                     alt="{{ get_lang_text(room, 'title') }}"
                     class="w-full h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                
                <!-- Overlay Gradient -->
                <div class="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/90 
                            group-hover:via-black/30 group-hover:to-black/95 transition-all duration-500"></div>

                <!-- Oda Bilgileri -->
                <div class="absolute bottom-0 left-0 right-0 p-4 sm:p-6 lg:p-8">
                    <div class="space-y-3 sm:space-y-4">
                        <h3 class="text-2xl sm:text-3xl font-light text-white text-center">{{ get_lang_text(room, 'title') }}</h3>
                        
                        <!-- Detay Butonu -->
                        <div class="flex justify-center pt-3 sm:pt-4 border-t border-white/10">
                            <a href="{{ url_for('rooms.room_detail', slug=room.slug) }}" 
                               class="inline-flex items-center text-white/80 hover:text-gold transition-colors group">
                                <span class="text-base sm:text-lg tracking-wider font-light" data-translate="view_details">DETAYLI BİLGİ</span>
                                <svg class="w-4 h-4 sm:w-5 sm:h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        {% else %}
        <div class="col-span-full text-center py-8 sm:py-12">
            <p class="text-gray-500 text-sm sm:text-base" data-translate="no_rooms">Bu kategoride henüz oda bulunmuyor.</p>
        </div>
        {% endfor %}
    </div>

    <!-- Sayfalama -->
    {% if rooms.pages > 1 %}
    <div class="flex justify-center mt-8 sm:mt-12">
        <nav class="inline-flex rounded-md shadow-sm">
            {% for page in rooms.iter_pages() %}
                {% if page %}
                    <a href="{{ url_for('rooms.rooms_by_category', slug=category.slug, page=page) }}" 
                       class="px-3 sm:px-4 py-2 text-sm sm:text-base {% if page == rooms.page %}bg-gold text-white{% else %}bg-white text-gray-700 hover:bg-gray-50{% endif %} 
                                {% if loop.first %}rounded-l-md{% endif %} {% if loop.last %}rounded-r-md{% endif %}
                                border-t border-b {% if not loop.first %}border-l{% endif %} border-gray-200">
                        {{ page }}
                    </a>
                {% else %}
                    <span class="px-3 sm:px-4 py-2 text-sm sm:text-base border-t border-b border-l border-gray-200 bg-gray-50 text-gray-500">...</span>
                {% endif %}
            {% endfor %}
        </nav>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Geri dön butonunu seç
    const backButton = document.querySelector('.text-gold.hover\\:opacity-80');
    
    backButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        const previousPage = document.referrer;
        const roomIndexUrl = '{{ url_for("rooms.room_index") }}#rooms';
        
        if (previousPage) {
            window.history.back();
        } else {
            // Eğer önceki sayfa yoksa ana sayfadaki odalar bölümüne git
            window.location.href = roomIndexUrl;
        }
    });
});
</script>
{% endblock %} 