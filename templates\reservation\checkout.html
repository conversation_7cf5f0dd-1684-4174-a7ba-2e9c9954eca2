{% extends "base.html" %}

{% block title %}{{ _('Ödeme') }} - {{ _('Rezervasyon') }}{% endblock title %}

{% block head %}
<style>
    /* <PERSON><PERSON> stiller */
    .checkout-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 16px;
    }
    
    .page-title {
        font-size: 1.8rem;
        color: #333;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    /* Ana içerik grid */
    .checkout-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    @media (min-width: 768px) {
        .checkout-grid {
            grid-template-columns: 1.5fr 1fr;
        }
    }
    
    /* Form alanları */
    .checkout-form {
        padding: 1.25rem;
    }
    
    .form-section {
        margin-bottom: 1.5rem;
    }
    
    .form-section:last-child {
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .section-icon {
        width: 18px;
        height: 18px;
        margin-right: 0.5rem;
        color: #C6A87D;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-size: 0.8rem;
        font-weight: 500;
        color: #555;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: #fff;
        transition: all 0.2s ease;
    }
    
    .form-input:focus {
        border-color: #C6A87D;
        box-shadow: 0 0 0 2px rgba(198, 168, 125, 0.2);
        outline: none;
    }
    
    .form-input:hover {
        border-color: #bbb;
    }
    
    .input-icon-wrapper {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        color: #C6A87D;
    }
    
    .input-with-icon {
        padding-left: 2.5rem;
    }
    
    /* Form grid */
    .form-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    @media (min-width: 640px) {
        .form-row {
            grid-template-columns: 1fr 1fr;
        }
    }
    
    /* Ödeme yöntemleri */
    .payment-methods {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    @media (min-width: 640px) {
        .payment-methods {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    .payment-method {
        position: relative;
    }
    
    .payment-method input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }
    
    .payment-method-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
        height: 100%;
        text-align: center;
    }
    
    .payment-method input:checked + .payment-method-label {
        border-color: #C6A87D;
        background-color: rgba(198, 168, 125, 0.1);
        box-shadow: 0 0 0 2px rgba(198, 168, 125, 0.2);
    }
    
    .payment-method-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 0.5rem;
        color: #666;
    }
    
    .payment-method input:checked + .payment-method-label .payment-method-icon {
        color: #C6A87D;
    }
    
    .payment-method-name {
        font-size: 0.8rem;
        font-weight: 500;
        color: #555;
    }
    
    .payment-method input:checked + .payment-method-label .payment-method-name {
        color: #C6A87D;
    }
    
    /* Rezervasyon özeti */
    .reservation-summary {
        padding: 1.25rem;
        position: sticky;
        top: 1.5rem;
    }
    
    .summary-section {
        margin-bottom: 1.25rem;
    }
    
    .summary-section:last-child {
        margin-bottom: 0;
    }
    
    .summary-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
    }
    
    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }
    
    .summary-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
    
    .summary-label {
        font-size: 0.8rem;
        color: #666;
    }
    
    .summary-value {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
    }
    
    .room-info {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #eee;
    }
    
    .room-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        margin-right: 1rem;
    }
    
    .room-details {
        flex: 1;
    }
    
    .room-name {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .room-category {
        font-size: 0.8rem;
        color: #666;
    }
    
    .dates-info {
        display: flex;
        justify-content: space-between;
        background-color: #f9f7f4;
        border-radius: 6px;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .date-group {
        text-align: center;
    }
    
    .date-label {
        font-size: 0.7rem;
        color: #666;
        margin-bottom: 0.25rem;
    }
    
    .date-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
    }
    
    .nights-count {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        color: #C6A87D;
    }
    
    .price-total {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f9f7f4;
        border-radius: 6px;
        border-left: 3px solid #C6A87D;
    }
    
    .price-total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .price-total-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
    }
    
    .price-total-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #C6A87D;
    }
    
    .price-currency {
        font-size: 0.8rem;
        color: #888;
        margin-top: 0.25rem;
        text-align: right;
    }
    
    /* Buton */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
    }
    
    .btn-primary {
        background-color: #C6A87D;
        color: white;
        border: none;
    }
    
    .btn-primary:hover {
        background-color: #b39669;
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(198, 168, 125, 0.25);
    }
    
    .btn-primary:active {
        transform: translateY(0);
    }
    
    .btn-icon {
        width: 16px;
        height: 16px;
        margin-left: 0.5rem;
    }
    
    /* Gizli sınıfı */
    .hidden {
        display: none !important;
    }
    
    /* Para birimi butonları */
    .currency-btn {
        padding: 8px 12px;
        display: inline-block;
        border-radius: 6px;
        border: 1px solid #ddd;
        background-color: #fff;
        font-size: 0.8rem;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .currency-btn:hover {
        background-color: #f5f5f5;
    }
    
    .currency-btn.active {
        background-color: #C6A87D;
        color: white;
        border-color: #C6A87D;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-gray-50 py-8 pt-32 min-h-screen">
    <div class="checkout-container">
        <!-- Sayfa Başlığı -->
        <div class="text-center mb-8">
            <h1 class="page-title">{{ _('Ödeme Bilgileri') }}</h1>
            <p class="text-gray-600 text-sm">{{ _('Rezervasyonunuzu tamamlamak için bilgilerinizi giriniz') }}</p>
        </div>
        
        <form action="{{ url_for('reservation.reservation_checkout') }}" method="POST" id="checkoutForm">
                <input type="hidden" id="selected_currency" name="currency" value="{{ room.currency }}">
            <div class="checkout-grid">
                <!-- Sol: Müşteri ve Ödeme Bilgileri -->
                <div class="card checkout-form">
                    <!-- Kişisel Bilgiler -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            {{ _('Kişisel Bilgiler') }}
                        </h2>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="first_name">{{ _('Ad') }}</label>
                                <input type="text" id="first_name" name="first_name" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="last_name">{{ _('Soyad') }}</label>
                                <input type="text" id="last_name" name="last_name" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="email">{{ _('E-posta') }}</label>
                                <div class="input-icon-wrapper">
                                    <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <input type="email" id="email" name="email" class="form-input input-with-icon" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="phone">{{ _('Telefon') }}</label>
                                <div class="input-icon-wrapper">
                                    <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    <input type="tel" id="phone" name="phone" class="form-input input-with-icon" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ödeme Yöntemi -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            {{ _('Ödeme Yöntemi') }}
                        </h2>
                        
                        <div class="payment-methods">
                            <div class="payment-method">
                                <input type="radio" id="payment_method_2" name="payment_method_id" value="2" checked>
                                <label for="payment_method_2" class="payment-method-label">
                                    <svg class="payment-method-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                    </svg>
                                    <span class="payment-method-name">{{ _('Kredi Kartı') }}</span>
                                </label>
                            </div>
                            
                            <div class="payment-method">
                                <input type="radio" id="payment_method_3" name="payment_method_id" value="3">
                                <label for="payment_method_3" class="payment-method-label">
                                    <svg class="payment-method-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                                    </svg>
                                    <span class="payment-method-name">{{ _('Havale / EFT') }}</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Kredi Kartı Bilgileri (başlangıçta gizli) -->
                        <div id="creditCardFields" class="mt-4 hidden">
                            <div class="form-group">
                                <label class="form-label" for="card_holder">{{ _('Kart Sahibi') }}</label>
                                <input type="text" id="card_holder" name="card_holder" class="form-input" placeholder="Ad Soyad">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="card_number">{{ _('Kart Numarası') }}</label>
                                <input type="text" id="card_number" name="card_number" class="form-input" placeholder="1234 5678 9012 3456">
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label" for="expiry_date">{{ _('Son Kullanma Tarihi') }}</label>
                                    <input type="text" id="expiry_date" name="expiry_date" class="form-input" placeholder="MM/YY">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label" for="cvv">{{ _('CVV') }}</label>
                                    <input type="text" id="cvv" name="cvv" class="form-input" placeholder="123">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Havale/EFT Bilgileri (başlangıçta gizli) -->
                        <div id="transferFields" class="mt-4 hidden">
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-sm mb-2"><strong>{{ _('Banka Bilgileri:') }}</strong></p>
                                <p class="text-sm">{{ _('Banka: Ziraat Bankası') }}</p>
                                <p class="text-sm">{{ _('Şube: Merkez') }}</p>
                                <p class="text-sm">{{ _('IBAN: TR00 0000 0000 0000 0000 0000 00') }}</p>
                                <p class="text-sm">{{ _('Hesap Sahibi: Zeppelin Otel') }}</p>
                                <p class="text-sm mt-2">{{ _('Lütfen açıklama kısmına rezervasyon kodunuzu yazınız.') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Özel İstekler -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                            </svg>
                            {{ _('Özel İstekler') }}
                        </h2>
                        
                        <div class="form-group">
                            <textarea id="special_requests" name="special_requests" class="form-input" rows="3" placeholder="{{ _('Özel isteklerinizi buraya yazabilirsiniz...') }}"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Sağ: Rezervasyon Özeti -->
                <div class="card reservation-summary">
                    <div class="summary-section">
                        <h2 class="summary-title">{{ _('Rezervasyon Özeti') }}</h2>
                        
                        <!-- Oda Bilgisi -->
                        <div class="room-info">
                            {% if room.gallery_images %}
                            <img src="{{ url_for('static', filename='uploads/rooms/' + room.images_list[0]) }}" 
                                 alt="{{ get_lang_text(room, 'title') }}" 
                                 class="room-image">
                            {% else %}
                            <div class="room-image bg-gray-200 flex items-center justify-center">
                                <span class="text-xs text-gray-400">{{ _('Resim Yok') }}</span>
                            </div>
                            {% endif %}
                            
                            <div class="room-details">
                                <h3 class="room-name">{{ get_lang_text(room, 'title') }}</h3>
                                <p class="room-category">{{ room.category.name_tr if room.category else '-' }}</p>
                            </div>
                        </div>
                        
                        <!-- Tarih Bilgileri -->
                        <div class="dates-info">
                            <div class="date-group">
                                <p class="date-label">{{ _('Giriş Tarihi') }}</p>
                                <p class="date-value">{{ reservation.check_in.strftime('%d.%m.%Y') }}</p>
                            </div>
                            
                            <div class="nights-count">
                                {{ nights }} {{ _('Gece') }}
                            </div>
                            
                            <div class="date-group">
                                <p class="date-label">{{ _('Çıkış Tarihi') }}</p>
                                <p class="date-value">{{ reservation.check_out.strftime('%d.%m.%Y') }}</p>
                            </div>
                        </div>
                        
                        <!-- Fiyat Detayları -->
                        <div>
                            <div class="summary-item">
                                <span class="summary-label">{{ _('Misafir') }}</span>
                                <span class="summary-value">{{ reservation.guests }} {{ _('Kişi') }}</span>
                            </div>
                            
                            <div class="summary-item">
                                <span class="summary-label">{{ _('Gecelik Fiyat') }}</span>
                                <span class="summary-value">
                                    {% if room.currency == 'TRY' %}
                                        {{ room.price|round|int }} ₺
                                    {% elif room.currency == 'USD' %}
                                        ${{ room.price|round|int }}
                                    {% elif room.currency == 'EUR' %}
                                        €{{ room.price|round|int }}
                                    {% else %}
                                        {{ room.price|round|int }} {{ room.currency }}
                                    {% endif %}
                                </span>
                            </div>
                            
                            <div class="summary-item">
                                <span class="summary-label">{{ _('Gece Sayısı') }}</span>
                                <span class="summary-value">{{ reservation.nights }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Para Birimi Seçimi -->
                    <div class="mb-4">
                        <span class="summary-label block mb-2">{{ _('Ödeme Para Birimi') }}</span>
                        <div class="currency-selector flex gap-2">
                            <button type="button" class="currency-btn {% if selected_currency == 'EUR' %}active{% endif %}" data-currency="EUR">€ Euro</button>
                            <button type="button" class="currency-btn {% if selected_currency == 'USD' %}active{% endif %}" data-currency="USD">$ Dolar</button>
                            <button type="button" class="currency-btn {% if selected_currency == 'TRY' %}active{% endif %}" data-currency="TRY">₺ TL</button>
                            <input type="hidden" name="currency" id="selected_currency" value="{{ selected_currency }}">
                        </div>
                    </div>
                    
                    <!-- Toplam Fiyat -->
                    <div class="price-total">
                        <div class="price-total-row">
                            <span class="price-total-label">{{ _('Toplam Tutar') }}</span>
                            <span class="price-total-value">
                                {% if reservation.currency == 'TRY' %}
                                    {{ reservation.amount|round|int }} ₺
                                {% elif reservation.currency == 'USD' %}
                                    ${{ reservation.amount|round|int }}
                                {% elif reservation.currency == 'EUR' %}
                                    €{{ reservation.amount|round|int }}
                                {% else %}
                                    {{ reservation.amount|round|int }} {{ reservation.currency }}
                                {% endif %}
                            </span>
                        </div>
                        
                        <p class="price-currency">
                            {% for curr in currencies %}
                                {% if curr.currency_code != reservation.currency %}
                                {% if curr.currency_code == 'TRY' %}
                                    {{ (reservation.amount * curr.selling_rate)|round|int }} ₺
                                {% elif curr.currency_code == 'USD' %}
                                    ${{ (reservation.amount * curr.selling_rate)|round|int }}
                                {% elif curr.currency_code == 'EUR' %}
                                    €{{ (reservation.amount * curr.selling_rate)|round|int }}
                                {% else %}
                                    {{ (reservation.amount * curr.selling_rate)|round|int }} {{ curr.currency_code }}
                                {% endif %}
                                {% if not loop.last and curr.currency_code != currencies[-1].currency_code %}, {% endif %}
                                {% endif %}
                            {% endfor %}
                        </p>
                    </div>
                    
                    <!-- Ödeme Butonu -->
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <span>{{ _('Rezervasyonu Tamamla') }}</span>
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ödeme yöntemi seçim alanlarını al
        const paymentMethod2 = document.getElementById('payment_method_2'); // Kredi Kartı
        const paymentMethod3 = document.getElementById('payment_method_3'); // Havale/EFT
        
        // Ödeme detay alanlarını al
        const creditCardFields = document.getElementById('creditCardFields');
        const transferFields = document.getElementById('transferFields');
        
        // Başlangıçta seçili olan ödeme yöntemine göre alanları göster/gizle
        updatePaymentFields();
        
        // Ödeme yöntemi değiştiğinde alanları güncelle
        paymentMethod2.addEventListener('change', updatePaymentFields);
        paymentMethod3.addEventListener('change', updatePaymentFields);
        
        function updatePaymentFields() {
            // Tüm alanları gizle
            creditCardFields.classList.add('hidden');
            transferFields.classList.add('hidden');
            
            // Seçilen ödeme yöntemine göre ilgili alanları göster
            if (paymentMethod2.checked) {
                creditCardFields.classList.remove('hidden');
            } else if (paymentMethod3.checked) {
                transferFields.classList.remove('hidden');
            }
        }
        
        // Para birimi seçimi
        const currencyButtons = document.querySelectorAll('.currency-btn');
        currencyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault(); // Butonun varsayılan davranışını engelle
                
                // Aktif sınıfı kaldır
                currencyButtons.forEach(btn => btn.classList.remove('active'));
                // Tıklanan butona aktif sınıfı ekle
                this.classList.add('active');
                
                // Seçilen para birimini al
                const selectedCurrency = this.getAttribute('data-currency');
                
                // Seçilen para birimini form'a ekle
                const currencyInput = document.getElementById('selected_currency');
                if (currencyInput) {
                    currencyInput.value = selectedCurrency;
                }
                
                // Form submit et - bu şekilde sayfayı yeniden yükleyerek para birimi değişimini uygulayacağız
                document.getElementById('checkoutForm').submit();
            });
        });
    });
</script>
{% endblock %} 