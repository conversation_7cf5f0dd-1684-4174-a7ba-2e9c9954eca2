from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models.setting import db, Setting
import os
from datetime import datetime
from models.ip_settings import IPSettings
from middleware.hotel_network import is_hotel_network
import ipaddress

settings_bp = Blueprint('settings', __name__)

def save_image(file, folder='settings'):
    if file:
        filename = secure_filename(file.filename)
        base, ext = os.path.splitext(filename)
        filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads', folder)
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        return filename
    return None

@settings_bp.route('/admin/settings')
@login_required
def settings_list():
    settings = Setting.query.order_by(Setting.group, Setting.key).all()
    return render_template('admin/settings/list.html', settings=settings)

@settings_bp.route('/admin/settings/edit', methods=['GET', 'POST'])
@login_required
def settings_edit():
    if request.method == 'POST':
        try:
            # Text Logo Ayarı
            use_text_logo = request.form.get('use_text_logo') == 'on'
            Setting.set_setting('use_text_logo', str(use_text_logo), 'boolean', 'general')

            # Genel Site Ayarları
            site_settings = {
                'site_title': ('text', 'general'),
                'site_description': ('text', 'general'),
                'site_text_logo': ('text', 'general'),
                'main_page_content': ('text', 'general'),
            }



            for key, (type_, group) in site_settings.items():
                value = request.form.get(key)
                if value is not None:
                    Setting.set_setting(key, value, type_, group)

            # Ana Logo
            if 'site_logo' in request.files:
                logo_file = request.files['site_logo']
                if logo_file and logo_file.filename:
                    filename = save_image(logo_file, 'settings')
                    if filename:
                        Setting.set_setting('site_logo', filename, 'image', 'general')

            # Küçük Logo (Scroll Logo)
            if 'site_logo_small' in request.files:
                small_logo_file = request.files['site_logo_small']
                if small_logo_file and small_logo_file.filename:
                    filename = save_image(small_logo_file, 'settings')
                    if filename:
                        Setting.set_setting('site_logo_small', filename, 'image', 'general')

            # Sosyal Medya Ayarları
            social_settings = {
                'whatsapp_link': ('link', 'social'),
                'instagram_link': ('link', 'social'),
                'whatsapp_active': ('boolean', 'social'),
                'instagram_active': ('boolean', 'social'),
            }

            for key, (type_, group) in social_settings.items():
                if 'active' in key:
                    value = 'True' if request.form.get(key) == 'on' else 'False'
                else:
                    value = request.form.get(key)
                if value is not None:
                    Setting.set_setting(key, value, type_, group)

            db.session.commit()
            flash('Ayarlar başarıyla güncellendi.', 'success')
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Settings update error: {str(e)}")
            flash(f'Ayarlar güncellenirken bir hata oluştu: {str(e)}', 'error')
            
        return redirect(url_for('settings.settings_edit'))

    settings = {setting.key: setting.value for setting in Setting.query.all()}
    return render_template('admin/settings/edit.html', settings=settings)

@settings_bp.route('/admin/settings/edit', methods=['POST'])
@login_required
def settings_edit_post():
    try:
        # Site title ayarını kaydet
        site_title = request.form.get('site_title')
        Setting.set_setting('site_title', site_title, 'text', 'general')
        
        # Diğer ayarlar...
        
        flash('Ayarlar başarıyla güncellendi!', 'success')
        return redirect(url_for('settings.settings_list'))
    except Exception as e:
        flash('Ayarlar güncellenirken bir hata oluştu!', 'error')
        return redirect(url_for('settings.settings_edit'))

# Logo silme route'u ekleyelim
@settings_bp.route('/admin/settings/remove-logo/<type>', methods=['POST'])
@login_required
def remove_logo(type):
    try:
        setting_key = 'site_logo' if type == 'main' else 'site_logo_small'
        setting = Setting.query.filter_by(key=setting_key).first()
        
        if setting and setting.value:
            # Dosyayı sil
            file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'settings', setting.value)
            if os.path.exists(file_path):
                os.remove(file_path)
            
            setting.value = None
            db.session.commit()
            
        return {'success': True}
    except Exception as e:
        current_app.logger.error(f"Logo removal error: {str(e)}")
        return {'success': False, 'error': str(e)}

@settings_bp.route('/ip-settings', methods=['GET', 'POST'])
@login_required
def ip_settings():
    if not current_user.is_admin:
        return redirect(url_for('index'))

    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'add':
            # Yeni IP aralığı ekle
            ip_range = request.form.get('ip_range')
            description = request.form.get('description')
            
            # IP formatını düzelt
            if ip_range:
                # /255 formatını /24'e çevir
                if ip_range.endswith('/255'):
                    ip_range = f"{ip_range.split('/')[0]}/24"
                # Tek IP için /32 ekle
                elif '/' not in ip_range:
                    ip_range = f"{ip_range}/32"
            
            if ip_range:
                try:
                    # Test et
                    ipaddress.ip_network(ip_range, strict=False)
                    
                    # IP adresi zaten var mı kontrol et
                    existing_ip = IPSettings.query.filter_by(ip_range=ip_range).first()
                    if existing_ip:
                        if not existing_ip.is_active:
                            existing_ip.is_active = True
                            db.session.commit()
                            flash('IP aralığı aktif hale getirildi.', 'success')
                        else:
                            flash('Bu IP aralığı zaten ekli.', 'warning')
                    else:
                        new_ip = IPSettings(
                            ip_range=ip_range,
                            description=description,
                            is_active=True
                        )
                        db.session.add(new_ip)
                        db.session.commit()
                        flash('IP aralığı başarıyla eklendi.', 'success')
                except Exception as e:
                    db.session.rollback()
                    flash(f'IP aralığı eklenirken hata oluştu: {str(e)}', 'error')
                    
        elif action == 'delete':
            # IP aralığını sil
            ip_id = request.form.get('ip_id')
            if ip_id:
                ip_setting = IPSettings.query.get(ip_id)
                if ip_setting:
                    db.session.delete(ip_setting)
                    db.session.commit()
                    flash('IP aralığı başarıyla silindi.', 'success')
                    
        elif action == 'toggle':
            # IP aralığını aktif/pasif yap
            ip_id = request.form.get('ip_id')
            if ip_id:
                ip_setting = IPSettings.query.get(ip_id)
                if ip_setting:
                    ip_setting.is_active = not ip_setting.is_active
                    db.session.commit()
                    flash('IP aralığı durumu güncellendi.', 'success')

    ip_settings = IPSettings.query.order_by(IPSettings.created_at.desc()).all()
    return render_template('admin/settings/ip_settings.html', ip_settings=ip_settings)

@settings_bp.route('/ip-settings/test', methods=['POST'])
@login_required
def test_ip():
    if not current_user.is_admin:
        return redirect(url_for('index'))
        
    test_ip = request.form.get('test_ip')
    if test_ip:
        try:
            is_allowed = is_hotel_network(test_ip)
            if is_allowed:
                flash(f'IP adresi {test_ip} otel ağında bulunuyor.', 'success')
            else:
                flash(f'IP adresi {test_ip} otel ağında bulunmuyor.', 'error')
        except Exception as e:
            flash(f'IP kontrolü sırasında hata: {str(e)}', 'error')
    
    return redirect(url_for('settings.ip_settings')) 