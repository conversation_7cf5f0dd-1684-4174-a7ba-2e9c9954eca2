<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="dark">
    <meta name="theme-color" content="#000000">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="description" content="Zeppelin Hotel Kapadokya'da lüks konaklama deneyimi sunar. <PERSON><PERSON><PERSON>z manzaralar, konforl<PERSON> odalar, özel hizmetler ve unutulmaz anılarla dolu bir tatil için ideal seçim. Kapadokya'nın büyülü atmosferinde huzurlu bir konaklama deneyimi yaşayın.">
    <meta name="keywords" content="Zeppelin Hotel, Kapadokya oteli, lüks otel, butik otel, Nevşehir otel, Kapadokya konaklama, balon turu, mağara otel, taş otel, romantik otel, spa otel, Göreme otelleri, Ürgüp otelleri">
    <title>{{ settings.site_title or 'Zeppelin Hotel' }} | Kapadokya - Resmi Web Sitesi</title>

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ request.url }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:title" content="{{ settings.site_title or 'Zeppelin Hotel' }}">
    <meta property="og:description" content="Zeppelin Hotel - Kapadokya'nın kalbinde, konfor ve lüksün buluştuğu nokta. Modern tesislerimiz ve özel hizmetlerimizle unutulmaz bir konaklama deneyimi sizi bekliyor.">
    <meta property="og:image" content="{% if settings.site_logo %}{{ url_for('static', filename='uploads/settings/' + settings.site_logo, _external=True) }}{% else %}{{ request.url_root }}{% endif %}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.url }}">
    <meta property="twitter:title" content="{{ settings.site_title or 'Zeppelin Hotel' }}">
    <meta property="twitter:description" content="Zeppelin Hotel - Kapadokya'nın kalbinde, konfor ve lüksün buluştuğu nokta. Modern tesislerimiz ve özel hizmetlerimizle unutulmaz bir konaklama deneyimi sizi bekliyor.">
    <meta property="twitter:image" content="{% if settings.site_logo %}{{ url_for('static', filename='uploads/settings/' + settings.site_logo, _external=True) }}{% else %}{{ request.url_root }}{% endif %}">

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LodgingBusiness",
      "name": "Yapay Zeka Destekli {{ settings.site_title or 'Zeppelin Hotel' }}",
      "description": "Yapay zeka teknolojisi ile geliştirilmiş akıllı otel rezervasyon sistemi. Kapadokya'nın kalbinde AI destekli lüks konaklama deneyimi. Otomatik rezervasyon, akıllı oda yönetimi ve machine learning ile kişiselleştirilmiş hizmetler.",
      "url": "{{ request.url_root }}",
      "logo": "{% if settings.site_logo %}{{ url_for('static', filename='uploads/settings/' + settings.site_logo, _external=True) }}{% else %}{{ request.url_root }}{% endif %}",
      "image": "{% if settings.site_logo %}{{ url_for('static', filename='uploads/settings/' + settings.site_logo, _external=True) }}{% else %}{{ request.url_root }}{% endif %}",
      "telephone": "{{ settings.contact_phone or '+90 ************' }}",
      "email": "{{ settings.contact_email or '<EMAIL>' }}",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "{{ settings.address_street or '' }}",
        "addressLocality": "{{ settings.address_city or 'Nevşehir' }}",
        "addressRegion": "{{ settings.address_region or 'Kapadokya' }}",
        "postalCode": "{{ settings.address_postal or '' }}",
        "addressCountry": "TR"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "{{ settings.geo_lat or '38.626615' }}",
        "longitude": "{{ settings.geo_lng or '34.798558' }}"
      },
      "priceRange": "₺₺₺",
      "starRating": {
        "@type": "Rating",
        "ratingValue": "{{ settings.hotel_rating or '4.5' }}"
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday"
        ],
        "opens": "00:00",
        "closes": "23:59"
      },
      "makesOffer": [
        {
          "@type": "Offer",
          "name": "Oda Rezervasyonu",
          "description": "Lüks ve konforlu odalarımızda konaklama imkanı",
          "url": "https://zeppelincappadocia.com/rooms"
        }
      ],
      "amenityFeature": [
        {
          "@type": "LocationFeatureSpecification",
          "name": "WiFi",
          "value": true
        },
        {
          "@type": "LocationFeatureSpecification",
          "name": "Parking",
          "value": true
        },
        {
          "@type": "LocationFeatureSpecification",
          "name": "Restaurant",
          "value": true
        }
      ]
    }
    </script>

    <!-- Font imports -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=Poppins:wght@100;200;300;400;500;600&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Analytics -->
    {% if settings.google_analytics_active and settings.google_analytics_id %}
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ settings.google_analytics_id }}"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '{{ settings.google_analytics_id }}');
    </script>
    {% endif %}

    <!-- Google Tag Manager -->
    {% if settings.google_tag_manager_active and settings.google_tag_manager_id %}
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ settings.google_tag_manager_id }}');</script>
    {% endif %}
</head>
<body class="font-montserrat bg-black">
    <!-- Google Tag Manager (noscript) -->
    {% if settings.google_tag_manager_active and settings.google_tag_manager_id %}
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ settings.google_tag_manager_id }}"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    {% endif %}

<div class="relative h-screen min-h-[600px] overflow-hidden">
    <!-- Logo -->
    <a href="{{ url_for('main.main') }}" class="absolute top-4 sm:top-8 left-1/2 transform -translate-x-1/2 z-20">
        {% if settings.use_text_logo == 'True' %}
            <div class="text-logo text-base sm:text-2xl">{{ settings.site_text_logo or 'Site Adı' }}</div>
        {% else %}
            {% if settings.site_logo %}
                <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo) }}"
                     alt="{{ settings.site_title or 'Zeppelin Hotel' }} - Logo"
                     class="w-auto h-[80px] sm:h-[120px] md:h-[140px] lg:h-[160px] transition-all duration-500"
                     loading="eager">
            {% else %}
                <div class="text-white text-2xl sm:text-3xl md:text-4xl font-light tracking-wider"
                     style="font-family: 'Poppins', sans-serif;">
                    {{ settings.site_title or 'ZEPPELIN HOTEL' }}
                </div>
            {% endif %}
        {% endif %}
    </a>

    <!-- Slider -->
    <div class="relative h-full overflow-hidden" id="parallaxContainer">
        {% for slider in slider_images %}
        <div class="slide absolute inset-0 transition-opacity duration-1000 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %}">
            {% if slider.file_type == 'image' %}
                <div class="relative w-full h-full overflow-hidden will-change-transform">
                    <img src="{{ url_for('static', filename='uploads/' + slider.file_path) }}" 
                         alt="{{ slider.title }}" 
                         class="w-full h-full object-cover transition-all duration-1000 ease-out">
                    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/30 pointer-events-none"></div>
                </div>
            {% else %}
                <div class="relative w-full h-full overflow-hidden">
                    <video src="{{ url_for('static', filename='uploads/' + slider.file_path) }}" 
                           class="w-full h-full object-cover"
                           autoplay muted loop playsinline>
                        Tarayıcınız video elementini desteklemiyor.
                    </video>
                    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/30 pointer-events-none"></div>
                </div>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if not slider_images %}
        <div class="slide absolute inset-0 opacity-100">
            <div class="relative w-full h-full overflow-hidden bg-black">
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                        <h2 class="text-white text-3xl md:text-4xl font-light tracking-wider mb-6">{{ settings.site_title or 'Zeppelin Hotel' }}</h2>
                        <div class="w-24 h-1 bg-white/20 mx-auto"></div>
                    </div>
                </div>
                <div class="absolute inset-0 bg-gradient-to-b from-black/60 via-transparent to-black/60 pointer-events-none"></div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Giriş Butonları -->
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center z-10 w-full px-4">
        <!-- SEO için H1 başlık (görsel olarak gizlenmiş) -->
        <h1 class="sr-only">Zeppelin Hotel - Kapadokya'da Lüks ve Konforun Buluşma Noktasi</h1>

        <!-- Butonlar -->
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 md:space-x-8">
                <a href="{{ url_for('main.main') }}?lang=tr"
                   class="group relative w-full sm:w-auto min-w-[160px] sm:min-w-[180px] px-6 sm:px-8 py-3 sm:py-4
                          transition-all duration-700 overflow-hidden
                          bg-gradient-to-r from-white/5 to-white/10
                          rounded-2xl border border-white/20 hover:border-[#C6A87D]/50
                          hover:shadow-[0_0_30px_rgba(198,168,125,0.3)]
                          hover:bg-gradient-to-r hover:from-[#C6A87D]/10 hover:to-[#C6A87D]/20
                          transform hover:scale-105 hover:-translate-y-1
                          focus:outline-none focus:ring-2 focus:ring-[#C6A87D]/50 focus:ring-offset-2 focus:ring-offset-black"
                   style="font-family: 'Poppins', sans-serif;"
                   aria-label="Türkçe ana sayfaya giriş yap">

                    <!-- Hover Efekti -->
                    <span class="absolute inset-0 bg-gradient-to-r from-[#C6A87D]/20 to-transparent
                                translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></span>

                    <!-- Text -->
                    <span class="relative inline-flex flex-col items-center">
                        <span class="text-base sm:text-lg md:text-xl lg:text-2xl tracking-[0.2em] text-white/90 font-light
                                   transition-all duration-500 group-hover:text-white group-hover:tracking-[0.25em]"
                              data-translate="enter_button_tr">
                            GİRİŞ
                        </span>
                        <span class="h-[1px] w-0 bg-gradient-to-r from-transparent via-[#C6A87D] to-transparent
                                   transition-all duration-700 group-hover:w-full mt-2 sm:mt-3"></span>
                    </span>
                </a>

                <!-- Ayraç -->
                <div class="hidden sm:block h-[40px] md:h-[50px] w-[1px] bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
                <!-- Mobil ayraç -->
                <div class="block sm:hidden h-[1px] w-[60px] sm:w-[80px] bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

                <a href="{{ url_for('main.main') }}?lang=en"
                   class="group relative w-full sm:w-auto min-w-[160px] sm:min-w-[180px] px-6 sm:px-8 py-3 sm:py-4
                          transition-all duration-700 overflow-hidden
                          bg-gradient-to-r from-white/5 to-white/10
                          rounded-2xl border border-white/20 hover:border-[#C6A87D]/50
                          hover:shadow-[0_0_30px_rgba(198,168,125,0.3)]
                          hover:bg-gradient-to-r hover:from-[#C6A87D]/10 hover:to-[#C6A87D]/20
                          transform hover:scale-105 hover:-translate-y-1
                          focus:outline-none focus:ring-2 focus:ring-[#C6A87D]/50 focus:ring-offset-2 focus:ring-offset-black"
                   style="font-family: 'Poppins', sans-serif;"
                   aria-label="Enter English main site">

                    <!-- Hover Efekti -->
                    <span class="absolute inset-0 bg-gradient-to-r from-[#C6A87D]/20 to-transparent
                                translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></span>

                    <!-- Text -->
                    <span class="relative inline-flex flex-col items-center">
                        <span class="text-base sm:text-lg md:text-xl lg:text-2xl tracking-[0.2em] text-white/90 font-light
                                   transition-all duration-500 group-hover:text-white group-hover:tracking-[0.25em]"
                              data-translate="enter_button_en">
                            ENTER
                        </span>
                        <span class="h-[1px] w-0 bg-gradient-to-r from-transparent via-[#C6A87D] to-transparent
                                   transition-all duration-700 group-hover:w-full mt-2 sm:mt-3"></span>
                    </span>
                </a>
            </div>
    </div>

    <!-- Referans Logoları - Modern Card Tasarımı -->
    {% if sponsors %}
    <div class="absolute bottom-20 sm:bottom-24 left-1/2 transform -translate-x-1/2 text-center z-15 w-full max-w-5xl px-4">
        <!-- Ana Card Container -->
        <div class="bg-black/30 rounded-3xl border border-white/10 p-6 sm:p-8
                    shadow-2xl shadow-black/30 hover:shadow-[0_0_40px_rgba(255,255,255,0.1)]
                    transition-all duration-700 hover:border-white/20 hover:bg-black/40">

            <!-- Başlık -->
            <div class="mb-6">
                <h3 class="text-white/80 text-sm sm:text-base font-light tracking-wider mb-3"
                    style="font-family: 'Poppins', sans-serif; font-weight: 200;">
                    İş Ortaklarımız
                </h3>
                <div class="w-16 h-[1px] bg-gradient-to-r from-transparent via-[#C6A87D]/60 to-transparent mx-auto"></div>
            </div>

            <!-- Logo Grid -->
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 sm:gap-6 justify-items-center">
                {% for sponsor in sponsors %}
                <div class="group relative w-full max-w-[140px] h-16 sm:h-20
                            bg-gradient-to-br from-white/5 via-white/10 to-white/5
                            rounded-2xl px-4 py-3
                            border border-white/10 hover:border-[#C6A87D]/40
                            hover:bg-gradient-to-br hover:from-[#C6A87D]/5 hover:to-[#C6A87D]/10
                            transition-all duration-500 hover:shadow-[0_0_20px_rgba(198,168,125,0.2)]
                            transform hover:scale-105 hover:-translate-y-1">
                    {% if sponsor.url %}
                    <a href="{{ sponsor.url }}" target="_blank" rel="noopener noreferrer"
                       class="w-full h-full flex items-center justify-center">
                    {% endif %}
                    <img src="{{ url_for('static', filename='uploads/sponsors/' + sponsor.image) }}"
                         alt="{{ sponsor.name }} - İş Ortağımız"
                         class="max-h-full max-w-full w-auto h-auto object-contain
                                opacity-70 group-hover:opacity-100
                                transition-all duration-500 filter brightness-90 group-hover:brightness-110"
                         loading="lazy">
                    {% if sponsor.url %}
                    </a>
                    {% endif %}

                    <!-- Hover Efekti -->
                    <div class="absolute inset-0 bg-gradient-to-r from-[#C6A87D]/10 to-transparent
                                opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Cookie/KVKK Bildirimi - Modern Card Tasarımı -->
<div id="cookieConsent"
     class="fixed bottom-0 left-1/2 -translate-x-1/2 w-full max-w-2xl mx-auto transform translate-y-full
             transition-all duration-700 ease-out z-[60] px-4">
    <div class="bg-black/70 rounded-3xl p-8 border border-white/15 shadow-2xl
                bg-gradient-to-br from-white/[0.08] to-transparent mb-6
                hover:shadow-[0_0_60px_rgba(198,168,125,0.2)] transition-all duration-500">

        <!-- İkon ve Başlık -->
        <div class="flex items-center gap-5 mb-6">
            <div class="w-14 h-14 rounded-2xl bg-gradient-to-br from-[#C6A87D]/30 to-[#C6A87D]/10
                        flex items-center justify-center border border-[#C6A87D]/30 shadow-lg shadow-[#C6A87D]/20">
                <i class="fas fa-cookie-bite text-[#C6A87D] text-xl"></i>
            </div>
            <div>
                <h3 class="text-white/95 text-xl font-light tracking-wide mb-1"
                    style="font-family: 'Poppins', sans-serif; font-weight: 300;">
                    Çerez Politikası
                </h3>
                <div class="w-16 h-[1px] bg-gradient-to-r from-[#C6A87D]/60 to-transparent"></div>
            </div>
        </div>

        <!-- Açıklama -->
        <div class="text-white/85 text-[15px] leading-relaxed mb-6">
            <p class="font-light tracking-wide" style="font-family: 'Poppins', sans-serif; font-weight: 200;">
                Sizlere daha iyi bir deneyim sunabilmek için çerezleri kullanıyoruz.
                <a href="/kvkk" class="text-[#C6A87D] hover:text-[#C6A87D]/80 transition-all duration-300
                                     border-b border-[#C6A87D]/50 hover:border-[#C6A87D] pb-0.5
                                     font-normal">
                    Çerez Politikamız
                </a>
                sayfasını inceleyerek daha detaylı bilgi edinebilirsiniz.
            </p>
        </div>

        <!-- Ayraç -->
        <div class="my-6 border-t border-white/15"></div>

        <!-- Butonlar -->
        <div class="flex items-center justify-center gap-4">
            <button onclick="rejectCookies()"
                    class="group px-6 py-3 text-sm text-white/70 hover:text-white transition-all duration-500
                           rounded-2xl border border-white/15 hover:border-white/30 tracking-wide
                           hover:bg-white/10 flex items-center gap-3
                           transform hover:scale-105 hover:-translate-y-0.5"
                    style="font-family: 'Poppins', sans-serif; font-weight: 200;">
                <i class="fas fa-times text-xs opacity-70 group-hover:opacity-100 transition-opacity duration-300"></i>
                Reddet
            </button>
            <button onclick="acceptCookies()"
                    class="group relative px-6 py-3 bg-gradient-to-r from-[#C6A87D] to-[#B8956F]
                           text-white text-sm rounded-2xl transition-all duration-500
                           shadow-xl shadow-[#C6A87D]/30 hover:shadow-[#C6A87D]/50
                           flex items-center gap-3 tracking-wide overflow-hidden
                           transform hover:scale-105 hover:-translate-y-0.5"
                    style="font-family: 'Poppins', sans-serif; font-weight: 300;">
                <!-- Hover Efekti -->
                <span class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent
                            translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></span>
                <!-- İkon ve Metin -->
                <i class="fas fa-check text-xs relative z-10"></i>
                <span class="relative z-10">
                    Kabul Et
                </span>
            </button>
        </div>
    </div>
</div>

<!-- Copyright - Modern Card Tasarımı -->
<div class="absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 text-center z-20">
    <div class="bg-black/30 rounded-2xl px-4 sm:px-6 py-2 sm:py-3 border border-white/10
                shadow-lg shadow-black/30 hover:shadow-[0_0_20px_rgba(255,255,255,0.1)]
                transition-all duration-500 hover:border-white/20 hover:bg-black/40">
        <p class="text-white/60 text-xs sm:text-sm font-light tracking-wide"
           style="font-family: 'Poppins', sans-serif; font-weight: 200;">
            © {{ current_year or '2025' }} {{ settings.site_title or 'Zeppelin Hotel' }}.
            <span class="text-xs sm:text-sm" data-translate="all_rights_reserved">Tüm hakları saklıdır.</span>
        </p>
    </div>
</div>

<style>
/* Temel stiller */
body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 200;
    background: #000;
    color: #fff;
    scroll-behavior: smooth;
}

/* Logo stili */
.text-logo {
    font-size: 2rem;
    font-weight: 300;
    color: white;
    text-align: center;
    padding: 1rem;
    font-family: 'Poppins', sans-serif;
}

/* Slider animasyonları */
.slide {
    opacity: 1 !important;
    z-index: 1;
    position: absolute;
    inset: 0;
    transition: opacity 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Card Hover Efektleri */
.group:hover .scale-\[0\.98\] {
    transform: scale(1);
}

/* Slider images - no effects */
.slide img, .slide video {
    transition: none;
}

.slide:hover img, .slide:hover video {
    transform: none;
}

/* Gelişmiş Scroll Stilleri */
::-webkit-scrollbar {
    width: 10px;
    background-color: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(198, 168, 125, 0.6), rgba(198, 168, 125, 0.3));
    border-radius: 10px;
    border: 2px solid rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(198, 168, 125, 0.8), rgba(198, 168, 125, 0.5));
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

/* Gelişmiş Animasyonlar */
* {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 0.3s;
}

/* Card Hover Animasyonları */
.group {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card Optimizasyonu */
.card-container {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Animasyonları */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 6s ease infinite;
}

/* Responsive Font Optimizasyonu */
@media (max-width: 640px) {
    body {
        font-size: 14px;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    body {
        font-size: 15px;
    }
}

@media (min-width: 1025px) {
    body {
        font-size: 16px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Çerez onayı kontrolü
    const cookieConsent = document.getElementById('cookieConsent');

    if (!localStorage.getItem('cookieConsent')) {
        setTimeout(() => {
            cookieConsent.style.transform = 'translate(-50%, 0)';
            cookieConsent.style.opacity = '1';
        }, 1500);
    }

    // Slider otomatik geçiş (eğer birden fazla slide varsa)
    const slides = document.querySelectorAll('.slide');
    if (slides && slides.length > 1) {
        let currentSlide = 0;
        let sliderInterval;

        function startSlider() {
            sliderInterval = setInterval(() => {
                if (slides[currentSlide]) {
                    slides[currentSlide].style.opacity = '0';
                }
                currentSlide = (currentSlide + 1) % slides.length;
                if (slides[currentSlide]) {
                    slides[currentSlide].style.opacity = '1';
                }
            }, 5000);
        }

        // Sayfa görünür olduğunda slider'ı başlat
        if (document.visibilityState === 'visible') {
            startSlider();
        }

        // Sayfa görünürlük durumu değiştiğinde slider'ı kontrol et
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                startSlider();
            } else {
                clearInterval(sliderInterval);
            }
        });
    }

    // Parallax effect removed
});

function acceptCookies() {
    localStorage.setItem('cookieConsent', 'accepted');
    const cookieConsent = document.getElementById('cookieConsent');
    cookieConsent.style.transform = 'translate(-50%, 100%)';
    cookieConsent.style.opacity = '0';
}

function rejectCookies() {
    localStorage.setItem('cookieConsent', 'rejected');
    const cookieConsent = document.getElementById('cookieConsent');
    cookieConsent.style.transform = 'translate(-50%, 100%)';
    cookieConsent.style.opacity = '0';
}

// Intersection Observer ile animasyonlar
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Animasyon için elementleri gözlemle
document.addEventListener('DOMContentLoaded', () => {
    const animatedElements = document.querySelectorAll('.group, .card-container');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        observer.observe(el);
    });
});
</script>

</body>
</html> 