{% extends "base.html" %}

{% block title %}Sunucu Hatası - 500{% endblock %}

{% block content %}
<div class="min-h-screen flex items-end pb-20 bg-cover bg-center bg-no-repeat relative bg-500">
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
    
    <div class="container mx-auto relative z-10 px-4">
        <div class="max-w-2xl mx-auto text-center">
            <!-- 500 Text -->
            <div class="font-poppins font-light text-[120px] leading-none text-white/90 mb-6">500</div>
            
            <!-- <PERSON>a <PERSON> -->
            <h2 class="font-poppins font-light text-3xl text-white mb-6">
                Üzgünüz! Bir Hata Oluştu
            </h2>
            <p class="font-poppins font-light text-lg text-white/80 mb-2">
                Sunucumuzda geçici bir sorun oluştu. Lütfen daha sonra tekrar deneyin.
            </p>
            <p class="font-poppins font-light text-lg text-white/80 mb-10">
                Bu sorun devam ederse lütfen bizimle iletişime geçin.
            </p>
            
            <!-- Butonlar -->
            <div class="flex justify-center gap-6 flex-wrap">
                <a href="{{ url_for('main.main') }}" 
                   class="bg-white hover:bg-white/90 text-black px-10 py-4 font-poppins font-light tracking-wider transition-all duration-300">
                    ANA SAYFA
                </a>
                <a href="javascript:history.back()" 
                   class="border hover:bg-white hover:text-black text-white px-10 py-4 font-poppins font-light tracking-wider transition-all duration-300">
                    GERİ DÖN
                </a>
            </div>
        </div>
    </div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400&display=swap');

.bg-500 {
    background-image: url("{{ url_for('static', filename='images/errors/500.jpeg') }}");
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container {
    animation: fadeIn 1s ease-out forwards;
}

@media (max-width: 640px) {
    .font-poppins.text-\[120px\] {
        font-size: 80px;
    }
    .flex.justify-center {
        flex-direction: column;
        align-items: center;
    }
    .flex.justify-center a {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %} 