{% extends "base.html" %}

{% block title %}{{ get_lang_text(room, 'title') }} - {% if current_language == 'tr' %}<PERSON>da Detayları{% else %}Room Details{% endif %}{% endblock title %}

{% block head %}
<!-- Flickity CSS -->
<link rel="stylesheet" href="https://unpkg.com/flickity@2/dist/flickity.min.css">
<!-- Flickity JavaScript -->
<script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js"></script>

<style>
/* Ana slider container */
.carousel-wrapper {
    max-width: 1400px;
    margin: 0 auto 15px;
    padding: 0 8px;
}

@media (min-width: 640px) {
    .carousel-wrapper {
        margin-bottom: 25px;
        padding: 0 15px;
    }
}

.carousel {
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.carousel-cell {
    width: 100%;
    height: 250px;
}

@media (min-width: 480px) {
    .carousel-cell {
        height: 300px;
    }
}

@media (min-width: 640px) {
    .carousel-cell {
        height: 400px;
    }
}

@media (min-width: 768px) {
    .carousel-cell {
        height: 500px;
    }
}

.carousel-cell img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Thumbnail grid */
.gallery-thumbs {
    max-width: 1400px;
    margin: 15px auto;
    padding: 0 8px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 6px;
}

@media (min-width: 480px) {
    .gallery-thumbs {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }
}

@media (min-width: 640px) {
    .gallery-thumbs {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        padding: 0 15px;
        margin: 25px auto;
    }
}

.gallery-thumb {
    position: relative;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-thumb:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.gallery-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.gallery-thumb:hover img {
    opacity: 1;
}

/* Slider navigasyon butonları */
.flickity-button {
    width: 36px !important;
    height: 36px !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

@media (min-width: 640px) {
    .flickity-button {
        width: 44px !important;
        height: 44px !important;
    }
}

.flickity-prev-next-button.previous { left: 10px; }
.flickity-prev-next-button.next { right: 10px; }

@media (min-width: 640px) {
    .flickity-prev-next-button.previous { left: 20px; }
    .flickity-prev-next-button.next { right: 20px; }
}

/* Modal stilleri */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    padding: 40px;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 1001;
}

.modal img {
    max-height: 80vh;
    max-width: 90vw;
    object-fit: contain;
}

/* Aktif thumbnail stili */
.gallery-thumb.is-selected {
    border: 2px solid #C6A87D;
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(198, 168, 125, 0.2);
}

.gallery-thumb.is-selected img {
    opacity: 1;
}

/* Mobil için slider düzenlemeleri */
@media (max-width: 768px) {
    .flickity-button {
        width: 36px !important;
        height: 36px !important;
        opacity: 0.8;
    }

    .flickity-button:hover {
        opacity: 1;
    }

    .flickity-prev-next-button.previous { left: 10px; }
    .flickity-prev-next-button.next { right: 10px; }

    /* Mobilde nokta navigasyonu göster */
    .flickity-page-dots {
        bottom: 10px;
    }

    .flickity-page-dots .dot {
        width: 8px;
        height: 8px;
        margin: 0 5px;
        background: rgba(255, 255, 255, 0.8);
    }

    .flickity-page-dots .dot.is-selected {
        background: white;
    }

    /* Mobilde thumbnail grid düzenlemesi */
    .gallery-thumbs {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        padding: 0 10px;
    }
}

/* Oda bilgileri bölümü */
.room-info-section {
    padding: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 640px) {
    .room-info-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

/* Özellikler grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

@media (min-width: 640px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}

/* Rezervasyon butonu */
.reservation-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

@media (min-width: 640px) {
    .reservation-button {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Üst başlık kısmı -->
    <div class="container mx-auto px-4 pt-24 sm:pt-32 md:pt-40 pb-6 sm:pb-8">
        <!-- Header Bölümü -->
        <div class="flex items-center justify-between gap-3 sm:gap-4">
            <!-- Sol: Geri Dön Butonu -->
            <div class="w-1/4 text-left">
                <a href="javascript:history.back();" 
                   class="inline-flex items-center text-gold hover:opacity-80 transition-colors group">
                    <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1.5 transform group-hover:-translate-x-1 transition-transform" 
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    <span class="font-great-vibes text-xl sm:text-2xl md:text-3xl" data-translate="back_to_rooms">Geri</span>
                </a>
            </div>

            <!-- Orta: Oda Başlığı -->
            <div class="flex-1 text-center">
                <span class="text-gold font-great-vibes text-2xl sm:text-3xl md:text-4xl block">
                    {{ get_lang_text(room, 'title') }}
                </span>
                <div class="flex items-center justify-center mt-1.5 sm:mt-2">
                    <div class="h-[1.5px] w-4 sm:w-6 md:w-8 bg-gold"></div>
                    <div class="mx-2 text-gold text-base sm:text-lg md:text-xl">⚜</div>
                    <div class="h-[1.5px] w-4 sm:w-6 md:w-8 bg-gold"></div>
                </div>
            </div>

            <!-- Sağ: Boş alan (simetri için) -->
            <div class="w-1/4"></div>
        </div>
    </div>

    <!-- Ana slider -->
    <div class="carousel-wrapper">
        <div class="carousel" data-flickity='{ "cellAlign": "left", "contain": true, "prevNextButtons": true, "pageDots": true, "wrapAround": true, "adaptiveHeight": true }'>
            {% if room.images_list %}
                {% for image in room.images_list %}
                <div class="carousel-cell">
                    <img src="{{ url_for('static', filename='uploads/rooms/' + image) }}" alt="{{ room.title }}" class="gallery-image" data-index="{{ loop.index0 }}">
                </div>
                {% endfor %}
            {% else %}
                <div class="carousel-cell">
                    <img src="{{ url_for('static', filename='images/default-room.jpg') }}" alt="{{ room.title }}" class="gallery-image" data-index="0">
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Thumbnail grid -->
    <div class="gallery-thumbs">
        {% if room.images_list %}
            {% for image in room.images_list %}
            <div class="gallery-thumb" data-index="{{ loop.index0 }}">
                <img src="{{ url_for('static', filename='uploads/rooms/' + image) }}" alt="Thumbnail {{ loop.index }}">
            </div>
            {% endfor %}
        {% else %}
            <div class="gallery-thumb is-selected" data-index="0">
                <img src="{{ url_for('static', filename='images/default-room.jpg') }}" alt="Thumbnail 1">
            </div>
        {% endif %}
    </div>

    <!-- Alt kısım -->
    <div class="max-w-[1400px] mx-auto px-3 sm:px-4 md:px-6">
        <!-- Oda Bilgileri -->
        <div class="bg-white rounded-lg p-3 sm:p-4 md:p-6 mb-4 sm:mb-6">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-3 sm:gap-4">
                <!-- Temel Özellikler -->
                <div class="md:col-span-4 bg-gray-50/30 rounded-lg p-3 sm:p-4">
                    <div class="border-b border-gray-200 pb-2 mb-3">
                        <h3 class="text-sm sm:text-base font-medium text-gray-700" data-translate="basic_features">
                            Temel Özellikler
                        </h3>
                    </div>
                    <div class="space-y-2.5">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-ruler-combined text-gold text-sm"></i>
                            <div>
                                <p class="text-[10px] sm:text-xs text-gray-400" data-translate="room_size">Oda Boyutu</p>
                                <p class="text-sm sm:text-base font-light">{{ room.size }}m²</p>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fas fa-user-friends text-gold text-sm"></i>
                            <div>
                                <p class="text-[10px] sm:text-xs text-gray-400" data-translate="max_capacity">Maksimum Kapasite</p>
                                <p class="text-sm sm:text-base font-light">{{ room.capacity }} <span data-translate="person">Kişi</span></p>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <i class="fas fa-mountain text-gold text-sm"></i>
                            <div>
                                <p class="text-[10px] sm:text-xs text-gray-400" data-translate="view">Manzara</p>
                                <p class="text-sm sm:text-base font-light">{{ room.get_view_type_display() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Oda Olanakları -->
                {% if room.features %}
                <div class="md:col-span-8 bg-gray-50/30 rounded-lg p-3 sm:p-4">
                    <div class="border-b border-gray-200 pb-2 mb-3">
                        <h3 class="text-sm sm:text-base font-medium text-gray-700" data-translate="room_features">
                            Oda Olanakları
                        </h3>
                    </div>
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                        {% for feature in room.features %}
                        <div class="flex items-center gap-2 p-2 bg-white/50 rounded-lg">
                            <i class="{{ feature.icon }} text-gold text-xs sm:text-sm"></i>
                            <span class="text-gray-600 text-xs sm:text-sm">{{ feature.name }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Rezervasyon Butonu -->
        <div class="text-center mb-6 sm:mb-8">
            <a href="{{ settings.reservation_link or '#' }}" 
               onclick="countReservationClick(event)"
               class="inline-flex items-center justify-center px-5 sm:px-6 py-2.5 sm:py-3 bg-gold hover:bg-gold/90 text-white 
                      rounded-full transition-all duration-300 group gap-2 text-xs sm:text-sm tracking-wider font-medium">
                <i class="fas fa-concierge-bell text-sm sm:text-base"></i>
                <span data-translate="make_reservation">REZERVASYON YAP</span>
                <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 transform group-hover:translate-x-1 transition-transform" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
            </a>
        </div>

        <!-- Oda Açıklaması -->
        <div class="bg-white rounded-lg shadow-sm p-3 sm:p-4 md:p-6 mb-8 sm:mb-12">
            <div class="border-b border-gray-200 pb-2 mb-3">
                <h3 class="text-sm sm:text-base font-medium text-gray-700" data-translate="about_room">
                    Oda Hakkında
                </h3>
            </div>
            <div class="prose max-w-none prose-sm sm:prose-base">
                {{ get_lang_text(room, 'description')|safe }}
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div id="imageModal" class="modal">
    <span class="modal-close" onclick="closeModal()">&times;</span>
    
    <!-- Sol Ok -->
    <button class="modal-nav modal-prev" onclick="changeImage(-1)">
        <i class="fas fa-chevron-left"></i>
    </button>
    
    <img id="modalImage" src="" alt="Büyük Görsel" style="max-height: 90vh; max-width: 90vw; object-fit: contain;">
    
    <!-- Sağ Ok -->
    <button class="modal-nav modal-next" onclick="changeImage(1)">
        <i class="fas fa-chevron-right"></i>
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ana slider'ı başlat
    var flkty = new Flickity('.carousel', {
        cellAlign: 'left',
        contain: true,
        wrapAround: true,
        autoPlay: 3000,
        pauseAutoPlayOnHover: true,
        prevNextButtons: true,
        pageDots: false
    });

    // Thumbnail'ları seç
    const thumbnails = document.querySelectorAll('.gallery-thumb');
    
    // İlk thumbnail'ı seçili yap
    thumbnails[0]?.classList.add('is-selected');

    // Thumbnail tıklama işlemleri
    thumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', function(e) {
            // Slider'ı seçilen resme getir
            flkty.select(index);
            
            // Çift tıklama kontrolü için
            if (e.detail === 2) {
                // Modal'ı aç
                openModal(this.dataset.image);
            }
        });
    });

    // Ana slider'daki resimlere tıklama işlemi ekle
    document.querySelectorAll('.carousel-cell img').forEach(img => {
        img.addEventListener('click', function() {
            openModal(this.src);
        });
    });

    // Slider değiştiğinde thumbnail'ı güncelle
    flkty.on('change', function(index) {
        // Tüm thumbnail'lardan seçili sınıfını kaldır
        thumbnails.forEach(thumb => thumb.classList.remove('is-selected'));
        // Aktif thumbnail'a seçili sınıfını ekle
        thumbnails[index]?.classList.add('is-selected');
    });

    // Otomatik geçişte de thumbnail'ı güncelle
    flkty.on('select', function() {
        thumbnails.forEach(thumb => thumb.classList.remove('is-selected'));
        thumbnails[flkty.selectedIndex]?.classList.add('is-selected');
    });

    // Geri butonuna tıklandığında
    document.querySelector('a[href="javascript:history.back();"]').addEventListener('click', function(e) {
        e.preventDefault();
        
        // Önceki sayfa varsa oraya git
        if (document.referrer) {
            window.history.back();
        } else {
            // Yoksa kategori veya odalar sayfasına yönlendir
            const roomCategoryUrl = '{{ url_for("rooms.rooms_by_category", slug=room.room_category.slug) if room.room_category else "" }}';
            const roomIndexUrl = '{{ url_for("rooms.room_index") }}';
            
            window.location.href = roomCategoryUrl || roomIndexUrl;
        }
    });
});

function openModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    modal.classList.add('active');
    modalImg.src = imageSrc;
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    const modal = document.getElementById('imageModal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function changeImage(direction) {
    const flkty = document.querySelector('.carousel');
    const selectedIndex = flkty.selectedIndex;
    const newIndex = (selectedIndex + direction + flkty.cells.length) % flkty.cells.length;
    flkty.select(newIndex);
}

// ESC tuşu ile modalı kapatma ve yön tuşlarıyla gezinme
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    } else if (e.key === 'ArrowLeft') {
        changeImage(-1);
    } else if (e.key === 'ArrowRight') {
        changeImage(1);
    }
});

// Rezervasyon tıklama sayacı
function countReservationClick(event) {
    // AJAX isteği gönder
    fetch('/reservation/click', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
    }).then(response => {
        // Başarılı olsa da olmasa da bir şey yapmaya gerek yok
        console.log('Rezervasyon tıklama kaydedildi');
    }).catch(error => {
        console.error('Rezervasyon tıklama hatası:', error);
    });
}
</script>
{% endblock content %} 