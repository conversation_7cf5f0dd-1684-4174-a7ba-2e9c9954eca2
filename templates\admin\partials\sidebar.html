<!-- Sidebar Links -->
<nav class="mt-6">
    <!-- <PERSON><PERSON> -->
    <a href="{{ url_for('admin.dashboard') }}" 
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'admin.dashboard' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-home w-6"></i>
        <span><PERSON><PERSON></span>
    </a>

    <!-- Menü -->
    <a href="{{ url_for('menu.menu_list') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'menu.menu_list' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-bars w-6"></i>
        <span>Menü</span>
    </a>

    <!-- <PERSON>da Müsaitlik Durumu -->
    <a href="{{ url_for('room_settings.room_availability') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'room_settings.room_availability' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-calendar-alt w-6"></i>
        <span>Oda Müsaitliği</span>
    </a>
    
    <!-- Rezervasyonlar -->
    <a href="{{ url_for('reservation.user_reservations') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'reservation.user_reservations' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-bookmark w-6"></i>
        <span>Rezervasyonlar</span>
    </a>
    
    <!-- Ödemeler -->
    <a href="{{ url_for('reservation.payment_list') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'reservation.payment_list' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-credit-card w-6"></i>
        <span>Ödemeler</span>
    </a>

    <!-- Sunucu İzleme -->
    <a href="{{ url_for('server_bp.server_info') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'server_bp.server_info' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-server w-6"></i>
        <span>Sunucu Durumu</span>
    </a>
    
    <!-- Ülke Bazlı Ziyaretçi İstatistikleri -->
    <a href="{{ url_for('server_bp.visitor_stats_page') }}"
       class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded-lg transition-colors {% if request.endpoint == 'server_bp.visitor_stats_page' %}bg-gray-100 text-gray-800{% endif %}">
        <i class="fas fa-globe w-6"></i>
        <span>Ülke İstatistikleri</span>
    </a>
</nav> 