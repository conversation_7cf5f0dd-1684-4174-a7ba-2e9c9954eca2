<!-- Google Fonts - Inter -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">

<!-- Google Fonts - Poppins -->
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">

<!-- Sidebar -->
<div class="w-72 min-h-screen shadow-2xl flex flex-col font-['Poppins'] bg-gradient-to-b from-gray-800 to-gray-900 border-r border-gray-700">
    <!-- Logo ve Başlık Alanı -->
    <div class="px-6 py-5 border-b border-gray-700/50 bg-gray-800/50">
        <div class="flex items-center space-x-4">
            <div class="relative group">
                <div class="absolute -inset-1 bg-gradient-to-r from-gold/40 to-amber-400/40 rounded-xl blur-sm opacity-50 group-hover:opacity-75 transition duration-300"></div>
                <div class="w-12 h-12 bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700 rounded-xl flex items-center justify-center shadow-xl relative transform group-hover:scale-105 transition-transform duration-200">
                    <i class="fas fa-gem text-gold text-lg"></i>
                </div>
            </div>
            <div class="flex flex-col -space-y-0.5">
                <h1 class="text-base font-semibold text-white tracking-wide">Zeppelin Admin</h1>
                <span class="text-xs text-slate-400 font-light">v3.0 Premium</span>
            </div>
        </div>
    </div>

    <!-- Ana Menü -->
    <nav class="flex-1 overflow-y-auto custom-scrollbar px-4 py-6 space-y-3">
        <!-- Açılır menü grubu için makro -->
        {% macro dropdown_menu_group(title, icon, id, active_namespaces=[]) %}
        {% set current_namespace = request.endpoint.split('.')[0] %}
        {% set is_active = current_namespace in active_namespaces %}
        <div class="space-y-2">
            <button onclick="toggleDropdown('{{ id }}')"
                    class="w-full group flex items-center justify-between px-4 py-3 text-sm font-medium text-slate-300 rounded-lg
                           transition-transform duration-150 ease-out relative menu-item-glow
                           {% if is_active %} bg-gold/20 text-white shadow-inner transform scale-[1.02] border-l-4 border-gold {% else %} hover:bg-slate-700/50 hover:scale-[1.01] active:scale-[0.99] {% endif %}">
                <div class="flex items-center">
                    <i class="{{ icon }} w-5 mr-3 text-slate-400 group-hover:text-gold transition-colors {% if is_active %} text-gold {% endif %}"></i>
                    <span class="group-hover:text-slate-100 transition-colors font-light {% if is_active %} text-white {% endif %}">{{ title }}</span>
                </div>
                <i class="fas fa-chevron-down text-xs text-slate-400 group-hover:text-gold transition-all duration-300 transform {% if is_active %} rotate-180 {% endif %}" id="arrow-{{ id }}"></i>
            </button>
            <div id="{{ id }}" class="pl-4 space-y-1.5 overflow-hidden transition-all duration-300 mt-1 {% if not is_active %}hidden{% endif %}" {% if not is_active %}style="max-height: 0px;"{% endif %}>
                {{ caller() }}
            </div>
        </div>
        {% endmacro %}

        <!-- Tek menü öğesi için makro -->
        {% macro single_menu_item(url, icon, text, endpoint, badge=None) %}
        <a href="{{ url }}"
           class="group flex items-center px-4 py-3 text-sm font-light text-slate-300 rounded-lg
                  transition-transform duration-150 ease-out hover:bg-slate-700/50 relative menu-item-glow
                  {% if request.endpoint == endpoint %} bg-gold/20 text-white shadow-inner transform scale-[1.02] border-l-4 border-gold {% else %} hover:scale-[1.02] active:scale-[0.98] {% endif %}">
            <i class="{{ icon }} w-5 mr-3 text-slate-400 group-hover:text-gold transition-colors {% if request.endpoint == endpoint %} text-gold {% endif %}"></i>
            <span class="flex-1 group-hover:text-white transition-colors">{{ text }}</span>
            {% if badge %}
            <span class="px-2 py-0.5 text-[10px] font-medium bg-gold/20 text-gold rounded-full border border-gold/30">
                {{ badge }}
            </span>
            {% endif %}
        </a>
        {% endmacro %}

        <!-- Alt menü öğesi için makro -->
        {% macro sub_menu_item(url, icon, text, endpoint, badge=None) %}
        <a href="{{ url }}"
           class="group flex items-center px-3 py-2.5 text-sm font-light text-slate-400 rounded-lg
                  transition-transform duration-150 ease-out hover:bg-slate-700/50 relative border-l-2 border-transparent
                  {% if request.endpoint == endpoint %} bg-gold/20 text-white border-gold shadow-inner transform scale-[1.01] border-l-4 border-gold {% else %} hover:border-gold/50 hover:scale-[1.01] active:scale-[0.99] {% endif %}">
            <i class="{{ icon }} w-4 mr-3 text-slate-500 group-hover:text-gold transition-colors text-xs {% if request.endpoint == endpoint %} text-gold {% endif %}"></i>
            <span class="flex-1 group-hover:text-white transition-colors text-xs">{{ text }}</span>
            {% if badge %}
            <span class="px-1.5 py-0.5 text-[9px] font-medium bg-gold/20 text-gold rounded-full border border-gold/30">
                {{ badge }}
            </span>
            {% endif %}
        </a>
        {% endmacro %}

        <!-- Dashboard - Tek öğe -->
        <div class="group-separator">
            {{ single_menu_item(url_for('admin.dashboard'), 'fas fa-home', 'Genel Bakış', endpoint='admin.dashboard') }}
        </div>

        <!-- İçerik Yönetimi -->
        <div class="group-separator">
            {% call dropdown_menu_group('İçerik Yönetimi', 'fas fa-file-alt', 'content-menu', active_namespaces=['pages', 'blog', 'menu']) %}
                {{ sub_menu_item(url_for('pages.page_list'), 'fas fa-file-alt', 'Sayfalar', endpoint='pages.page_list') }}
                {{ sub_menu_item(url_for('blog.blog_list'), 'fas fa-blog', 'Blog', endpoint='blog.blog_list') }}
                {{ sub_menu_item(url_for('menu.menu_list'), 'fas fa-bars', 'Menü', endpoint='menu.menu_list') }}
            {% endcall %}
        </div>

        <!-- Medya Yönetimi -->
        <div class="group-separator">
            {% call dropdown_menu_group('Medya Yönetimi', 'fas fa-photo-video', 'media-menu', active_namespaces=['slider', 'banner', 'gallery']) %}
                {{ sub_menu_item(url_for('slider.slider_list'), 'fas fa-images', 'Slider', endpoint='slider.slider_list') }}
                {{ sub_menu_item(url_for('banner.banner_list'), 'fas fa-image', 'Banner', endpoint='banner.banner_list') }}
                {{ sub_menu_item(url_for('gallery.admin_gallery_list'), 'fas fa-photo-video', 'Galeri', endpoint='gallery.admin_gallery_list') }}
            {% endcall %}
        </div>

        <!-- Otel İşlemleri -->
        <div class="group-separator">
            {% call dropdown_menu_group('Otel İşlemleri', 'fas fa-hotel', 'hotel-menu', active_namespaces=['rooms', 'room_settings', 'activities', 'reservation']) %}
                {{ sub_menu_item(url_for('rooms.room_list'), 'fas fa-bed', 'Odalar', endpoint='rooms.room_list') }}
                {{ sub_menu_item(url_for('room_settings.category_list'), 'fas fa-th-large', 'Oda Kategorileri', endpoint='room_settings.category_list') }}
                {{ sub_menu_item(url_for('room_settings.feature_list'), 'fas fa-list-ul', 'Oda Özellikleri', endpoint='room_settings.feature_list') }}
                {{ sub_menu_item(url_for('room_settings.room_availability'), 'fas fa-calendar-alt', 'Oda Müsaitliği', endpoint='room_settings.room_availability', badge='YENİ') }}
                {{ sub_menu_item(url_for('activities.activity_list'), 'fas fa-mountain', 'Aktiviteler', endpoint='activities.activity_list') }}
                {{ sub_menu_item(url_for('reservation.reservation_list'), 'fas fa-calendar-check', 'Rezervasyonlar', endpoint='reservation.reservation_list') }}
                {{ sub_menu_item(url_for('reservation.admin_user_reservations'), 'fas fa-bookmark', 'Rezervasyon Listesi', endpoint='reservation.admin_user_reservations', badge='YENİ') }}
                {{ sub_menu_item(url_for('reservation.admin_payment_list'), 'fas fa-credit-card', 'Ödemeler', endpoint='reservation.admin_payment_list', badge='YENİ') }}
            {% endcall %}
        </div>

        <!-- Restoran Yönetimi -->
        <div class="group-separator">
            {% call dropdown_menu_group('Restoran Yönetimi', 'fas fa-utensils', 'restaurant-menu', active_namespaces=['admin_food']) %}
                {{ sub_menu_item(url_for('admin_food.category_list'), 'fas fa-list', 'Kategoriler', endpoint='admin_food.category_list') }}
                {{ sub_menu_item(url_for('admin_food.food_list'), 'fas fa-utensils', 'Yemekler', endpoint='admin_food.food_list') }}
                {{ sub_menu_item(url_for('admin_food.order_list'), 'fas fa-receipt', 'Siparişler', endpoint='admin_food.order_list',
                            badge=active_orders if active_orders and active_orders > 0 else None) }}
            {% endcall %}
        </div>

        <!-- Pazarlama & SEO -->
        <div class="group-separator">
            {% call dropdown_menu_group('Pazarlama & SEO', 'fas fa-bullhorn', 'marketing-menu', active_namespaces=['seo', 'admin_promotions', 'admin_sponsors']) %}
                {{ sub_menu_item(url_for('seo.seo_list'), 'fas fa-search', 'SEO Ayarları', endpoint='seo.seo_list') }}
                {{ sub_menu_item(url_for('seo.keyword_analytics'), 'fas fa-chart-line', 'Kelime Analizi', endpoint='seo.keyword_analytics') }}
                {{ sub_menu_item(url_for('seo.keyword_generator'), 'fas fa-magic', 'Kelime Üretici', badge='AI', endpoint='seo.keyword_generator') }}
                {{ sub_menu_item(url_for('admin_promotions.promotion_list'), 'fas fa-tags', 'Promosyonlar', badge='NEW', endpoint='admin_promotions.promotion_list') }}
                {{ sub_menu_item(url_for('admin_sponsors.sponsor_list'), 'fas fa-handshake', 'Sponsorlar', endpoint='admin_sponsors.sponsor_list') }}
            {% endcall %}
        </div>

        <!-- Entegrasyon -->
        <div class="group-separator">
            {% call dropdown_menu_group('Entegrasyon', 'fas fa-plug', 'integration-menu', active_namespaces=['integration', 'whatsapp', 'seo', 'indexing_status']) %}
                {{ sub_menu_item(url_for('integration.payment_settings'), 'fas fa-credit-card', 'Ödeme Sistemleri', badge='YENİ', endpoint='integration.payment_settings') }}
                {{ sub_menu_item(url_for('integration.api_keys'), 'fas fa-key', 'API Anahtarları', endpoint='integration.api_keys') }}
                {{ sub_menu_item(url_for('integration.google_analytics'), 'fab fa-google', 'Google Analytics', badge='NEW', endpoint='integration.google_analytics') }}
                {{ sub_menu_item(url_for('seo.google_console'), 'fab fa-google', 'Google Console', badge='NEW', endpoint='seo.google_console') }}
                {{ sub_menu_item(url_for('indexing_status'), 'fas fa-search-plus', 'Indexing Durumu', badge='LIVE', endpoint='indexing_status') }}
                {{ sub_menu_item(url_for('whatsapp.whatsapp_edit'), 'fab fa-whatsapp', 'WhatsApp', endpoint='whatsapp.whatsapp_edit') }}
            {% endcall %}
        </div>

        <!-- Sistem Ayarları -->
        <div class="group-separator">
            {% call dropdown_menu_group('Sistem Ayarları', 'fas fa-cogs', 'system-menu', active_namespaces=['users', 'settings', 'footer', 'language', 'cache', 'security', 'server', 'under_construction']) %}
                {{ sub_menu_item(url_for('users.user_list'), 'fas fa-users', 'Kullanıcılar', endpoint='users.user_list') }}
                {{ sub_menu_item(url_for('settings.settings_edit'), 'fas fa-cog', 'Genel Ayarlar', endpoint='settings.settings_edit') }}
                {{ sub_menu_item(url_for('settings.ip_settings'), 'fas fa-network-wired', 'IP Ayarları', endpoint='settings.ip_settings') }}
                {{ sub_menu_item(url_for('footer.footer_edit'), 'fas fa-shoe-prints', 'Footer Ayarları', endpoint='footer.footer_edit') }}
                {{ sub_menu_item(url_for('language.language_list'), 'fas fa-language', 'Dil Yönetimi', endpoint='language.language_list') }}
                {{ sub_menu_item(url_for('cache.cache_dashboard'), 'fas fa-memory', 'Cache Yönetimi', endpoint='cache.cache_dashboard') }}
                {{ sub_menu_item(url_for('security.security_dashboard'), 'fas fa-shield-alt', 'Güvenlik İzleme', endpoint='security.security_dashboard') }}
                {{ sub_menu_item(url_for('server_bp.server_info'), 'fas fa-server', 'Sunucu İzleme', endpoint='server_bp.server_info') }}
                {{ sub_menu_item(url_for('server_bp.visitor_stats_page'), 'fas fa-globe', 'Ülke İstatistikleri', endpoint='server_bp.visitor_stats_page', badge='YENİ') }}
                {{ sub_menu_item(url_for('under_construction.list'), 'fas fa-hard-hat', 'Yapım Aşaması', badge='Beta', endpoint='under_construction.list') }}
            {% endcall %}
        </div>
    </nav>

    <!-- Çıkış -->
    <div class="border-t border-gray-700/50 p-4 bg-gray-800/50 mt-auto">
        <a href="{{ url_for('logout') }}"
           class="group flex items-center px-4 py-3 text-sm text-gray-400 rounded-lg
                  transition-all duration-200 hover:bg-red-600/20 relative">
            <i class="fas fa-sign-out-alt w-5 mr-3 text-gray-400 group-hover:text-red-400 transition-colors"></i>
            <span class="group-hover:text-white transition-colors font-light">Çıkış Yap</span>
        </a>
    </div>
</div>

<style>
/* Özel scrollbar stilleri */
.custom-scrollbar::-webkit-scrollbar {
    width: 3px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(198, 168, 125, 0.3); /* gold color */
    border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(198, 168, 125, 0.5);
}

/* Hover efektleri */
@keyframes goldGlow {
    0% { box-shadow: 0 0 0 0 rgba(198, 168, 125, 0.2); }
    50% { box-shadow: 0 0 12px 4px rgba(198, 168, 125, 0.2); }
    100% { box-shadow: 0 0 0 0 rgba(198, 168, 125, 0.2); }
}

.menu-item-glow:hover {
    animation: goldGlow 2s infinite;
}

/* Dropdown animasyonları */
.dropdown-enter {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
}

.dropdown-enter-active {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-exit {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-exit-active {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

/* Gold accent border */
.gold-border {
    border-left: 3px solid #C6A87D;
}

.group-separator:not(:first-child) {
    padding-top: 1rem; /* Adjust as needed */
    margin-top: 1rem; /* Adjust as needed */
    border-top: 1px solid rgba(255, 255, 255, 0.08); /* Light separator line */
}
</style>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    const arrow = document.getElementById(`arrow-${id}`);
    const isCurrentlyHidden = dropdown.classList.contains('hidden');

    if (isCurrentlyHidden) {
        // Dropdown'ı aç
        dropdown.classList.remove('hidden');
        // DOM'un güncellenmesini bekle, sonra animasyonu başlat
        requestAnimationFrame(() => {
            dropdown.style.maxHeight = dropdown.scrollHeight + 'px';
            arrow.classList.add('rotate-180');
        });
    } else {
        // Dropdown'ı kapat
        dropdown.style.maxHeight = '0px';
        arrow.classList.remove('rotate-180');
        // Animasyon bittiğinde 'hidden' class'ını ekle
        dropdown.addEventListener('transitionend', function handler() {
            if (dropdown.style.maxHeight === '0px') {
                dropdown.classList.add('hidden');
            }
            dropdown.removeEventListener('transitionend', handler);
        }, { once: true });
    }
}

// Sayfa yüklendiğinde aktif olan menü grubunun açık kalmasını sağla
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[id$="-menu"]:not(.hidden)').forEach(dropdown => {
        if (dropdown.scrollHeight > 0) {
            dropdown.style.maxHeight = dropdown.scrollHeight + 'px';
        }
    });
});
</script>