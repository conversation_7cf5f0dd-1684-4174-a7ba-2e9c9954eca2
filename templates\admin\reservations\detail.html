{% extends "admin/base.html" %}

{% block breadcrumb %}Rezervasyonlar / Detay{% endblock %}
{% block page_title %}Rezervasyon #{{ reservation.id }}{% endblock %}
{% block page_subtitle %}Rezervasyon ve ödeme detayları{% endblock %}

{% block admin_content %}
<div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-5xl mx-auto">
        <!-- Durum Kartı -->
        <div class="mb-6">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-5 border-b flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-800">Rezervasyon Durumu</h2>
                    
                    <div class="flex space-x-3">
                        <!-- Rezervasyon Durumu -->
                        <span class="px-3 py-1 rounded-full text-xs font-medium
                            {% if reservation.status == 'confirmed' %}bg-green-100 text-green-800
                            {% elif reservation.status == 'cancelled' %}bg-red-100 text-red-800
                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                            {% if reservation.status == 'confirmed' %}Onaylandı
                            {% elif reservation.status == 'cancelled' %}İptal Edildi
                            {% else %}Beklemede{% endif %}
                        </span>
                        
                        <!-- Ödeme Durumu -->
                        <span class="px-3 py-1 rounded-full text-xs font-medium
                            {% if reservation.payment_status == 'paid' %}bg-green-100 text-green-800
                            {% elif reservation.payment_status == 'failed' %}bg-red-100 text-red-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {% if reservation.payment_status == 'paid' %}Ödendi
                            {% elif reservation.payment_status == 'failed' %}Ödeme Başarısız
                            {% elif reservation.payment_status == 'processing' %}İşlemde
                            {% else %}Ödeme Bekliyor{% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="p-5">
                    <form action="{{ url_for('reservation.update_status', id=reservation.id) }}" method="POST" class="flex items-center space-x-3">
                        <select name="status" class="form-select">
                            <option value="pending" {% if reservation.status == 'pending' %}selected{% endif %}>Beklemede</option>
                            <option value="confirmed" {% if reservation.status == 'confirmed' %}selected{% endif %}>Onaylandı</option>
                            <option value="cancelled" {% if reservation.status == 'cancelled' %}selected{% endif %}>İptal Edildi</option>
                        </select>
                        <button type="submit" class="btn btn-primary">Durumu Güncelle</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Müşteri Bilgileri -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-5 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">Müşteri Bilgileri</h2>
                    </div>
                    <div class="p-5">
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">İsim</label>
                                <p class="font-medium">{{ reservation.name }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">E-posta</label>
                                <p class="font-medium">{{ reservation.email }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Telefon</label>
                                <p class="font-medium">{{ reservation.phone }}</p>
                            </div>
                            
                            {% if reservation.message %}
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Not</label>
                                <p class="text-gray-700">{{ reservation.message }}</p>
                            </div>
                            {% endif %}
                            
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Oluşturma Tarihi</label>
                                <p class="font-medium">{{ reservation.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Rezervasyon Detayları -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-5 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">Rezervasyon Detayları</h2>
                    </div>
                    <div class="p-5">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Giriş Tarihi</label>
                                    <p class="font-medium">{{ reservation.check_in.strftime('%d.%m.%Y') }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Çıkış Tarihi</label>
                                    <p class="font-medium">{{ reservation.check_out.strftime('%d.%m.%Y') }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Misafir Sayısı</label>
                                    <p class="font-medium">{{ reservation.guests }} kişi</p>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Oda Tipi</label>
                                    <p class="font-medium">{{ reservation.room_type }}</p>
                                </div>
                                
                                {% if reservation.room %}
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Oda</label>
                                    <p class="font-medium">{{ reservation.room.title_tr }}</p>
                                </div>
                                {% endif %}
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Geceleme</label>
                                    {% set nights = (reservation.check_out - reservation.check_in).days %}
                                    <p class="font-medium">{{ nights }} gece</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if reservation.room %}
                        <!-- Oda Görseli -->
                        <div class="mt-6">
                            {% if reservation.room.gallery_images %}
                            <div class="w-full h-56 rounded-lg overflow-hidden">
                                <img src="/static/uploads/rooms/{{ reservation.room.gallery_images.split(',')[0] }}" 
                                     alt="{{ reservation.room.title_tr }}"
                                     class="w-full h-full object-cover">
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Ödeme Bilgileri -->
                {% if reservation.payment_transaction %}
                <div class="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="p-5 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">Ödeme Bilgileri</h2>
                    </div>
                    <div class="p-5">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Ödeme Yöntemi</label>
                                    <p class="font-medium">{{ reservation.payment_transaction.payment_method.name }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">İşlem Numarası</label>
                                    <p class="font-medium">{{ reservation.payment_transaction.transaction_id or 'Belirtilmemiş' }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Sipariş Numarası</label>
                                    <p class="font-medium">{{ reservation.payment_transaction.order_id }}</p>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Tutar</label>
                                    <p class="font-medium">{{ reservation.payment_transaction.amount }} {{ reservation.payment_transaction.currency }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">İşlem Tarihi</label>
                                    <p class="font-medium">{{ reservation.payment_transaction.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm text-gray-600 mb-1">Durum</label>
                                    <p class="font-medium 
                                        {% if reservation.payment_transaction.status.value == 'success' %}text-green-600
                                        {% elif reservation.payment_transaction.status.value == 'failed' %}text-red-600
                                        {% else %}text-yellow-600{% endif %}">
                                        {% if reservation.payment_transaction.status.value == 'success' %}Başarılı
                                        {% elif reservation.payment_transaction.status.value == 'failed' %}Başarısız
                                        {% elif reservation.payment_transaction.status.value == 'refunded' %}İade Edildi
                                        {% else %}Beklemede{% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        {% if reservation.payment_transaction.error_message %}
                        <div class="mt-6 p-4 bg-red-50 border border-red-100 rounded-md">
                            <h4 class="text-sm font-medium text-red-800 mb-1">Hata Mesajı:</h4>
                            <p class="text-red-700">{{ reservation.payment_transaction.error_message }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Butonlar -->
        <div class="mt-6 flex justify-end space-x-3">
            <a href="{{ url_for('reservation.reservation_list') }}" class="btn btn-secondary">
                Listeye Dön
            </a>
            
            {% if reservation.status != 'cancelled' %}
            <a href="{{ url_for('reservation.new_reservation') }}?room_id={{ reservation.room_id }}" class="btn btn-primary">
                Yeni Rezervasyon
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 