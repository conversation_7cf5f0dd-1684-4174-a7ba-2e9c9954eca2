from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, g
from flask_login import login_required
from werkzeug.utils import secure_filename
from models.page import db, Page
from models.menu import Menu
import os
from datetime import datetime
from slugify import slugify
from models.setting import Setting

pages_bp = Blueprint('pages', __name__)

def save_image(file):
    if file and allowed_file(file.filename):
        try:
            filename = secure_filename(file.filename)
            base, ext = os.path.splitext(filename)
            filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
            
            # Uploads/pages klasörünü kontrol et ve oluştur
            uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'pages')
            if not os.path.exists(uploads_dir):
                os.makedirs(uploads_dir, exist_ok=True)
            
            file_path = os.path.join(uploads_dir, filename)
            file.save(file_path)
            
            # Dosya kaydedildi mi kontrol et
            if os.path.exists(file_path):
                return filename
            
            current_app.logger.error(f"Dosya kaydedilemedi: {file_path}")
            return None
            
        except Exception as e:
            current_app.logger.error(f"Resim kaydetme hatası: {str(e)}")
            return None
            
    return None

# Sayfa görüntüleme route'u
@pages_bp.route('/<slug>')
def page_view(slug):
    try:
        page = Page.query.filter_by(slug=slug, status='published').first_or_404()
        menus = Menu.query.filter_by(active=True).order_by(Menu.order).all()
        settings = {setting.key: setting.value for setting in Setting.query.all()}
        current_language = g.get('language', 'tr')  # Dil bilgisini al
        
        return render_template('page.html', 
                            page=page,
                            menus=menus, 
                            settings=settings,
                            current_language=current_language,  # Dil bilgisini gönder
                            error=None)
                            
    except Exception as e:
        current_app.logger.error(f"Sayfa görüntüleme hatası: {str(e)}")
        menus = Menu.query.filter_by(active=True).order_by(Menu.order).all()
        settings = {setting.key: setting.value for setting in Setting.query.all()}
        return render_template('page.html', 
                             page=None,
                             error="Sayfa bulunamadı veya bir hata oluştu.",
                             menus=menus,
                             settings=settings), 404

@pages_bp.route('/admin/pages')
@login_required
def page_list():
    pages = Page.query.order_by(Page.created_at.desc()).all()
    current_language = g.get('language', 'tr')
    return render_template('admin/pages/list.html', 
                         pages=pages,
                         current_language=current_language)

@pages_bp.route('/admin/pages/create', methods=['GET', 'POST'])
@login_required
def page_create():
    if request.method == 'POST':
        try:
            page = Page(
                title_tr=request.form.get('title_tr'),
                content_tr=request.form.get('content_tr'),
                title_en=request.form.get('title_en'),
                content_en=request.form.get('content_en'),
                meta_description=request.form.get('meta_description'),
                status=request.form.get('status', 'published'),  # Varsayılan olarak published
                is_homepage=bool(request.form.get('is_homepage')),
                slug=request.form.get('slug') or slugify(request.form.get('title_tr'))
            )
            
            # Görsel yükleme işlemi
            if 'featured_image' in request.files:
                file = request.files['featured_image']
                if file and file.filename:
                    filename = save_image(file)
                    if filename:
                        page.featured_image = filename

            db.session.add(page)
            db.session.commit()
            flash('Sayfa başarıyla oluşturuldu!', 'success')
            return redirect(url_for('pages.page_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/pages/create.html')

@pages_bp.route('/admin/pages/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def page_edit(id):
    page = Page.query.get_or_404(id)
    current_language = g.get('language', 'tr')
    
    if request.method == 'POST':
        try:
            page.title_tr = request.form.get('title_tr')
            page.content_tr = request.form.get('content_tr')
            page.title_en = request.form.get('title_en')
            page.content_en = request.form.get('content_en')
            page.meta_description = request.form.get('meta_description')
            page.status = request.form.get('status', 'published')
            page.is_homepage = bool(request.form.get('is_homepage'))
            page.slug = request.form.get('slug') or slugify(request.form.get('title_tr'))

            if 'featured_image' in request.files:
                file = request.files['featured_image']
                if file and file.filename:
                    filename = save_image(file)
                    if filename:
                        page.featured_image = filename

            db.session.commit()
            flash('Sayfa başarıyla güncellendi!', 'success')
            return redirect(url_for('pages.page_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')

    return render_template('admin/pages/edit.html', 
                         page=page,
                         current_language=current_language)

@pages_bp.route('/admin/pages/delete/<int:id>', methods=['POST'])
@login_required
def page_delete(id):
    page = Page.query.get_or_404(id)
    
    try:
        # Eğer sayfanın görseli varsa, onu da sil
        if page.featured_image:
            file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'pages', page.featured_image)
            if os.path.exists(file_path):
                os.remove(file_path)
        
        db.session.delete(page)
        db.session.commit()
        flash('Sayfa başarıyla silindi!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('pages.page_list'))

@pages_bp.route('/admin/upload-image', methods=['POST'])
@login_required
def upload_image():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        filename = save_image(file)
        file_url = url_for('static', filename=f'uploads/pages/{filename}', _external=True)
        return jsonify({'location': file_url})
    
    return jsonify({'error': 'Invalid file type'}), 400

def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@pages_bp.route('/pages/<int:id>')
def page_detail_id(id):
    page = Page.query.get_or_404(id)
    return redirect(url_for('pages.page_view', slug=page.slug), code=301) 