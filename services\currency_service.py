import requests
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
import logging
from flask import current_app
from models.setting import db

class CurrencyRate(db.Model):
    """Döviz kuru tablosu"""
    id = db.Column(db.Integer, primary_key=True)
    currency_code = db.Column(db.String(3), nullable=False)  # USD, EUR, GBP
    buying_rate = db.Column(db.Float, nullable=False)
    selling_rate = db.Column(db.Float, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<CurrencyRate {self.currency_code}: {self.selling_rate}>'

class CurrencyService:
    """TCMB Döviz Kuru Servis Sınıfı"""
    
    TCMB_URL = "https://www.tcmb.gov.tr/kurlar/today.xml"
    
    @classmethod
    def fetch_currency_rates(cls):
        """TCMB'den güncel kurları çeker"""
        try:
            response = requests.get(cls.TCMB_URL)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            
            rates = {}
            for currency in root.findall(".//Currency"):
                code = currency.get('CurrencyCode')
                if code in ['USD', 'EUR', 'GBP']:  # Sadece USD, EUR ve GBP takip ediliyor
                    buying_rate = float(currency.find('ForexBuying').text.replace(',', '.'))
                    selling_rate = float(currency.find('ForexSelling').text.replace(',', '.'))
                    rates[code] = {
                        'buying_rate': buying_rate,
                        'selling_rate': selling_rate
                    }
            
            return rates
        
        except Exception as e:
            logging.error(f"Döviz kuru çekme hatası: {str(e)}")
            return {}

    @classmethod
    def update_db_rates(cls):
        """Döviz kurlarını günceller ve veritabanına kaydeder"""
        rates = cls.fetch_currency_rates()
        if not rates:
            return False
            
        try:
            for code, rate_data in rates.items():
                # Mevcut kur kaydını ara
                currency = CurrencyRate.query.filter_by(currency_code=code).first()
                
                if currency:
                    # Varsa güncelle
                    currency.buying_rate = rate_data['buying_rate']
                    currency.selling_rate = rate_data['selling_rate']
                    currency.updated_at = datetime.utcnow()
                else:
                    # Yoksa yeni kayıt oluştur
                    currency = CurrencyRate(
                        currency_code=code,
                        buying_rate=rate_data['buying_rate'],
                        selling_rate=rate_data['selling_rate']
                    )
                    db.session.add(currency)
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            logging.error(f"Döviz kuru kaydetme hatası: {str(e)}")
            return False
    
    @classmethod
    def get_latest_rates(cls, refresh=False):
        """En güncel döviz kurlarını getirir, eğer güncellenmesi gerekiyorsa günceller"""
        # Son güncelleme zamanını kontrol et
        last_update = CurrencyRate.query.order_by(CurrencyRate.updated_at.desc()).first()
        
        # Eğer hiç kayıt yoksa veya son güncellemeden bu yana 24 saat geçtiyse ya da refresh istendiyse
        if refresh or not last_update or (datetime.utcnow() - last_update.updated_at > timedelta(hours=24)):
            cls.update_db_rates()
        
        # Güncel kurları getir
        rates = {}
        for currency in CurrencyRate.query.all():
            rates[currency.currency_code] = {
                'buying_rate': currency.buying_rate,
                'selling_rate': currency.selling_rate,
                'updated_at': currency.updated_at
            }
        
        return rates
    
    @classmethod
    def convert_currency(cls, amount, from_currency, to_currency):
        """Bir para biriminden diğerine dönüşüm yapar"""
        rates = cls.get_latest_rates()
        
        # Eğer TL'den dönüşüm yapılıyorsa
        if from_currency == 'TRY':
            if to_currency == 'TRY':
                return amount
            # TL'den dövize çevirirken satış kuru kullan (TL verip döviz alıyoruz)
            return amount / rates[to_currency]['selling_rate']
        
        # Dövizden TL'ye dönüşüm
        elif to_currency == 'TRY':
            # Dövizden TL'ye çevirirken alış kuru kullan (Döviz verip TL alıyoruz)
            return amount * rates[from_currency]['buying_rate']
        
        # Dövizden dövize dönüşüm (TL üzerinden)
        else:
            # Önce TL'ye çevir, sonra diğer dövize
            try:
                tl_amount = amount * rates[from_currency]['buying_rate']  # Dövizden TL'ye
                return tl_amount / rates[to_currency]['selling_rate']  # TL'den dövize
            except KeyError:
                return None 