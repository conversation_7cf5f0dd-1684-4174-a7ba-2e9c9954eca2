{% extends "admin/base.html" %}

{% block title %}Banner Düzenle{% endblock %}

{% block breadcrumb %}Banner <PERSON>ü<PERSON>le{% endblock %}

{% block page_title %}Banner Düzenle{% endblock %}
{% block page_subtitle %}Banner bilgiler<PERSON> güncelleyin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">Banner Düzenle</h2>
        <a href="{{ url_for('banner.banner_list') }}" 
           class="text-blue-500 hover:text-blue-600">
            <i class="fas fa-arrow-left mr-2"></i>Listeye Dön
        </a>
    </div>

    <form action="{{ url_for('banner.banner_edit', id=banner.id) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Sol <PERSON> -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">İçerik Tipi</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="file_type" value="image" {% if banner.file_type is not defined or banner.file_type == 'image' or not banner.file_type %}checked{% endif %} class="form-radio h-4 w-4 text-blue-600" onchange="toggleFileType('image')">
                            <span class="ml-2">Resim</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="file_type" value="video" {% if banner.file_type is defined and banner.file_type == 'video' %}checked{% endif %} class="form-radio h-4 w-4 text-blue-600" onchange="toggleFileType('video')">
                            <span class="ml-2">Video</span>
                        </label>
                    </div>
                </div>

                <!-- Resim Alanı -->
                <div id="image-upload-section" {% if banner.file_type is defined and banner.file_type == 'video' %}class="hidden"{% endif %}>
                    <!-- Mevcut Banner Resmi -->
                    {% if banner.image %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mevcut Resim</label>
                        <img src="{{ url_for('static', filename='uploads/banners/' + banner.image) }}" 
                            alt="{{ banner.title }}" 
                            class="w-48 h-auto rounded-lg shadow-md">
                    </div>
                    {% endif %}

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">Yeni Banner Resmi</label>
                        <input type="file" name="image" accept="image/*" class="mt-1 block w-full">
                        <p class="mt-1 text-sm text-gray-500">Yeni bir resim seçmezseniz mevcut resim korunacaktır.</p>
                    </div>
                </div>

                <!-- Video Alanı -->
                <div id="video-upload-section" {% if banner.file_type is not defined or banner.file_type != 'video' %}class="hidden"{% endif %}>
                    <!-- Mevcut Video Gösterimi -->
                    {% if banner.video is defined and banner.video %}
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mevcut Video</label>
                        <video 
                            src="{{ url_for('static', filename='uploads/banners/' + banner.video) }}" 
                            controls
                            class="w-full max-w-md h-auto rounded-lg shadow-md">
                            Tarayıcınız video öğesini desteklemiyor.
                        </video>
                    </div>
                    {% elif banner.video_url is defined and banner.video_url %}
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mevcut Video URL</label>
                        <div class="p-3 bg-gray-100 rounded-lg">
                            <a href="{{ banner.video_url }}" target="_blank" class="text-blue-500 break-all">
                                {{ banner.video_url }}
                            </a>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Yeni Video Dosyası</label>
                        <input type="file" name="video" accept="video/mp4,video/webm,video/ogg,video/mov" class="mt-1 block w-full">
                        <p class="mt-1 text-xs text-gray-500">Desteklenen formatlar: MP4, WebM, Ogg, MOV (Max 50MB)</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">VEYA Video URL'si</label>
                        <input type="url" name="video_url" value="{{ banner.video_url or '' }}" placeholder="https://example.com/video.mp4" class="mt-1 block w-full rounded-md border-gray-300">
                        <p class="mt-1 text-xs text-gray-500">Doğrudan video dosyasının URL'si (YouTube embed URL'si değil)</p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Alt Başlık</label>
                    <input type="text" name="subtitle" value="{{ banner.subtitle or '' }}" 
                           class="mt-1 block w-full rounded-md border-gray-300">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Açıklama</label>
                    <textarea name="description" rows="3" 
                              class="mt-1 block w-full rounded-md border-gray-300">{{ banner.description or '' }}</textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                    <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="main" {% if banner.category == 'main' %}selected{% endif %}>Ana Banner (Üst)</option>
                        <option value="secondary" {% if banner.category == 'secondary' %}selected{% endif %}>İkincil Banner (Alt)</option>
                    </select>
                </div>
            </div>

            <!-- Sağ Kolon -->
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Buton Metni</label>
                    <input type="text" name="button_text" value="{{ banner.button_text or '' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Buton Linki</label>
                    <input type="text" name="button_link" value="{{ banner.button_link or '' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Sıralama</label>
                    <input type="number" name="order" value="{{ banner.order or 0 }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="active" {% if banner.active %}checked{% endif %}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-700">Banner Aktif</label>
                </div>
            </div>
        </div>

        <div class="flex justify-end mt-6">
            <button type="submit" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-300">
                Değişiklikleri Kaydet
            </button>
        </div>
    </form>
</div>

<script>
function toggleFileType(type) {
    const imageSection = document.getElementById('image-upload-section');
    const videoSection = document.getElementById('video-upload-section');
    
    if (type === 'image') {
        imageSection.classList.remove('hidden');
        videoSection.classList.add('hidden');
    } else {
        imageSection.classList.add('hidden');
        videoSection.classList.remove('hidden');
    }
}
</script>
{% endblock %} 