/**
 * Rezervasyon fiyat hesaplayıcı
 */
document.addEventListener('DOMContentLoaded', function() {
    // Form elementini bul
    const form = document.getElementById('reservationForm');
    if (!form) return;

    // Para birimi butonlarını bul
    const currencyButtons = document.querySelectorAll('.currency-btn');
    if (!currencyButtons.length) return;

    // Misa<PERSON>r sayısı seçimini bul
    const guestsSelect = document.getElementById('guests');
    if (!guestsSelect) return;

    // Oda bilgilerini al
    const roomId = form.dataset.roomId;
    const roomPrice = parseFloat(form.dataset.roomPrice);
    const roomCurrency = form.dataset.roomCurrency;

    // Tarih alanlarını bul
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');

    // Güncel para birimi (başlangıçta oda para birimi)
    let currentCurrency = roomCurrency || 'EUR';

    // Aktif para birimi butonunu ayarla
    setActiveCurrencyButton(currentCurrency);

    // Para birimi butonlarına tıklama olayı ekle
    currencyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const newCurrency = this.getAttribute('data-currency');
            if (newCurrency === currentCurrency) return;
            
            // Aktif butonu güncelle
            currencyButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Para birimini değiştir
            currentCurrency = newCurrency;
            
            // Hidden input değerini güncelle
            const selectedCurrencyInput = document.getElementById('selected_currency');
            if (selectedCurrencyInput) {
                selectedCurrencyInput.value = currentCurrency;
            }
            
            // Fiyatı güncelle
            updatePrice(roomId, checkInInput.value, checkOutInput.value, guestsSelect.value, currentCurrency);
            
            // Ana oda fiyatını da güncelle
            updateRoomMainPrice(roomId, currentCurrency);
        });
    });

    // Misafir sayısı değişikliğini dinle
    guestsSelect.addEventListener('change', function() {
        updatePrice(roomId, checkInInput.value, checkOutInput.value, this.value, currentCurrency);
    });

    // Sayfa yüklendiğinde fiyatı hesapla
    if (checkInInput && checkOutInput) {
        updatePrice(roomId, checkInInput.value, checkOutInput.value, guestsSelect.value, currentCurrency);
    }
});

/**
 * Aktif para birimi butonunu ayarla
 * @param {string} currency - Para birimi kodu
 */
function setActiveCurrencyButton(currency) {
    const currencyButtons = document.querySelectorAll('.currency-btn');
    currencyButtons.forEach(btn => {
        if (btn.getAttribute('data-currency') === currency) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

/**
 * Ana oda fiyatını güncelle
 * @param {number} roomId - Oda ID
 * @param {string} currency - Para birimi
 */
function updateRoomMainPrice(roomId, currency) {
    if (!roomId) return;

    // API'ye istek gönder
    fetch(`/reservation/get-price?room_id=${roomId}&currency=${currency}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Ana oda fiyatını güncelle
                const roomMainPriceElement = document.getElementById('room-main-price');
                if (roomMainPriceElement) {
                    roomMainPriceElement.textContent = data.formatted_price;
                }
            }
        })
        .catch(error => console.error('Oda fiyatı güncellenirken bir hata oluştu:', error));
}

/**
 * Fiyatı güncelle
 * @param {number} roomId - Oda ID
 * @param {string} checkIn - Giriş tarihi (YYYY-MM-DD)
 * @param {string} checkOut - Çıkış tarihi (YYYY-MM-DD)
 * @param {number} guests - Misafir sayısı
 * @param {string} currency - Para birimi
 */
function updatePrice(roomId, checkIn, checkOut, guests, currency) {
    if (!checkIn || !checkOut || !roomId) return;

    // API'ye istek gönder
    fetch(`/reservation/get-price?room_id=${roomId}&check_in=${checkIn}&check_out=${checkOut}&guests=${guests}&currency=${currency}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Gecelik fiyatı güncelle
                const nightlyPriceElement = document.getElementById('nightlyPrice');
                if (nightlyPriceElement) {
                    nightlyPriceElement.textContent = data.formatted_price;
                }

                // Gece sayısını hesapla
                const checkInDate = new Date(checkIn);
                const checkOutDate = new Date(checkOut);
                const nights = Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24));

                // Gece sayısını göster
                const nightCountElement = document.getElementById('nightCount');
                if (nightCountElement) {
                    nightCountElement.textContent = nights + ' gece';
                }

                // Toplam fiyatı güncelle
                const totalPriceElement = document.getElementById('totalPrice');
                if (totalPriceElement) {
                    // Toplam fiyatı hesapla (gecelik fiyat * gece sayısı)
                    const price = parseFloat(data.price);
                    const total = price * nights;
                    
                    // Para birimi sembolü
                    let symbol = '';
                    if (currency === 'TRY') symbol = '₺';
                    else if (currency === 'USD') symbol = '$';
                    else if (currency === 'EUR') symbol = '€';
                    
                    // Fiyatı formatla ve göster
                    if (currency === 'TRY') {
                        totalPriceElement.textContent = Math.round(total) + ' ' + symbol;
                    } else {
                        totalPriceElement.textContent = symbol + Math.round(total);
                    }
                }
            }
        })
        .catch(error => console.error('Fiyat güncellenirken bir hata oluştu:', error));
} 