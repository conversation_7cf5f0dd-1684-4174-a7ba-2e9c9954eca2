from fpdf import FPDF
import os
from datetime import datetime
import qrcode
from flask import current_app, url_for
from io import BytesIO
import tempfile
import uuid

class ReservationPDF(FPDF):
    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        try:
            # Try to add DejaVu font if available
            dejavu_path = os.path.join('static', 'fonts', 'DejaVuSansCondensed.ttf')
            dejavu_bold_path = os.path.join('static', 'fonts', 'DejaVuSansCondensed-Bold.ttf')
            
            if os.path.exists(dejavu_path) and os.path.exists(dejavu_bold_path):
                self.add_font('DejaVu', '', dejavu_path, uni=True)
                self.add_font('DejaVu', 'B', dejavu_bold_path, uni=True)
                self.default_font = 'DejaVu'
            else:
                # If DejaVu fonts are not available, use Helvetica (built-in)
                self.default_font = 'Arial'
        except Exception as e:
            print(f"Font loading error: {str(e)}")
            self.default_font = 'Arial'

    def header(self):
        # Logo
        try:
            self.image(os.path.join('static', 'img', 'logo.png'), 10, 8, 50)
        except:
            pass
        
        # Font
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)
        
        # Başlık
        self.cell(0, 10, 'REZERVASYON BILGILERI', 0, 1, 'C')
        
        # Çizgi
        self.line(10, 25, 200, 25)
        
        # Boşluk
        self.ln(15)

    def footer(self):
        # Footer pozisyonuna git
        self.set_y(-15)
        
        # Font
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', '', 8)
        else:
            self.set_font('Arial', '', 8)
        
        # Sayfa numarası
        self.cell(0, 10, f'Sayfa {self.page_no()}/{{nb}}', 0, 0, 'C')

    def add_title(self, title):
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', 'B', 16)
        else:
            self.set_font('Arial', 'B', 16)
        self.cell(0, 10, title, 0, 1, 'C')
        self.ln(5)

    def add_info_line(self, label, value):
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', 'B', 10)
        else:
            self.set_font('Arial', 'B', 10)
        self.cell(40, 8, label, 0)
        
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', '', 10)
        else:
            self.set_font('Arial', '', 10)
        self.cell(0, 8, str(value), 0, 1)

    def add_section_title(self, title):
        self.ln(5)
        if self.default_font == 'DejaVu':
            self.set_font('DejaVu', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)
        self.cell(0, 8, title, 0, 1)
        self.line(10, self.get_y(), 200, self.get_y())
        self.ln(5)

    def add_qr_code(self, data, x=None, y=None, size=50):
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Create a temporary file for the QR code
        temp_dir = os.path.join(tempfile.gettempdir(), 'zeppelin_temp')
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, f'qr_{uuid.uuid4().hex}.png')
        
        # Save QR code to temporary file
        img.save(temp_file)
        
        # Pozisyon belirtilmediyse merkeze yerleştir
        if x is None:
            x = (210 - size) / 2  # A4 genişliği: 210mm
        
        if y is None:
            y = self.get_y()
        
        # QR kodunu PDF'e ekle
        self.image(temp_file, x, y, size, size)
        self.ln(size + 5)
        
        # Clean up the temporary file
        try:
            os.remove(temp_file)
        except:
            pass

class PDFService:
    @staticmethod
    def create_reservation_pdf(reservation, save_path=None):
        """Rezervasyon bilgileri için PDF oluşturur"""
        try:
            pdf = ReservationPDF()
            pdf.alias_nb_pages()
            pdf.add_page()
            
            # Remove special Turkish characters from strings
            def clean_text(text):
                if not isinstance(text, str):
                    return str(text)
                # Replace Turkish characters that can't be encoded in latin-1
                replacements = {
                    'İ': 'I', 'ı': 'i', 'Ğ': 'G', 'ğ': 'g',
                    'Ü': 'U', 'ü': 'u', 'Ş': 'S', 'ş': 's',
                    'Ö': 'O', 'ö': 'o', 'Ç': 'C', 'ç': 'c'
                }
                for turkish, latin in replacements.items():
                    text = text.replace(turkish, latin)
                return text
            
            # Rezervasyon başlığı
            pdf.add_title(f"Rezervasyon: #{reservation.reservation_code}")
            
            # Müşteri bilgileri
            pdf.add_section_title('Musteri Bilgileri')
            pdf.add_info_line('Ad Soyad:', clean_text(f"{reservation.name} {reservation.surname}"))
            pdf.add_info_line('E-posta:', clean_text(reservation.email))
            pdf.add_info_line('Telefon:', clean_text(reservation.phone))
            
            if reservation.country:
                pdf.add_info_line('Ulke:', clean_text(reservation.country))
                
            if reservation.id_number:
                id_type = clean_text(reservation.id_type or 'Kimlik No')
                pdf.add_info_line(id_type + ':', clean_text(reservation.id_number))
                
            if reservation.address:
                pdf.add_info_line('Adres:', clean_text(reservation.address))
            
            # Rezervasyon detayları
            pdf.add_section_title('Rezervasyon Detaylari')
            room_title = clean_text(reservation.room.get_title() if reservation.room else reservation.room_type)
            pdf.add_info_line('Oda:', room_title)
            pdf.add_info_line('Giris Tarihi:', reservation.check_in.strftime("%d.%m.%Y"))
            pdf.add_info_line('Cikis Tarihi:', reservation.check_out.strftime("%d.%m.%Y"))
            pdf.add_info_line('Toplam Gece:', reservation.get_total_nights())
            pdf.add_info_line('Misafir Sayisi:', reservation.guests)
            
            # Ödeme bilgileri
            pdf.add_section_title('Odeme Bilgileri')
            pdf.add_info_line('Odeme Durumu:', 'Odenmis' if reservation.payment_status == 'paid' else 'Odenmemis')
            
            if reservation.amount:
                pdf.add_info_line('Toplam Tutar:', f"{reservation.amount:.2f} {reservation.currency}")
                
                if reservation.original_amount and reservation.original_amount != reservation.amount:
                    pdf.add_info_line('Orijinal Tutar:', f"{reservation.original_amount:.2f} {reservation.room.currency if reservation.room else 'EUR'}")
            
            if reservation.payment_transaction:
                pdf.add_info_line('Islem No:', clean_text(reservation.payment_transaction.transaction_id))
                pdf.add_info_line('Odeme Tarihi:', reservation.payment_transaction.updated_at.strftime("%d.%m.%Y %H:%M"))
            
            # Özel istekler
            if reservation.special_requests:
                pdf.add_section_title('Ozel Istekler')
                if pdf.default_font == 'DejaVu':
                    pdf.set_font('DejaVu', '', 10)
                else:
                    pdf.set_font('Arial', '', 10)
                pdf.multi_cell(0, 5, clean_text(reservation.special_requests))
            
            # QR Kodu
            pdf.add_section_title('Rezervasyon QR Kodu')
            qr_data = f"RESERVATION:{reservation.reservation_code}|NAME:{clean_text(reservation.name)} {clean_text(reservation.surname)}|CHECK_IN:{reservation.check_in}|CHECK_OUT:{reservation.check_out}"
            pdf.add_qr_code(qr_data)
            
            # PDF'i kaydet veya döndür
            if save_path:
                # Klasör yoksa oluştur
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                pdf.output(save_path)
                
                # Make sure the file was actually created
                if os.path.exists(save_path):
                    # Store PDF generation success in the DB or in a temp file
                    success_marker = os.path.join(os.path.dirname(save_path), f"{reservation.reservation_code}.success")
                    with open(success_marker, 'w') as f:
                        f.write('1')
                    return save_path
                else:
                    return False
            else:
                # Bellek içinde tut
                return BytesIO(pdf.output(dest='S').encode('latin1'))
        
        except Exception as e:
            print(f"PDF generation error: {str(e)}")
            # Return False to indicate PDF generation failed
            return False
    
    @staticmethod
    def get_reservation_pdf_path(reservation):
        """Rezervasyon PDF'i için dosya yolunu döndürür"""
        pdf_dir = os.path.join(current_app.static_folder, 'pdfs', 'reservations')
        os.makedirs(pdf_dir, exist_ok=True)
        return os.path.join(pdf_dir, reservation.get_pdf_filename())
    
    @staticmethod
    def is_pdf_generated(reservation_code):
        """Check if PDF was successfully generated"""
        pdf_dir = os.path.join(current_app.root_path, 'static', 'pdfs', 'reservations')
        success_marker = os.path.join(pdf_dir, f"{reservation_code}.success")
        return os.path.exists(success_marker) 