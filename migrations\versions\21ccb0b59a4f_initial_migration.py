"""initial migration

Revision ID: 21ccb0b59a4f
Revises: 
Create Date: 2025-07-17 15:40:52.336121

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '21ccb0b59a4f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title_tr', sa.String(length=200), nullable=False),
    sa.Column('description_tr', sa.Text(), nullable=True),
    sa.Column('content_tr', sa.Text(), nullable=True),
    sa.Column('title_en', sa.String(length=200), nullable=False),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('content_en', sa.Text(), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=True),
    sa.Column('featured_image', sa.String(length=200), nullable=True),
    sa.Column('gallery_images', sa.Text(), nullable=True),
    sa.Column('duration', sa.String(length=100), nullable=True),
    sa.Column('difficulty', sa.String(length=50), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('max_participants', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('banner',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=True),
    sa.Column('subtitle', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image', sa.String(length=200), nullable=True),
    sa.Column('video', sa.String(length=200), nullable=True),
    sa.Column('video_url', sa.String(length=500), nullable=True),
    sa.Column('file_type', sa.String(length=20), nullable=True),
    sa.Column('button_text', sa.String(length=50), nullable=True),
    sa.Column('button_link', sa.String(length=200), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('blog_post',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title_tr', sa.String(length=200), nullable=False),
    sa.Column('title_en', sa.String(length=200), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=False),
    sa.Column('content_tr', sa.Text(), nullable=True),
    sa.Column('content_en', sa.Text(), nullable=True),
    sa.Column('excerpt_tr', sa.Text(), nullable=True),
    sa.Column('excerpt_en', sa.Text(), nullable=True),
    sa.Column('meta_description_tr', sa.String(length=160), nullable=True),
    sa.Column('meta_description_en', sa.String(length=160), nullable=True),
    sa.Column('featured_image', sa.String(length=200), nullable=True),
    sa.Column('video_url', sa.String(length=500), nullable=True),
    sa.Column('video_thumbnail', sa.String(length=200), nullable=True),
    sa.Column('video_duration', sa.Integer(), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('author', sa.String(length=100), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('country_visitor_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('country_code', sa.String(length=2), nullable=False),
    sa.Column('country_name', sa.String(length=100), nullable=False),
    sa.Column('visitors', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('currency_rate',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('currency_code', sa.String(length=3), nullable=False),
    sa.Column('buying_rate', sa.Float(), nullable=False),
    sa.Column('selling_rate', sa.Float(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('food_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name_tr', sa.String(length=100), nullable=False),
    sa.Column('description_tr', sa.Text(), nullable=True),
    sa.Column('name_en', sa.String(length=100), nullable=True),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('gallery',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image', sa.String(length=255), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ip_settings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ip_range', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('keyword_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('keyword', sa.String(length=255), nullable=True),
    sa.Column('source', sa.String(length=50), nullable=True),
    sa.Column('page_url', sa.String(length=255), nullable=True),
    sa.Column('visitor_ip', sa.String(length=50), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('referrer', sa.Text(), nullable=True),
    sa.Column('visit_count', sa.Integer(), nullable=True),
    sa.Column('first_visit', sa.DateTime(), nullable=True),
    sa.Column('last_visit', sa.DateTime(), nullable=True),
    sa.Column('avg_visit_duration', sa.Float(), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=True),
    sa.Column('region', sa.String(length=100), nullable=True),
    sa.Column('latitude', sa.Float(), nullable=True),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('device_type', sa.String(length=50), nullable=True),
    sa.Column('browser', sa.String(length=50), nullable=True),
    sa.Column('os', sa.String(length=50), nullable=True),
    sa.Column('is_mobile', sa.Boolean(), nullable=True),
    sa.Column('is_tablet', sa.Boolean(), nullable=True),
    sa.Column('converted', sa.Boolean(), nullable=True),
    sa.Column('conversion_type', sa.String(length=50), nullable=True),
    sa.Column('conversion_value', sa.Float(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('language',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key', sa.String(length=100), nullable=False),
    sa.Column('tr', sa.Text(), nullable=True),
    sa.Column('en', sa.Text(), nullable=True),
    sa.Column('page', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key')
    )
    op.create_table('menu',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title_tr', sa.String(length=100), nullable=False),
    sa.Column('title_en', sa.String(length=100), nullable=True),
    sa.Column('link', sa.String(length=255), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['menu.id'], name='fk_menu_parent'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('newsletter',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    op.create_table('order',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('room_number', sa.String(length=50), nullable=True),
    sa.Column('customer_name', sa.String(length=100), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('total_amount', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('customer_name', sa.String(length=100), nullable=True),
    sa.Column('total_amount', sa.Float(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('pages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title_tr', sa.String(length=200), nullable=True),
    sa.Column('content_tr', sa.Text(), nullable=True),
    sa.Column('title_en', sa.String(length=200), nullable=True),
    sa.Column('content_en', sa.Text(), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=True),
    sa.Column('meta_description', sa.String(length=200), nullable=True),
    sa.Column('featured_image', sa.String(length=200), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('is_homepage', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('views', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('payment_method',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('provider', sa.Enum('VAKIFBANK', 'IYZICO', name='paymentprovider'), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_test_mode', sa.Boolean(), nullable=True),
    sa.Column('logo_url', sa.String(length=255), nullable=True),
    sa.Column('merchant_id', sa.String(length=100), nullable=True),
    sa.Column('api_key', sa.String(length=255), nullable=True),
    sa.Column('secret_key', sa.String(length=255), nullable=True),
    sa.Column('webhook_url', sa.String(length=255), nullable=True),
    sa.Column('terminal_id', sa.String(length=50), nullable=True),
    sa.Column('terminal_user', sa.String(length=50), nullable=True),
    sa.Column('terminal_pass', sa.String(length=50), nullable=True),
    sa.Column('terminal_merch', sa.String(length=50), nullable=True),
    sa.Column('secure3d_url', sa.String(length=255), nullable=True),
    sa.Column('success_url', sa.String(length=255), nullable=True),
    sa.Column('fail_url', sa.String(length=255), nullable=True),
    sa.Column('iyzico_api_key', sa.String(length=255), nullable=True),
    sa.Column('iyzico_secret_key', sa.String(length=255), nullable=True),
    sa.Column('iyzico_base_url', sa.String(length=255), nullable=True),
    sa.Column('supported_currencies', sa.JSON(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('installment_options', sa.JSON(), nullable=True),
    sa.Column('min_amount', sa.Float(), nullable=True),
    sa.Column('max_amount', sa.Float(), nullable=True),
    sa.Column('transaction_fee', sa.Float(), nullable=True),
    sa.Column('fixed_fee', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('room_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name_tr', sa.String(length=100), nullable=False),
    sa.Column('name_en', sa.String(length=100), nullable=True),
    sa.Column('description_tr', sa.Text(), nullable=True),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=False),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug'),
    sa.UniqueConstraint('slug', name='uq_room_category_slug')
    )
    op.create_table('room_feature',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('seos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('page_name', sa.String(length=100), nullable=True),
    sa.Column('title', sa.String(length=200), nullable=True),
    sa.Column('title_en', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('keywords', sa.String(length=500), nullable=True),
    sa.Column('keywords_en', sa.String(length=500), nullable=True),
    sa.Column('og_title', sa.String(length=70), nullable=True),
    sa.Column('og_description', sa.String(length=200), nullable=True),
    sa.Column('og_image', sa.String(length=255), nullable=True),
    sa.Column('canonical_url', sa.String(length=255), nullable=True),
    sa.Column('robots', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('schema_type', sa.String(length=50), nullable=True),
    sa.Column('twitter_card', sa.String(length=50), nullable=True),
    sa.Column('twitter_site', sa.String(length=50), nullable=True),
    sa.Column('twitter_creator', sa.String(length=50), nullable=True),
    sa.Column('fb_app_id', sa.String(length=50), nullable=True),
    sa.Column('locale', sa.String(length=10), nullable=True),
    sa.Column('alternate_urls', sa.JSON(), nullable=True),
    sa.Column('priority', sa.Float(), nullable=True),
    sa.Column('changefreq', sa.String(length=20), nullable=True),
    sa.Column('last_modified', sa.DateTime(), nullable=True),
    sa.Column('meta_author', sa.String(length=100), nullable=True),
    sa.Column('meta_publisher', sa.String(length=100), nullable=True),
    sa.Column('meta_copyright', sa.String(length=100), nullable=True),
    sa.Column('meta_language', sa.String(length=10), nullable=True),
    sa.Column('og_type', sa.String(length=50), nullable=True),
    sa.Column('og_site_name', sa.String(length=100), nullable=True),
    sa.Column('og_locale', sa.String(length=10), nullable=True),
    sa.Column('og_updated_time', sa.DateTime(), nullable=True),
    sa.Column('twitter_title', sa.String(length=70), nullable=True),
    sa.Column('twitter_description', sa.String(length=200), nullable=True),
    sa.Column('twitter_image', sa.String(length=255), nullable=True),
    sa.Column('schema_data', sa.JSON(), nullable=True),
    sa.Column('page_speed_mobile', sa.Float(), nullable=True),
    sa.Column('page_speed_desktop', sa.Float(), nullable=True),
    sa.Column('last_speed_check', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('page_name')
    )
    op.create_table('settings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key', sa.String(length=100), nullable=False),
    sa.Column('value', sa.Text(), nullable=True),
    sa.Column('type_', sa.String(length=20), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('group', sa.String(length=50), nullable=True),
    sa.Column('click_count', sa.Integer(), nullable=True),
    sa.Column('last_click', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('site_logo', sa.String(length=255), nullable=True),
    sa.Column('site_logo_small', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key')
    )
    op.create_table('sidebar_menus',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=100), nullable=False),
    sa.Column('url', sa.String(length=200), nullable=False),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('slider',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=100), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('file_type', sa.String(length=10), nullable=True),
    sa.Column('file_path', sa.String(length=255), nullable=True),
    sa.Column('category', sa.String(length=20), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sponsors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('image', sa.String(length=255), nullable=True),
    sa.Column('url', sa.String(length=255), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('under_construction',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('estimated_completion', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('show_modal', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=80), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('password_hash', sa.String(length=128), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_table('visitor_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('unique_visitors', sa.Integer(), nullable=True),
    sa.Column('page_views', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('whatsapp',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('position', sa.String(length=20), nullable=True),
    sa.Column('color', sa.String(length=20), nullable=True),
    sa.Column('size', sa.String(length=20), nullable=True),
    sa.Column('show_text', sa.Boolean(), nullable=True),
    sa.Column('text', sa.String(length=100), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('food',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name_tr', sa.String(length=100), nullable=False),
    sa.Column('description_tr', sa.Text(), nullable=True),
    sa.Column('name_en', sa.String(length=100), nullable=True),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('image', sa.String(length=255), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['food_category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('payment_transaction',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('payment_method_id', sa.Integer(), nullable=True),
    sa.Column('order_id', sa.String(length=50), nullable=True),
    sa.Column('transaction_id', sa.String(length=100), nullable=True),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SUCCESS', 'FAILED', 'REFUNDED', name='paymentstatus'), nullable=True),
    sa.Column('installment', sa.Integer(), nullable=True),
    sa.Column('error_code', sa.String(length=50), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('response_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['payment_method_id'], ['payment_method.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('order_id')
    )
    op.create_table('promotions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('duration_minutes', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('start_time', sa.DateTime(), nullable=True),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('room',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title_tr', sa.String(length=200), nullable=False),
    sa.Column('description_tr', sa.Text(), nullable=True),
    sa.Column('title_en', sa.String(length=200), nullable=False),
    sa.Column('description_en', sa.Text(), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.Column('capacity', sa.Integer(), nullable=True),
    sa.Column('size', sa.Float(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('gallery_images', sa.Text(), nullable=True),
    sa.Column('video_url', sa.String(length=500), nullable=True),
    sa.Column('video_thumbnail', sa.String(length=200), nullable=True),
    sa.Column('video_duration', sa.Integer(), nullable=True),
    sa.Column('view_type', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('booking_url', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['room_category.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('order_item',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('food_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['food_id'], ['food.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['order.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('promotion_views',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('promotion_id', sa.Integer(), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=False),
    sa.Column('first_view', sa.DateTime(), nullable=True),
    sa.Column('last_view', sa.DateTime(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['promotion_id'], ['promotions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('reservation',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reservation_code', sa.String(length=50), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('surname', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=False),
    sa.Column('check_in', sa.Date(), nullable=False),
    sa.Column('check_out', sa.Date(), nullable=False),
    sa.Column('guests', sa.Integer(), nullable=False),
    sa.Column('room_type', sa.String(length=50), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('payment_status', sa.String(length=20), nullable=True),
    sa.Column('payment_transaction_id', sa.Integer(), nullable=True),
    sa.Column('room_id', sa.Integer(), nullable=True),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('original_amount', sa.Float(), nullable=True),
    sa.Column('exchange_rate', sa.Float(), nullable=True),
    sa.Column('country', sa.String(length=50), nullable=True),
    sa.Column('id_number', sa.String(length=20), nullable=True),
    sa.Column('id_type', sa.String(length=20), nullable=True),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('special_requests', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['payment_transaction_id'], ['payment_transaction.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['room_id'], ['room.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('reservation_code')
    )
    op.create_table('room_features',
    sa.Column('room_id', sa.Integer(), nullable=True),
    sa.Column('feature_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['feature_id'], ['room_feature.id'], ),
    sa.ForeignKeyConstraint(['room_id'], ['room.id'], )
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('room_features')
    op.drop_table('reservation')
    op.drop_table('promotion_views')
    op.drop_table('order_item')
    op.drop_table('room')
    op.drop_table('promotions')
    op.drop_table('payment_transaction')
    op.drop_table('food')
    op.drop_table('whatsapp')
    op.drop_table('visitor_stats')
    op.drop_table('user')
    op.drop_table('under_construction')
    op.drop_table('sponsors')
    op.drop_table('slider')
    op.drop_table('sidebar_menus')
    op.drop_table('settings')
    op.drop_table('seos')
    op.drop_table('room_feature')
    op.drop_table('room_category')
    op.drop_table('payment_method')
    op.drop_table('pages')
    op.drop_table('orders')
    op.drop_table('order')
    op.drop_table('newsletter')
    op.drop_table('menu')
    op.drop_table('language')
    op.drop_table('keyword_analytics')
    op.drop_table('ip_settings')
    op.drop_table('gallery')
    op.drop_table('food_category')
    op.drop_table('currency_rate')
    op.drop_table('country_visitor_stats')
    op.drop_table('blog_post')
    op.drop_table('banner')
    op.drop_table('activity')
    # ### end Alembic commands ###
