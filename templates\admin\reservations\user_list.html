{% extends "admin/base.html" %}

{% block admin_content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Rezervasyonlar</h1>
        <a href="{{ url_for('reservation.new_reservation') }}" class="px-4 py-2 bg-gold hover:bg-dark-gold text-white rounded-md transition-colors">
            <i class="fas fa-plus mr-2"></i> Yeni Rezervasyon
        </a>
    </div>

    <!-- <PERSON><PERSON> ve Filtreleme -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <form action="{{ url_for('reservation.user_reservations') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
                <label for="code" class="block text-sm font-medium text-gray-700 mb-1">Rezervasyon Kodu</label>
                <input type="text" id="code" name="code" value="{{ code or '' }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold"
                       placeholder="Rezervasyon kodunu girin">
            </div>
            <div class="flex items-end">
                <button type="submit" class="px-4 py-2 bg-gold hover:bg-dark-gold text-white rounded-md transition-colors h-10">
                    <i class="fas fa-search mr-2"></i> Ara
                </button>
                {% if code %}
                <a href="{{ url_for('reservation.user_reservations') }}" class="ml-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors h-10 inline-flex items-center">
                    <i class="fas fa-times mr-2"></i> Temizle
                </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Helper function to convert UTC to Turkey time -->
    {% macro to_turkey_time(utc_time) %}
        {% set turkey_time = utc_time.replace(tzinfo=None) + timedelta(hours=3) %}
        {{ turkey_time }}
    {% endmacro %}

    <!-- Rezervasyon Listesi -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        {% if reservations %}
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="bg-gray-50 text-left">
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Kod</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Müşteri</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Oda</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Tarihler</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Tutar</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Durumlar</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for reservation in reservations %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ reservation.reservation_code }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {% set turkey_time = reservation.created_at.replace(tzinfo=None) + timedelta(hours=3) %}
                                    {{ turkey_time.strftime('%d.%m.%Y') }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    {{ reservation.name }} {{ reservation.surname }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ reservation.email }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    {{ reservation.room.name if reservation.room else reservation.room_type }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ reservation.guests }} Kişi
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ reservation.check_in.strftime('%d.%m.%Y') }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ reservation.check_out.strftime('%d.%m.%Y') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {% if reservation.currency == 'TRY' %}
                                        {{ reservation.amount|int }} ₺
                                    {% elif reservation.currency == 'USD' %}
                                        ${{ reservation.amount|int }}
                                    {% elif reservation.currency == 'EUR' %}
                                        €{{ reservation.amount|int }}
                                    {% else %}
                                        {{ reservation.amount|int }} {{ reservation.currency }}
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col space-y-1">
                                    <span class="px-2 py-1 text-xs rounded-full 
                                        {% if reservation.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif reservation.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif reservation.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %} inline-flex items-center justify-center">
                                        {% if reservation.status == 'confirmed' %}
                                            <i class="fas fa-check-circle mr-1"></i> Onaylandı
                                        {% elif reservation.status == 'pending' %}
                                            <i class="fas fa-clock mr-1"></i> Beklemede
                                        {% elif reservation.status == 'cancelled' %}
                                            <i class="fas fa-times-circle mr-1"></i> İptal Edildi
                                        {% else %}
                                            <i class="fas fa-question-circle mr-1"></i> {{ reservation.status }}
                                        {% endif %}
                                    </span>
                                    
                                    <span class="px-2 py-1 text-xs rounded-full 
                                        {% if reservation.payment_status == 'paid' %}bg-green-100 text-green-800
                                        {% elif reservation.payment_status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif reservation.payment_status == 'failed' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %} inline-flex items-center justify-center">
                                        {% if reservation.payment_status == 'paid' %}
                                            <i class="fas fa-check-circle mr-1"></i> Ödendi
                                        {% elif reservation.payment_status == 'pending' %}
                                            <i class="fas fa-clock mr-1"></i> Beklemede
                                        {% elif reservation.payment_status == 'failed' %}
                                            <i class="fas fa-times-circle mr-1"></i> Başarısız
                                        {% else %}
                                            <i class="fas fa-question-circle mr-1"></i> Ödenmedi
                                        {% endif %}
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ url_for('reservation.reservation_detail', id=reservation.id) }}" 
                                       class="text-gold hover:text-dark-gold transition-colors">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if reservation.status != 'cancelled' %}
                                    <form action="{{ url_for('reservation.update_status', id=reservation.id) }}" method="POST" class="inline-block">
                                        <input type="hidden" name="status" value="cancelled">
                                        <button type="submit" onclick="return confirm('Bu rezervasyonu iptal etmek istediğinizden emin misiniz?')" 
                                                class="text-red-600 hover:text-red-800 transition-colors">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    <a href="{{ url_for('reservation.download_reservation_pdf', code=reservation.reservation_code) }}" 
                                       target="_blank" class="text-indigo-600 hover:text-indigo-800 transition-colors">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if pagination.pages > 1 %}
            <div class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {{ pagination.total }} sonuçtan
                            <span class="font-medium">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span>
                            -
                            <span class="font-medium">{{ min(pagination.page * pagination.per_page, pagination.total) }}</span>
                            arası gösteriliyor
                        </p>
                    </div>
                    <div>
                        <nav class="flex items-center">
                            {% if pagination.has_prev %}
                            <a href="{{ url_for('reservation.user_reservations', page=pagination.prev_num, code=code) }}" 
                               class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 mr-2">
                                Önceki
                            </a>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <a href="{{ url_for('reservation.user_reservations', page=page_num, code=code) }}" 
                                       class="px-3 py-1 rounded-md bg-gold text-white">{{ page_num }}</a>
                                    {% else %}
                                    <a href="{{ url_for('reservation.user_reservations', page=page_num, code=code) }}" 
                                       class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">{{ page_num }}</a>
                                    {% endif %}
                                {% else %}
                                    <span class="px-3 py-1">…</span>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <a href="{{ url_for('reservation.user_reservations', page=pagination.next_num, code=code) }}" 
                               class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 ml-2">
                                Sonraki
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="p-12 text-center">
                <div class="text-6xl text-gray-300 mb-4">
                    <i class="fas fa-bed"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-700 mb-2">Henüz rezervasyon bulunmuyor</h3>
                <p class="text-gray-500 mb-6">Belirtilen kriterlere uygun rezervasyon bulunamadı.</p>
                {% if code %}
                <a href="{{ url_for('reservation.user_reservations') }}" 
                   class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors">
                    Tüm Rezervasyonları Göster
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 