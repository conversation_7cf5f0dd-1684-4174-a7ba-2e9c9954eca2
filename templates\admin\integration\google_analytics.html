{% extends "admin/base.html" %}

{% block title %}Google Analytics & Tag Manager{% endblock %}

{% block admin_content %}
<div class="p-6">
    <!-- Başlık -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fab fa-google text-blue-600 mr-3"></i>
                Google Analytics & Tag Manager
            </h1>
            <p class="text-gray-600 mt-1">Web sitenizin analitik verilerini takip edin ve yönetin</p>
        </div>
        
        <div class="flex space-x-3">
            <button onclick="testAnalytics()"
                    class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-vial mr-2"></i>
                Test Gönder
            </button>

            <button onclick="testConnection()"
                    class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-plug mr-2"></i>
                Bağlantı Testi
            </button>

            <button onclick="refreshData()"
                    class="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Yenile
            </button>

            <a href="https://analytics.google.com/analytics/web/#/p10451427301/reports/intelligenthome"
               target="_blank"
               class="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                <i class="fas fa-external-link-alt mr-2"></i>
                Analytics Paneli
            </a>
        </div>
    </div>

    <!-- Durum Kartları -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Google Analytics Durumu -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-blue-900">Google Analytics</h3>
                    {% if settings.google_analytics_active and settings.google_analytics_id %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mt-2">
                        <i class="fas fa-check mr-1"></i>
                        Aktif
                    </span>
                    {% else %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 mt-2">
                        <i class="fas fa-times mr-1"></i>
                        Pasif
                    </span>
                    {% endif %}
                </div>
                <i class="fab fa-google-analytics text-4xl text-blue-500"></i>
            </div>
            {% if settings.google_analytics_id %}
            <div class="mt-3">
                <p class="text-sm text-blue-700">ID: <span class="font-mono">{{ settings.google_analytics_id }}</span></p>
            </div>
            {% endif %}
        </div>

        <!-- Google Tag Manager Durumu -->
        <div class="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-purple-900">Tag Manager</h3>
                    {% if settings.google_tag_manager_active and settings.google_tag_manager_id %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mt-2">
                        <i class="fas fa-check mr-1"></i>
                        Aktif
                    </span>
                    {% else %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 mt-2">
                        <i class="fas fa-times mr-1"></i>
                        Pasif
                    </span>
                    {% endif %}
                </div>
                <i class="fas fa-tags text-4xl text-purple-500"></i>
            </div>
            {% if settings.google_tag_manager_id %}
            <div class="mt-3">
                <p class="text-sm text-purple-700">ID: <span class="font-mono">{{ settings.google_tag_manager_id }}</span></p>
            </div>
            {% endif %}
        </div>

        <!-- Gerçek Ziyaretçi Verisi -->
        <div class="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-green-900">{{ monthly_data.period }}</h3>
                    <p class="text-2xl font-bold text-green-800 mt-1">
                        {{ "{:,}".format(monthly_data.users) }}
                        {% if monthly_data.mock %}
                        <span class="text-xs text-orange-600">(Demo)</span>
                        {% endif %}
                    </p>
                    <p class="text-sm text-green-600">Ziyaretçi</p>
                    <div class="mt-2 text-xs text-green-700">
                        <span>{{ "{:,}".format(monthly_data.sessions) }} oturum</span> •
                        <span>{{ "{:,}".format(monthly_data.pageviews) }} sayfa görüntüleme</span>
                    </div>
                </div>
                <i class="fas fa-users text-4xl text-green-500"></i>
            </div>
        </div>

        <!-- Bu Hafta Kartı -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-blue-900">{{ weekly_data.period }}</h3>
                    <p class="text-2xl font-bold text-blue-800 mt-1">
                        {{ "{:,}".format(weekly_data.users) }}
                        {% if weekly_data.mock %}
                        <span class="text-xs text-orange-600">(Demo)</span>
                        {% endif %}
                    </p>
                    <p class="text-sm text-blue-600">Ziyaretçi</p>
                </div>
                <i class="fas fa-calendar-week text-4xl text-blue-500"></i>
            </div>
        </div>

        <!-- Bugün Kartı -->
        <div class="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-purple-900">{{ daily_data.period }}</h3>
                    <p class="text-2xl font-bold text-purple-800 mt-1">
                        {{ "{:,}".format(daily_data.users) }}
                        {% if daily_data.mock %}
                        <span class="text-xs text-orange-600">(Demo)</span>
                        {% endif %}
                    </p>
                    <p class="text-sm text-purple-600">Ziyaretçi</p>
                </div>
                <i class="fas fa-calendar-day text-4xl text-purple-500"></i>
            </div>
        </div>
    </div>

    <!-- En Çok Ziyaret Edilen Sayfalar -->
    <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm mb-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <i class="fas fa-chart-bar text-blue-600 mr-3"></i>
                En Çok Ziyaret Edilen Sayfalar
            </h2>
            <button onclick="refreshData()" class="text-blue-600 hover:text-blue-800 transition-colors">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>

        <div class="overflow-hidden">
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sayfa</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Görüntüleme</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% if top_pages.pages %}
                        {% for page in top_pages.pages %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-file-alt text-gray-400 mr-2"></i>
                                    {{ page.path }}
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-500 text-right font-medium">
                                {{ "{:,}".format(page.pageviews) }}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="2" class="px-4 py-8 text-center text-gray-500">
                                <i class="fas fa-chart-bar text-3xl mb-2 text-gray-300"></i>
                                <p>Sayfa verisi bulunamadı</p>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        {% if top_pages.mock %}
        <div class="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-orange-500 mr-3 mt-0.5"></i>
                <div class="text-sm text-orange-700">
                    <p class="font-medium mb-2">Demo veriler gösteriliyor</p>
                    <p>Gerçek Google Analytics verilerini görmek için API yapılandırması gerekli.</p>
                    <button onclick="toggleSetupGuide()" class="mt-2 text-blue-600 hover:text-blue-800 underline">
                        Kurulum rehberini göster
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- API Kurulum Rehberi -->
    <div id="setup-guide" class="hidden bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-blue-900 flex items-center">
                <i class="fas fa-cog text-blue-600 mr-3"></i>
                Google Analytics API Kurulumu
            </h2>
            <button onclick="toggleSetupGuide()" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="space-y-6">
            <!-- Credentials Durumu -->
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h3 class="font-medium text-gray-900 mb-3">Mevcut Durum</h3>
                <div class="space-y-2">
                    {% if credentials_status.exists %}
                        {% if credentials_status.valid %}
                        <div class="flex items-center text-green-700">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>Credentials dosyası bulundu ve geçerli</span>
                        </div>
                        <div class="text-sm text-gray-600 ml-6">
                            <p>Email: {{ credentials_status.client_email }}</p>
                            <p>Proje: {{ credentials_status.project_id }}</p>
                        </div>
                        {% else %}
                        <div class="flex items-center text-red-700">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span>Credentials dosyası geçersiz</span>
                        </div>
                        {% endif %}
                    {% else %}
                    <div class="flex items-center text-orange-700">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>Credentials dosyası bulunamadı</span>
                    </div>
                    <div class="text-sm text-gray-600 ml-6">
                        <p>Beklenen konum: {{ credentials_status.path }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Kurulum Adımları -->
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h3 class="font-medium text-gray-900 mb-3">Kurulum Adımları</h3>
                <ol class="space-y-3 text-sm">
                    <li class="flex items-start">
                        <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">1</span>
                        <div>
                            <p class="font-medium">Google Cloud Console'da Service Account oluşturun</p>
                            <p class="text-gray-600">console.cloud.google.com → APIs & Services → Credentials</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">2</span>
                        <div>
                            <p class="font-medium">JSON key dosyasını indirin</p>
                            <p class="text-gray-600">Service Account → Keys → Add Key → Create New Key → JSON</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">3</span>
                        <div>
                            <p class="font-medium">Dosyayı proje kök dizinine yerleştirin</p>
                            <p class="text-gray-600">Dosya adı: <code class="bg-gray-100 px-1 rounded">google_analytics_credentials.json</code></p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">4</span>
                        <div>
                            <p class="font-medium">Google Analytics'te erişim verin</p>
                            <p class="text-gray-600">Analytics → Admin → Property Access Management → Service Account email'ini ekleyin</p>
                        </div>
                    </li>
                </ol>
            </div>

            <!-- Hızlı Linkler -->
            <div class="flex space-x-3">
                <a href="https://console.cloud.google.com/apis/credentials" target="_blank"
                   class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                    <i class="fab fa-google mr-2"></i>
                    Google Cloud Console
                </a>
                <a href="https://analytics.google.com/analytics/web/provision/#/provision" target="_blank"
                   class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm">
                    <i class="fas fa-chart-line mr-2"></i>
                    Analytics Admin
                </a>
            </div>
        </div>
    </div>

    <!-- Ayarlar Formu -->
    <form method="POST" class="space-y-8">
        <!-- Google Analytics Ayarları -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center mb-6">
                <i class="fab fa-google-analytics text-2xl text-blue-600 mr-3"></i>
                <h2 class="text-xl font-semibold text-gray-900">Google Analytics Ayarları</h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Aktif/Pasif -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" 
                               id="google_analytics_active" 
                               name="google_analytics_active"
                               {% if settings.google_analytics_active %}checked{% endif %}
                               class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                        <label for="google_analytics_active" class="text-lg font-medium text-gray-700">
                            Google Analytics'i Aktif Et
                        </label>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Google Analytics ID</label>
                        <input type="text" 
                               name="google_analytics_id" 
                               value="{{ settings.google_analytics_id or '' }}"
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150 font-mono"
                               placeholder="G-XXXXXXXXXX">
                        <p class="text-sm text-gray-500">Örnek: G-FBJ80QBVNZ</p>
                    </div>
                </div>
                
                <!-- OAuth Yetkilendirme -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 mb-3">OAuth Yetkilendirme</h4>
                    <div class="space-y-3">
                        <div id="oauth-status" class="text-sm text-blue-700">
                            <p>Durum kontrol ediliyor...</p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ url_for('oauth.oauth_authorize') }}"
                               class="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                <i class="fas fa-key mr-2"></i>
                                OAuth Yetkilendir
                            </a>
                            <button onclick="testAnalytics()"
                                    class="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                                <i class="fas fa-flask mr-2"></i>
                                Bağlantıyı Test Et
                            </button>
                            <button onclick="refreshAnalyticsData()"
                                    class="inline-flex items-center px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm">
                                <i class="fas fa-sync mr-2"></i>
                                Verileri Yenile
                            </button>
                            <a href="{{ url_for('oauth.oauth_revoke') }}"
                               class="inline-flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm">
                                <i class="fas fa-times mr-2"></i>
                                Yetkilendirmeyi İptal Et
                            </a>
                        </div>
                    </div>
                    <div class="mt-3 space-y-2 text-sm text-blue-700">
                        <p><strong>Akış Adı:</strong> zeppelincappadocia.com</p>
                        <p><strong>Akış URL'si:</strong> zeppelincappadocia.com</p>
                        <p><strong>Akış Kimliği:</strong> 483671011</p>
                        <p><strong>Ölçüm Kimliği:</strong> G-FBJ80QBVNZ</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Tag Manager Ayarları -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center mb-6">
                <i class="fas fa-tags text-2xl text-purple-600 mr-3"></i>
                <h2 class="text-xl font-semibold text-gray-900">Google Tag Manager Ayarları</h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Aktif/Pasif -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" 
                               id="google_tag_manager_active" 
                               name="google_tag_manager_active"
                               {% if settings.google_tag_manager_active %}checked{% endif %}
                               class="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500">
                        <label for="google_tag_manager_active" class="text-lg font-medium text-gray-700">
                            Google Tag Manager'ı Aktif Et
                        </label>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Google Tag Manager ID</label>
                        <input type="text" 
                               name="google_tag_manager_id" 
                               value="{{ settings.google_tag_manager_id or '' }}"
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition duration-150 font-mono"
                               placeholder="GTM-XXXXXXX">
                        <p class="text-sm text-gray-500">Örnek: GTM-XXXXXXX</p>
                    </div>
                </div>
                
                <!-- Bilgi Kartı -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 class="font-medium text-purple-900 mb-3">Tag Manager Hakkında</h4>
                    <div class="space-y-2 text-sm text-purple-700">
                        <p>• Gelişmiş etiket yönetimi</p>
                        <p>• Kod değişikliği olmadan etiket ekleme</p>
                        <p>• Conversion tracking</p>
                        <p>• Remarketing etiketleri</p>
                        <p>• Custom event tracking</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kaydet Butonu -->
        <div class="flex justify-end">
            <button type="submit" 
                    class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition duration-150">
                <i class="fas fa-save mr-2"></i>
                Ayarları Kaydet
            </button>
        </div>
    </form>

    <!-- Hızlı Linkler -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="https://analytics.google.com/" target="_blank" 
           class="block p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all">
            <div class="flex items-center">
                <i class="fab fa-google text-2xl mr-3"></i>
                <div>
                    <h3 class="font-semibold">Google Analytics</h3>
                    <p class="text-sm opacity-90">Analytics paneline git</p>
                </div>
            </div>
        </a>
        
        <a href="https://tagmanager.google.com/" target="_blank" 
           class="block p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all">
            <div class="flex items-center">
                <i class="fas fa-tags text-2xl mr-3"></i>
                <div>
                    <h3 class="font-semibold">Tag Manager</h3>
                    <p class="text-sm opacity-90">Tag Manager paneline git</p>
                </div>
            </div>
        </a>
        
        <a href="{{ url_for('seo.keyword_analytics') }}" 
           class="block p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all">
            <div class="flex items-center">
                <i class="fas fa-chart-line text-2xl mr-3"></i>
                <div>
                    <h3 class="font-semibold">Kelime Analizi</h3>
                    <p class="text-sm opacity-90">SEO analizlerine git</p>
                </div>
            </div>
        </a>
    </div>

    <!-- Gerçek Zamanlı Analytics Verileri -->
    <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm mt-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                Gerçek Zamanlı Analytics Verileri
            </h2>
            <div class="text-sm text-gray-500" id="last-updated">
                Son güncelleme: Yükleniyor...
            </div>
        </div>

        <!-- Veri Kartları -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Günlük Veriler -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Bugün</h3>
                        <div class="text-3xl font-bold" id="daily-users">{{ daily_data.users if daily_data.success else '0' }}</div>
                        <div class="text-sm opacity-90">Kullanıcı</div>
                        <div class="text-sm mt-1">
                            <span id="daily-pageviews">{{ daily_data.pageviews if daily_data.success else '0' }}</span> sayfa görüntüleme
                        </div>
                    </div>
                    <i class="fas fa-calendar-day text-4xl opacity-20"></i>
                </div>
            </div>

            <!-- Haftalık Veriler -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Bu Hafta</h3>
                        <div class="text-3xl font-bold" id="weekly-users">{{ weekly_data.users if weekly_data.success else '0' }}</div>
                        <div class="text-sm opacity-90">Kullanıcı</div>
                        <div class="text-sm mt-1">
                            <span id="weekly-pageviews">{{ weekly_data.pageviews if weekly_data.success else '0' }}</span> sayfa görüntüleme
                        </div>
                    </div>
                    <i class="fas fa-calendar-week text-4xl opacity-20"></i>
                </div>
            </div>

            <!-- Aylık Veriler -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Bu Ay</h3>
                        <div class="text-3xl font-bold" id="monthly-users">{{ monthly_data.users if monthly_data.success else '0' }}</div>
                        <div class="text-sm opacity-90">Kullanıcı</div>
                        <div class="text-sm mt-1">
                            <span id="monthly-pageviews">{{ monthly_data.pageviews if monthly_data.success else '0' }}</span> sayfa görüntüleme
                        </div>
                    </div>
                    <i class="fas fa-calendar-alt text-4xl opacity-20"></i>
                </div>
            </div>
        </div>

        <!-- En Popüler Sayfalar -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-fire text-orange-500 mr-2"></i>
                En Popüler Sayfalar
            </h3>
            <div class="space-y-3" id="top-pages-list">
                {% if top_pages.success and top_pages.pages %}
                    {% for page in top_pages.pages[:5] %}
                    <div class="flex items-center justify-between bg-white p-3 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ page.page }}</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-blue-600">{{ page.views }}</div>
                            <div class="text-xs text-gray-500">görüntüleme</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-gray-500 py-4">
                        <i class="fas fa-chart-bar text-3xl mb-2"></i>
                        <p>Henüz veri bulunmuyor</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// OAuth durumunu kontrol et
function checkOAuthStatus() {
    fetch('/oauth/status')
    .then(response => response.json())
    .then(data => {
        const statusDiv = document.getElementById('oauth-status');
        if (data.authorized) {
            statusDiv.innerHTML = `
                <p class="text-green-700"><i class="fas fa-check-circle mr-1"></i> ${data.message}</p>
                <p class="text-xs text-gray-600">Scopes: ${data.scopes ? data.scopes.join(', ') : 'Bilinmiyor'}</p>
            `;
        } else {
            statusDiv.innerHTML = `
                <p class="text-red-700"><i class="fas fa-exclamation-circle mr-1"></i> ${data.message}</p>
            `;
        }
    })
    .catch(error => {
        const statusDiv = document.getElementById('oauth-status');
        statusDiv.innerHTML = '<p class="text-red-700">Durum kontrol edilemedi</p>';
    });
}

function testAnalytics() {
    fetch('/admin/integration/google-analytics/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Google Analytics test eventi gönder
            if (typeof gtag !== 'undefined') {
                gtag('event', 'admin_test', {
                    'event_category': 'admin_panel',
                    'event_label': 'analytics_test',
                    'value': 1
                });
            }
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Test sırasında bir hata oluştu', 'error');
    });
}

// Veri yenileme fonksiyonu
function refreshAnalyticsData() {
    fetch('/admin/integration/google-analytics/refresh-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Verileri güncelle
            if (data.data.daily.success) {
                document.getElementById('daily-users').textContent = data.data.daily.users;
                document.getElementById('daily-pageviews').textContent = data.data.daily.pageviews;
            }
            if (data.data.weekly.success) {
                document.getElementById('weekly-users').textContent = data.data.weekly.users;
                document.getElementById('weekly-pageviews').textContent = data.data.weekly.pageviews;
            }
            if (data.data.monthly.success) {
                document.getElementById('monthly-users').textContent = data.data.monthly.users;
                document.getElementById('monthly-pageviews').textContent = data.data.monthly.pageviews;
            }

            // Son güncelleme zamanını güncelle
            const now = new Date();
            document.getElementById('last-updated').textContent =
                `Son güncelleme: ${now.toLocaleTimeString('tr-TR')}`;

            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Veri yenileme sırasında bir hata oluştu', 'error');
    });
}

// Sayfa yüklendiğinde OAuth durumunu kontrol et
document.addEventListener('DOMContentLoaded', function() {
    checkOAuthStatus();

    // İlk yüklemede son güncelleme zamanını ayarla
    const now = new Date();
    document.getElementById('last-updated').textContent =
        `Son güncelleme: ${now.toLocaleTimeString('tr-TR')}`;

    // Her 5 dakikada bir otomatik yenile
    setInterval(refreshAnalyticsData, 300000); // 5 dakika = 300000ms
});

function toggleSetupGuide() {
    const guide = document.getElementById('setup-guide');
    if (guide.classList.contains('hidden')) {
        guide.classList.remove('hidden');
    } else {
        guide.classList.add('hidden');
    }
}

function refreshData() {
    showNotification('Veriler yenileniyor...', 'info');

    // Sayfayı yenile
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function testConnection() {
    fetch('/admin/integration/google-analytics/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Bağlantı testi sırasında bir hata oluştu', 'error');
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'info' ? 'bg-blue-500' : 'bg-red-500';
    const icon = type === 'success' ? 'check' : type === 'info' ? 'info-circle' : 'exclamation-triangle';

    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${bgColor} shadow-lg`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${icon} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}
