from flask import Blueprint, render_template, g, request, session
from models.banner import Banner
from models.slider import Slider
from models.sponsor import Sponsor
from models.room import Room, RoomCategory
from models.food import FoodCategory, Food
from models.activity import Activity
from models.blog import BlogPost  # BlogPost modelini ekledik
from datetime import datetime

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Giriş sayfası - dil seçimi"""
    # Intro slider'ları çek (giriş sayfası için)
    slider_images = Slider.query.filter_by(active=True, category='intro').order_by(Slider.order).all()

    # Sponsor'ları çek
    sponsors = Sponsor.query.filter_by(active=True).order_by(Sponsor.order).all()

    return render_template('index.html',
                         slider_images=slider_images,
                         sponsors=sponsors,
                         current_year=datetime.now().year)

@main_bp.route('/main')
def main():
    """Ana sayfa - giri<PERSON> sayfasından sonra"""
    # URL'den dil parametresini kontrol et, ancak session'daki dil varsa onu kullan
    lang = request.args.get('lang')
    if not lang:
        # Session'da dil varsa kullan, yoksa varsayılan tr
        lang = session.get('language', 'tr')
    
    # Geçerli dil kontrolü
    if lang not in ['tr', 'en']:
        lang = 'tr'

    # Session'a kaydet
    session['language'] = lang
    g.language = lang

    # Banner'ları getir
    banners = Banner.query.filter_by(active=True).order_by(Banner.order.asc()).all()

    # Debug için banner bilgilerini yazdır
    print("Active Banners:", len(banners))
    for banner in banners:
        print(f"Banner: {banner.subtitle}, Category: {banner.category}, Active: {banner.active}")

    main_banners = Banner.query.filter_by(category='main', active=True).all()
    secondary_banners = Banner.query.filter_by(category='secondary', active=True).all()
    main_sliders = Slider.query.filter_by(active=True, category='main').order_by(Slider.order).all()
    sponsors = Sponsor.query.filter_by(active=True).order_by(Sponsor.order).all()

    # Debug için slider bilgilerini yazdır
    print("Main Sliders:", len(main_sliders))
    for slider in main_sliders:
        print(f"Slider: {slider.title}, File: {slider.file_path}, Type: {slider.file_type}, Active: {slider.active}")

    # Oda kategorilerini al
    room_categories = RoomCategory.query.filter_by(active=True).order_by(RoomCategory.order).all()
    # Odaları al
    rooms = Room.query.filter_by(status='active').all()

    # Yemek kategorilerini al
    food_categories = FoodCategory.query.filter_by(active=True).order_by(FoodCategory.order).all()

    # Aktif yemekleri al
    foods = Food.query.filter_by(active=True).all()

    # Her yemeğin kategorisini kontrol et
    active_foods = []
    for food in foods:
        if food.category and food.category.active:
            active_foods.append(food)

    # Aktiviteleri al
    activities = Activity.get_active_activities()
    
    # Blog yazılarını al
    latest_posts = BlogPost.query.filter_by(status='published').order_by(BlogPost.created_at.desc()).limit(5).all()

    return render_template('main.html',
                         banners=banners,
                         main_banners=main_banners,
                         secondary_banners=secondary_banners,
                         main_sliders=main_sliders,
                         sponsors=sponsors,
                         room_categories=room_categories,
                         rooms=rooms,
                         food_categories=food_categories,
                         foods=active_foods,
                         activities=activities,
                         latest_posts=latest_posts,  # Blog yazılarını şablona ilettik
                         current_language=lang)