from flask import Blueprint, render_template, request, redirect, url_for, flash, make_response, jsonify, current_app
from models.seo import SEO, db
from flask_login import login_required
from datetime import datetime, timedelta
import xml.etree.ElementTree as ET
from collections import Counter
import re
from models.keyword_analytics import KeywordAnalytics
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import requests
import os
import subprocess
import json

seo_bp = Blueprint('seo', __name__)

def extract_keywords(text, max_keywords=10):
    """Basit keyword çıkarma fonksiyonu"""
    # Metni küçük harfe çevir
    text = text.lower()

    # Noktalama işaretlerini kaldır
    text = re.sub(r'[^\w\s]', '', text)

    # Stop words (Türkçe)
    stop_words = {'ve', 'veya', 'bir', 'bu', 'şu', 'için', 'ile', 'da', 'de', 'mi', 'den', 'dan',
                 'ama', 'fakat', 'lakin', 'ancak', 'çünk<PERSON>', 'zira', 'dolayı', 'gibi', 'kadar',
                 'daha', 'hen<PERSON>z', 'yani', 'yine', 'gene', 'bile', 'dahi', 'hem', 'ise', 'iken'}

    # Kelimeleri ayır ve stop words'leri kaldır
    words = [word for word in text.split() if word not in stop_words and len(word) > 2]

    # Kelime frekanslarını hesapla
    word_freq = {}
    for word in words:
        word_freq[word] = word_freq.get(word, 0) + 1

    # En sık geçen kelimeleri al
    keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:max_keywords]

    return [keyword[0] for keyword in keywords]

def analyze_content(content):
    """Sayfa içeriğini analiz eder ve SEO önerileri oluşturur"""
    if isinstance(content, str):
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text()
    else:
        text = str(content)

    # Keywords oluştur
    keywords = extract_keywords(text)

    # İlk 155 karakteri açıklama olarak kullan
    description = text[:155] + '...' if len(text) > 155 else text

    # İlk 3 keyword'ü başlıkta kullan
    title = ' '.join(keywords[:3]).title() + ' - Zeppelin Hotel'

    return {
        'title': title,
        'description': description,
        'keywords': ', '.join(keywords),
        'meta_author': 'Zeppelin Hotel',
        'meta_publisher': 'Zeppelin Hotel',
        'meta_copyright': '© 2024 Zeppelin Hotel. Tüm hakları saklıdır.',
        'meta_language': 'tr',
        'og_type': 'website',
        'og_site_name': 'Zeppelin Hotel',
        'og_locale': 'tr_TR',
        'twitter_card': 'summary_large_image',
        'schema_data': {
            "@type": "WebPage",
            "name": title,
            "description": description,
            "publisher": {
                "@type": "Organization",
                "name": "Zeppelin Hotel"
            }
        }
    }

def basic_analyze_content(content):
    """Temel SEO analizi yapar"""
    if isinstance(content, str):
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text()
    else:
        text = str(content)
    
    # Keywords oluştur
    keywords = extract_keywords(text)
    
    # İlk 155 karakteri açıklama olarak kullan
    description = text[:155] + '...' if len(text) > 155 else text
    
    # İlk 3 keyword'ü başlıkta kullan
    title = ' '.join(keywords[:3]).title() + ' - Zeppelin Hotel'
    
    return {
        'title': title,
        'description': description,
        'keywords': ', '.join(keywords),
        'meta_author': 'Zeppelin Hotel',
        'meta_publisher': 'Zeppelin Hotel',
        'meta_copyright': '© 2024 Zeppelin Hotel. Tüm hakları saklıdır.',
        'meta_language': 'tr',
        'og_type': 'website',
        'og_site_name': 'Zeppelin Hotel',
        'og_locale': 'tr_TR',
        'twitter_card': 'summary_large_image'
    }

@seo_bp.route('/admin/seo')
@login_required
def seo_list():
    seo_items = SEO.query.all()
    return render_template('admin/seo/list.html', seo_items=seo_items)

@seo_bp.route('/admin/seo/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def seo_edit(id):
    seo = SEO.query.get_or_404(id)
    if request.method == 'POST':
        seo.title = request.form.get('title')
        seo.title_en = request.form.get('title_en')
        seo.description = request.form.get('description')
        seo.description_en = request.form.get('description_en')
        seo.keywords = request.form.get('keywords')
        seo.keywords_en = request.form.get('keywords_en')
        seo.og_title = request.form.get('og_title')
        seo.og_description = request.form.get('og_description')
        seo.canonical_url = request.form.get('canonical_url')
        seo.robots = request.form.get('robots')
        
        db.session.commit()
        flash('SEO ayarları başarıyla güncellendi.', 'success')
        return redirect(url_for('seo.seo_list'))
    
    return render_template('admin/seo/edit.html', seo=seo)

@seo_bp.route('/admin/seo/create', methods=['GET', 'POST'])
@login_required
def seo_create():
    if request.method == 'POST':
        try:
            # Form verilerini al
            page_name = request.form.get('page_name')
            title = request.form.get('title')
            title_en = request.form.get('title_en')
            description = request.form.get('description')
            description_en = request.form.get('description_en')
            keywords_en = request.form.get('keywords_en')
            content = request.form.get('content', '')
            
            # Keywords otomatik oluştur
            keywords = extract_keywords(content or title)
            keywords_str = ','.join(keywords)
            
            seo = SEO(
                page_name=page_name,
                title=title,
                title_en=title_en,
                description=description,
                description_en=description_en,
                keywords=keywords_str,
                keywords_en=keywords_en,
                robots="index, follow"
            )
            
            db.session.add(seo)
            db.session.commit()
            
            flash('SEO kaydı başarıyla oluşturuldu!', 'success')
            return redirect(url_for('seo.seo_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/seo/create.html')

@seo_bp.route('/robots.txt')
def robots_txt():
    """Gelişmiş robots.txt dosyası oluştur"""
    domain = request.host_url.rstrip('/')
    
    robots_content = f"""# www.robotstxt.org/
# Tüm robotlar için kurallar
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/
Disallow: /private/
Disallow: /*?*

# Medya dosyalarına erişimi engelleme
Disallow: /*.pdf$
Disallow: /*.doc$
Disallow: /*.xls$

# Sitemap
Sitemap: {domain}/sitemap.xml

# Özel crawler/bot kuralları
User-agent: Googlebot
Allow: /

User-agent: Googlebot-Image
Allow: /static/images/
Allow: /static/uploads/

User-agent: Bingbot
Allow: /

# Google crawl hızını otomatik ayarlar, manuel delay gerekmez
"""
    response = make_response(robots_content)
    response.headers['Content-Type'] = 'text/plain'
    return response

@seo_bp.route('/sitemap.xml')
def sitemap():
    """XML sitemap oluştur"""
    try:
        # XML root elementini oluştur
        root = ET.Element('urlset')
        root.set('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9')
        root.set('xmlns:image', 'http://www.google.com/schemas/sitemap-image/1.1')

        current_time = datetime.now().strftime('%Y-%m-%d')
        domain = request.host_url.rstrip('/')

        # Ana sayfa
        add_url_to_sitemap(root, domain, current_time, '1.0', 'daily')

        # Main sayfası
        add_url_to_sitemap(root, f"{domain}/main", current_time, '0.9', 'daily')

        # Odalar sayfası
        add_url_to_sitemap(root, f"{domain}/rooms", current_time, '0.8', 'weekly')

        # Yemek menüsü sayfası
        add_url_to_sitemap(root, f"{domain}/food/menu", current_time, '0.8', 'weekly')
        
        # Blog sayfası
        add_url_to_sitemap(root, f"{domain}/blog", current_time, '0.7', 'weekly')
        
        # Galeri sayfası
        add_url_to_sitemap(root, f"{domain}/gallery", current_time, '0.6', 'monthly')
        
        # İletişim sayfası
        add_url_to_sitemap(root, f"{domain}/contact", current_time, '0.5', 'monthly')

        # Aktiviteler sayfası
        add_url_to_sitemap(root, f"{domain}/activities", current_time, '0.7', 'weekly')

        # Blog yazıları
        from models.blog import BlogPost
        blog_posts = BlogPost.query.filter_by(status='published').all()
        for post in blog_posts:
            url = ET.SubElement(root, 'url')
            loc = ET.SubElement(url, 'loc')
            loc.text = f"{domain}/blog/{post.slug}"
            lastmod = ET.SubElement(url, 'lastmod')
            lastmod.text = current_time
            changefreq = ET.SubElement(url, 'changefreq')
            changefreq.text = 'monthly'
            priority = ET.SubElement(url, 'priority')
            priority.text = '0.7'
            
            if post.featured_image:
                image = ET.SubElement(url, 'image:image')
                image_loc = ET.SubElement(image, 'image:loc')
                image_loc.text = f"{domain}/static/uploads/blog/{post.featured_image}"
                image_title = ET.SubElement(image, 'image:title')
                image_title.text = post.title_tr

        # Odalar
        from models.room import Room
        rooms = Room.query.filter_by(status='active').all()
        for room in rooms:
            url = ET.SubElement(root, 'url')
            loc = ET.SubElement(url, 'loc')
            loc.text = f"{domain}/rooms/{room.slug}"
            lastmod = ET.SubElement(url, 'lastmod')
            lastmod.text = current_time
            changefreq = ET.SubElement(url, 'changefreq')
            changefreq.text = 'monthly'
            priority = ET.SubElement(url, 'priority')
            priority.text = '0.8'
            
            if room.images_list and len(room.images_list) > 0:
                image = ET.SubElement(url, 'image:image')
                image_loc = ET.SubElement(image, 'image:loc')
                image_loc.text = f"{domain}/static/uploads/rooms/{room.images_list[0]}"
                image_title = ET.SubElement(image, 'image:title')
                image_title.text = room.title_tr

        # Aktiviteler
        from models.activity import Activity
        activities = Activity.query.filter_by(status='active').all()
        for activity in activities:
            url = ET.SubElement(root, 'url')
            loc = ET.SubElement(url, 'loc')
            loc.text = f"{domain}/activities/{activity.slug}"
            lastmod = ET.SubElement(url, 'lastmod')
            lastmod.text = current_time
            changefreq = ET.SubElement(url, 'changefreq')
            changefreq.text = 'monthly'
            priority = ET.SubElement(url, 'priority')
            priority.text = '0.7'

            if activity.featured_image:
                image = ET.SubElement(url, 'image:image')
                image_loc = ET.SubElement(image, 'image:loc')
                image_loc.text = f"{domain}/static/uploads/activities/{activity.featured_image}"
                image_title = ET.SubElement(image, 'image:title')
                image_title.text = activity.title_tr

        # XML string'e çevir - pretty print ile
        from xml.dom import minidom

        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")

        # İlk satırdaki XML declaration'ı kaldır (çünkü kendi ekleyeceğiz)
        lines = pretty_xml.split('\n')
        if lines[0].startswith('<?xml'):
            lines = lines[1:]

        # Boş satırları temizle
        clean_lines = [line for line in lines if line.strip()]

        # XML header ekle
        xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>'
        complete_xml = xml_declaration + '\n' + '\n'.join(clean_lines)

        # Response oluştur
        response = make_response(complete_xml)
        response.headers['Content-Type'] = 'application/xml; charset=utf-8'
        response.headers['Cache-Control'] = 'public, max-age=3600'  # 1 saat cache

        return response
        
    except Exception as e:
        return str(e), 500

def add_url_to_sitemap(root, url, last_modified=None, priority=None, changefreq=None):
    """Sitemap'e URL ekle"""
    url_element = ET.SubElement(root, 'url')
    
    loc = ET.SubElement(url_element, 'loc')
    loc.text = url
    
    if last_modified:
        lastmod = ET.SubElement(url_element, 'lastmod')
        lastmod.text = last_modified
        
    if changefreq:
        changefreq_element = ET.SubElement(url_element, 'changefreq')
        changefreq_element.text = changefreq
        
    if priority:
        priority_element = ET.SubElement(url_element, 'priority')
        priority_element.text = str(priority)
    
    return url_element

def create_meta_description(content, max_length=220):
    """Sayfa içeriğinden kaliteli meta description oluştur"""
    if not content:
        return ""
    
    # BeautifulSoup ile HTML'i temizle
    if isinstance(content, str):
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text(' ', strip=True)
    else:
        text = str(content)
    
    # Metin kısa ise doğrudan kullan
    if len(text) <= max_length:
        return text
    
    # İlk 220 karakteri al ve cümleyi tamamla
    description = text[:max_length]
    last_period = description.rfind('.')
    
    if last_period > max_length * 0.7:  # En az %70'ini kullan
        description = description[:last_period+1]
    else:
        # Cümleyi kesmemek için son boşluğa kadar al
        last_space = description.rfind(' ')
        if last_space > 0:
            description = description[:last_space] + '...'
    
    return description

def create_google_analytics_code(ga_id):
    """Google Analytics kodu oluştur (GA4)"""
    if not ga_id:
        return ""
    
    return f"""
<!-- Google Analytics (GA4) -->
<script async src="https://www.googletagmanager.com/gtag/js?id={ga_id}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){{dataLayer.push(arguments);}}
  gtag('js', new Date());
  gtag('config', '{ga_id}');
</script>
"""

@seo_bp.route('/admin/seo/analytics', methods=['GET', 'POST'])
@login_required
def google_analytics_settings():
    """Google Analytics ayarları için sayfa"""
    from models.setting import Setting, db
    
    if request.method == 'POST':
        try:
            ga_id = request.form.get('ga_id', '')
            
            # Ayarı kaydet/güncelle
            ga_setting = Setting.query.filter_by(key='google_analytics_id').first()
            if ga_setting:
                ga_setting.value = ga_id
            else:
                ga_setting = Setting(key='google_analytics_id', value=ga_id)
                db.session.add(ga_setting)
            
            db.session.commit()
            flash('Google Analytics ayarları güncellendi!', 'success')
            return redirect(url_for('seo.google_analytics_settings'))
        
        except Exception as e:
            db.session.rollback()
            flash(f'Hata: {str(e)}', 'error')
    
    # Mevcut ayarı getir
    ga_setting = Setting.query.filter_by(key='google_analytics_id').first()
    ga_id = ga_setting.value if ga_setting else ''
    
    return render_template('admin/seo/google_analytics.html', ga_id=ga_id)

@seo_bp.route('/admin/seo/analyze/<int:id>')
@login_required
def seo_analyze(id):
    seo = SEO.query.get_or_404(id)
    analysis = {
        'title': {
            'status': 'success' if 30 <= len(seo.title) <= 60 else 'warning',
            'message': f'Başlık uzunluğu: {len(seo.title)}/60 karakter',
            'suggestion': 'İdeal başlık uzunluğu 30-60 karakter arasında olmalıdır.'
        },
        'description': {
            'status': 'success' if 120 <= len(seo.description) <= 155 else 'warning',
            'message': f'Açıklama uzunluğu: {len(seo.description)}/155 karakter',
            'suggestion': 'İdeal açıklama uzunluğu 120-155 karakter arasında olmalıdır.'
        },
        'keywords': {
            'status': 'success' if 5 <= len(seo.keywords.split(',')) <= 8 else 'warning',
            'message': f'Anahtar kelime sayısı: {len(seo.keywords.split(","))}',
            'suggestion': 'İdeal anahtar kelime sayısı 5-8 arasında olmalıdır.'
        },
        'images': {
            'status': 'success' if seo.og_image and seo.twitter_image else 'warning',
            'message': 'Sosyal medya görselleri',
            'suggestion': 'Hem Open Graph hem Twitter Card için görsel eklenmelidir.'
        },
        'schema': {
            'status': 'success' if seo.schema_data else 'warning',
            'message': 'Schema.org işaretlemesi',
            'suggestion': 'Sayfa içeriğine uygun schema.org işaretlemesi eklenmelidir.'
        },
        'mobile_speed': {
            'status': 'success' if seo.page_speed_mobile >= 80 else 'warning',
            'message': f'Mobil hız puanı: {seo.page_speed_mobile}/100',
            'suggestion': 'Mobil hız puanı en az 80/100 olmalıdır.'
        }
    }
    return render_template('admin/seo/analyze.html', seo=seo, analysis=analysis)

def generate_seo_suggestions(content):
    """İçeriğe göre SEO önerileri oluşturur"""
    # Kelimeleri ayır ve say
    words = re.findall(r'\w+', content.lower())
    word_freq = Counter(words).most_common(10)

    # Anahtar kelime önerileri
    suggested_keywords = [word for word, count in word_freq if len(word) > 3]

    # Başlık önerisi
    title_suggestion = ' '.join(suggested_keywords[:5]).title()

    # Açıklama önerisi
    description_suggestion = ' '.join(words[:30]) + '...'

    return {
        'keywords': ', '.join(suggested_keywords),
        'title': title_suggestion,
        'description': description_suggestion
    }

@seo_bp.route('/admin/seo/suggest', methods=['POST'])
@login_required
def seo_suggest():
    content = request.form.get('content', '')
    suggestions = generate_seo_suggestions(content)
    return jsonify(suggestions)

@seo_bp.route('/admin/seo/keyword-analytics')
@login_required
def keyword_analytics():
    try:
        # Son 30 günün istatistiklerini al
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)

        # En çok aranan kelimeler - önce tüm verileri kontrol et
        all_keywords = KeywordAnalytics.query.all()
        print(f"Toplam KeywordAnalytics kayıt sayısı: {len(all_keywords)}")

        # En çok aranan kelimeler
        top_keywords = db.session.query(
            KeywordAnalytics.keyword,
            db.func.sum(KeywordAnalytics.visit_count).label('total_visits')
        ).group_by(
            KeywordAnalytics.keyword
        ).order_by(
            db.desc('total_visits')
        ).limit(20).all()

        print(f"Top keywords sayısı: {len(top_keywords)}")

        # Kaynak bazında dağılım
        sources = db.session.query(
            KeywordAnalytics.source,
            db.func.count(KeywordAnalytics.id).label('count')
        ).group_by(
            KeywordAnalytics.source
        ).all()

        print(f"Sources sayısı: {len(sources)}")

        # Sayfa bazında dağılım
        pages = db.session.query(
            KeywordAnalytics.page_url,
            db.func.count(KeywordAnalytics.id).label('count')
        ).group_by(
            KeywordAnalytics.page_url
        ).order_by(
            db.desc('count')
        ).limit(10).all()

        print(f"Pages sayısı: {len(pages)}")

        return render_template('admin/seo/keyword_analytics.html',
                             top_keywords=top_keywords,
                             sources=sources,
                             pages=pages)
    except Exception as e:
        print(f"Keyword analytics hatası: {str(e)}")
        return render_template('admin/seo/keyword_analytics.html',
                             top_keywords=[],
                             sources=[],
                             pages=[])

@seo_bp.route('/admin/seo/keyword-generator')
@login_required
def keyword_generator():
    return render_template('admin/seo/keyword-generator.html')

@seo_bp.route('/admin/seo/fetch-content', methods=['POST'])
@login_required
def fetch_content():
    try:
        data = request.get_json()
        url = data.get('url')

        if not url:
            return jsonify({'error': 'URL gerekli'}), 400

        # URL'in geçerli olup olmadığını kontrol et
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return jsonify({'error': 'Geçersiz URL formatı'}), 400

        # User-Agent ekleyerek request yap
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Sayfa kodlamasını doğru şekilde ayarla
        response.encoding = response.apparent_encoding

        soup = BeautifulSoup(response.text, 'html.parser')

        # SEO bilgilerini topla
        seo_data = {
            'title': soup.title.string.strip() if soup.title else '',
            'meta': {
                'description': '',
                'keywords': '',
                'author': '',
                'robots': '',
                'viewport': '',
                'og:title': '',
                'og:description': '',
                'og:image': '',
                'twitter:card': '',
                'twitter:title': '',
                'twitter:description': '',
                'twitter:image': ''
            },
            'headings': {
                'h1': [],
                'h2': [],
                'h3': []
            },
            'links': [],
            'images': [],
            'word_count': 0,
            'canonical': ''
        }

        # Meta etiketlerini topla
        for meta in soup.find_all('meta'):
            name = meta.get('name', meta.get('property', '')).lower()
            content = meta.get('content', '').strip()

            if name in seo_data['meta']:
                seo_data['meta'][name] = content

        # Başlıkları topla
        for level in ['h1', 'h2', 'h3']:
            seo_data['headings'][level] = [h.get_text().strip()
                                         for h in soup.find_all(level)]

        # Linkleri topla
        seo_data['links'] = [{'text': a.get_text().strip(),
                             'href': a.get('href')}
                            for a in soup.find_all('a')
                            if a.get('href')]

        # Resimleri topla
        seo_data['images'] = [{'src': img.get('src'),
                              'alt': img.get('alt', '')}
                             for img in soup.find_all('img')
                             if img.get('src')]

        # Kelime sayısını hesapla
        content_text = ' '.join([p.get_text().strip()
                               for p in soup.find_all(['p', 'div', 'span'])])
        seo_data['word_count'] = len(content_text.split())

        # Canonical URL'i al
        canonical = soup.find('link', {'rel': 'canonical'})
        if canonical:
            seo_data['canonical'] = canonical.get('href', '')

        return jsonify({
            'seo_data': seo_data,
            'content': content_text
        })

    except requests.RequestException as e:
        print(f"Request hatası: {str(e)}")
        return jsonify({'error': 'URL erişim hatası: ' + str(e)}), 500
    except Exception as e:
        print(f"Genel hata: {str(e)}")
        return jsonify({'error': 'Bir hata oluştu: ' + str(e)}), 500

@seo_bp.route('/admin/seo/google-console')
@login_required
def google_console():
    """Google Search Console entegrasyonu"""
    return render_template('admin/seo/google_console.html')

@seo_bp.route('/admin/seo/submit-sitemap', methods=['POST'])
@login_required
def submit_sitemap():
    """Sitemap'i Google'a gönder"""
    try:
        site_url = request.host_url.rstrip('/')
        sitemap_url = f"{site_url}/sitemap.xml"

        # Google indexing script'ini çalıştır
        script_path = os.path.join(current_app.root_path, 'scripts', 'google_indexing.py')

        if os.path.exists(script_path):
            try:
                result = subprocess.run([
                    'python', script_path,
                    '--site-url', site_url,
                    '--submit-sitemap',
                    '--validate-sitemap'
                ], capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    flash('Sitemap başarıyla Google\'a gönderildi!', 'success')
                else:
                    flash(f'Sitemap gönderilirken hata oluştu: {result.stderr}', 'error')

            except subprocess.TimeoutExpired:
                flash('İşlem zaman aşımına uğradı. Lütfen tekrar deneyin.', 'error')
            except Exception as e:
                flash(f'Script çalıştırılırken hata: {str(e)}', 'error')
        else:
            flash('Google indexing script bulunamadı.', 'error')

        return redirect(url_for('seo.google_console'))

    except Exception as e:
        flash(f'Bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('seo.google_console'))

@seo_bp.route('/admin/seo/request-indexing', methods=['POST'])
@login_required
def request_indexing():
    """Belirli URL'leri Google'a indexleme için gönder"""
    try:
        urls = request.form.get('urls', '').strip().split('\n')
        urls = [url.strip() for url in urls if url.strip()]

        if not urls:
            flash('En az bir URL girmelisiniz.', 'error')
            return redirect(url_for('seo.google_console'))

        site_url = request.host_url.rstrip('/')
        script_path = os.path.join(current_app.root_path, 'scripts', 'google_indexing.py')

        if os.path.exists(script_path):
            try:
                # URL'leri tam URL'ye çevir
                full_urls = []
                for url in urls:
                    if not url.startswith('http'):
                        url = f"{site_url}{url if url.startswith('/') else '/' + url}"
                    full_urls.append(url)

                result = subprocess.run([
                    'python', script_path,
                    '--site-url', site_url,
                    '--request-indexing'
                ] + full_urls, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    flash(f'{len(full_urls)} URL indexleme için gönderildi!', 'success')
                else:
                    flash(f'Indexleme isteği gönderilirken hata: {result.stderr}', 'error')

            except subprocess.TimeoutExpired:
                flash('İşlem zaman aşımına uğradı. Lütfen tekrar deneyin.', 'error')
            except Exception as e:
                flash(f'Script çalıştırılırken hata: {str(e)}', 'error')
        else:
            flash('Google indexing script bulunamadı.', 'error')

        return redirect(url_for('seo.google_console'))

    except Exception as e:
        flash(f'Bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('seo.google_console'))