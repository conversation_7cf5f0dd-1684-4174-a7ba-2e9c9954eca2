{% extends "admin/base.html" %}

{% block page_title %}Promosyonlar{% endblock %}

{% block admin_content %}
<div class="px-6">
    <!-- <PERSON><PERSON>lık ve Butonlar -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-light text-zinc-800">Promosyonlar</h1>
            <p class="text-zinc-600 mt-1">Zaman sınırlı promosyon kodlarını yönetin</p>
        </div>
        <a href="{{ url_for('admin_promotions.promotion_create') }}" 
           class="bg-gradient-to-r from-amber-500 to-amber-600 text-white px-6 py-2.5 rounded-lg hover:from-amber-600 hover:to-amber-700 transition-all duration-200 shadow-lg hover:shadow-xl">
            <i class="fas fa-plus mr-2"></i>
            Yeni Promosyon
        </a>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-lg">
                    <i class="fas fa-tags text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Toplam Promosyon</p>
                    <p class="text-2xl font-semibold text-zinc-800">{{ promotions|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <i class="fas fa-play text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Aktif Promosyon</p>
                    <p class="text-2xl font-semibold text-zinc-800">{{ active_count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-amber-100 rounded-lg">
                    <i class="fas fa-users text-amber-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Kullanıcı Bazlı</p>
                    <p class="text-2xl font-semibold text-zinc-800">Yeni Sistem</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Promosyon Listesi -->
    <div class="bg-white rounded-xl shadow-sm border border-zinc-100">
        <div class="p-6 border-b border-zinc-100">
            <h2 class="text-lg font-medium text-zinc-800">Tüm Promosyonlar</h2>
        </div>
        
        {% if promotions %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-zinc-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Kod</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Açıklama</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Süre</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Durum</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Kalan Süre</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Oluşturulma</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">İşlemler</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-zinc-200">
                    {% for promotion in promotions %}
                    <tr class="hover:bg-zinc-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-zinc-900">{{ promotion.code }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-zinc-900">
                                {{ promotion.description[:50] + '...' if promotion.description and promotion.description|length > 50 else promotion.description or '-' }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-zinc-900">{{ promotion.duration_minutes }} dk</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if promotion.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    Aktif (Kullanıcı Bazlı)
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-stop mr-1"></i>
                                    Durduruldu
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if promotion.is_active %}
                                <div class="text-sm text-green-600">
                                    <i class="fas fa-user-clock mr-1"></i>
                                    IP bazlı süre
                                </div>
                            {% else %}
                                <div class="text-sm text-zinc-500">-</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-zinc-900">{{ promotion.created_at.strftime('%d.%m.%Y %H:%M') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <!-- Aktif/Pasif -->
                                {% if promotion.is_active %}
                                    <form method="POST" action="{{ url_for('admin_promotions.promotion_stop', promotion_id=promotion.id) }}" class="inline">
                                        <button type="submit" class="text-red-600 hover:text-red-900" title="Pasif Yap">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    </form>
                                {% else %}
                                    <form method="POST" action="{{ url_for('admin_promotions.promotion_start', promotion_id=promotion.id) }}" class="inline">
                                        <button type="submit" class="text-green-600 hover:text-green-900" title="Aktif Yap">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </form>
                                {% endif %}
                                
                                <!-- Düzenle -->
                                <a href="{{ url_for('admin_promotions.promotion_edit', promotion_id=promotion.id) }}" 
                                   class="text-blue-600 hover:text-blue-900" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <!-- İstatistikler -->
                                <a href="{{ url_for('admin_promotions.promotion_views', promotion_id=promotion.id) }}" 
                                   class="text-purple-600 hover:text-purple-900" title="İstatistikler">
                                    <i class="fas fa-chart-bar"></i>
                                </a>
                                
                                <!-- Sil -->
                                <form method="POST" action="{{ url_for('admin_promotions.promotion_delete', promotion_id=promotion.id) }}" 
                                      class="inline" onsubmit="return confirm('Bu promosyonu silmek istediğinizden emin misiniz?')">
                                    <button type="submit" class="text-red-600 hover:text-red-900" title="Sil">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="p-12 text-center">
            <i class="fas fa-tags text-zinc-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-zinc-900 mb-2">Henüz promosyon yok</h3>
            <p class="text-zinc-600 mb-6">İlk promosyonunuzu oluşturmak için aşağıdaki butona tıklayın.</p>
            <a href="{{ url_for('admin_promotions.promotion_create') }}" 
               class="bg-gradient-to-r from-amber-500 to-amber-600 text-white px-6 py-2.5 rounded-lg hover:from-amber-600 hover:to-amber-700 transition-all duration-200">
                <i class="fas fa-plus mr-2"></i>
                Yeni Promosyon Oluştur
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Kalan süreleri güncelle
function updateRemainingTimes() {
    {% for promotion in promotions %}
    {% if promotion.is_running() %}
    const element{{ promotion.id }} = document.getElementById('remaining-{{ promotion.id }}');
    if (element{{ promotion.id }}) {
        fetch('/api/promotion/current')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.promotion.id === {{ promotion.id }}) {
                    const minutes = Math.floor(data.promotion.remaining_seconds / 60);
                    const seconds = data.promotion.remaining_seconds % 60;
                    element{{ promotion.id }}.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                    
                    if (data.promotion.remaining_seconds <= 0) {
                        location.reload();
                    }
                }
            })
            .catch(error => console.error('Error:', error));
    }
    {% endif %}
    {% endfor %}
}

// Her saniye güncelle
setInterval(updateRemainingTimes, 1000);
</script>
{% endblock admin_content %}
