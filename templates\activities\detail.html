{% extends "base.html" %}

{% block title %}{{ activity.get_title(current_language) }} - {% if current_language == 'tr' %}Aktiviteler{% else %}Activities{% endif %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="relative h-[60vh] overflow-hidden">
        {% if activity.featured_image %}
        <img src="{{ url_for('static', filename='uploads/activities/' + activity.featured_image) }}" 
             alt="{{ activity.get_title(current_language) }}"
             class="w-full h-full object-cover">
        {% else %}
        <div class="w-full h-full bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
            <i class="fas fa-mountain text-gold text-6xl"></i>
        </div>
        {% endif %}
        
        <!-- Overlay -->
        <div class="absolute inset-0 bg-black/40"></div>
        
        <!-- Content -->
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white">
                <h1 class="text-3xl md:text-4xl font-great-vibes mb-3">{{ activity.get_title(current_language) }}</h1>
                {% if activity.get_description(current_language) %}
                <p class="text-base md:text-lg max-w-xl mx-auto">{{ activity.get_description(current_language) }}</p>
                {% endif %}
            </div>
        </div>
    </section>

    <!-- Aktivite Detayları -->
    <section class="py-16 bg-[#F5F5F5]">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Ana İçerik -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl p-8 shadow-sm">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6" data-translate="about_activity">Aktivite Hakkında</h2>
                        
                        {% if activity.get_content(current_language) %}
                        <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                            {{ activity.get_content(current_language)|nl2br }}
                        </div>
                        {% endif %}
                        
                        <!-- Galeri -->
                        {% if activity.images_list %}
                        <div class="mt-8">
                            <h3 class="text-2xl font-bold text-gray-900 mb-4" data-translate="gallery">Galeri</h3>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                {% for image in activity.images_list %}
                                <div class="aspect-square overflow-hidden rounded-lg">
                                    <img src="{{ url_for('static', filename='uploads/activities/' + image) }}" 
                                         alt="{{ activity.get_title(current_language) }}"
                                         class="w-full h-full object-cover hover:scale-105 transition-transform duration-300 cursor-pointer"
                                         data-image="{{ url_for('static', filename='uploads/activities/' + image) }}">
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Aktivite Bilgileri -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm">
                        <h3 class="text-xl font-bold text-gray-900 mb-4" data-translate="activity_info">Aktivite Bilgileri</h3>
                        
                        <div class="space-y-4">
                            {% if activity.duration %}
                            <div class="flex items-center">
                                <i class="fas fa-clock text-gold w-5"></i>
                                <span class="ml-3 text-gray-700">
                                    {{ activity.duration }} <span data-translate="duration">Süre</span>
                                </span>
                            </div>
                            {% endif %}
                            
                            {% if activity.difficulty %}
                            <div class="flex items-center">
                                <i class="fas fa-signal text-gold w-5"></i>
                                <span class="ml-3 text-gray-700">
                                    {% if activity.difficulty == 'easy' %}
                                    <span data-translate="difficulty_easy">{{ activity.get_difficulty_display() }}</span>
                                    {% elif activity.difficulty == 'medium' %}
                                    <span data-translate="difficulty_medium">{{ activity.get_difficulty_display() }}</span>
                                    {% elif activity.difficulty == 'hard' %}
                                    <span data-translate="difficulty_hard">{{ activity.get_difficulty_display() }}</span>
                                    {% else %}
                                    {{ activity.get_difficulty_display() }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                            
                            {% if activity.max_participants %}
                            <div class="flex items-center">
                                <i class="fas fa-users text-gold w-5"></i>
                                <span class="ml-3 text-gray-700"><span data-translate="max_people">Maks.</span> {{ activity.max_participants }} <span data-translate="person">kişi</span></span>
                            </div>
                            {% endif %}
                            
                            {% if activity.price %}
                            <div class="flex items-center">
                                <i class="fas fa-tag text-gold w-5"></i>
                                <span class="ml-3 text-gray-700 font-semibold">{{ "%.2f"|format(activity.price) }} ₺</span>
                            </div>
                            {% else %}
                            <div class="flex items-center">
                                <i class="fas fa-gift text-gold w-5"></i>
                                <span class="ml-3 text-green-600 font-semibold" data-translate="free">Ücretsiz</span>
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Rezervasyon Butonu -->
                        <div class="mt-6">
                            <a href="https://wa.me/905333170550?text=Merhaba, {{ activity.get_title(current_language) }} aktivitesi için rezervasyon yapmak istiyorum."
                               target="_blank"
                               class="w-full bg-gold hover:bg-gold/90 text-white py-3 px-6 rounded-full font-semibold transition-colors duration-300 flex items-center justify-center">
                                <i class="fab fa-whatsapp mr-2"></i>
                                <span data-translate="make_reservation">Rezervasyon Yap</span>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Diğer Aktiviteler -->
                    {% if other_activities %}
                    <div class="bg-white rounded-lg p-5 shadow-sm">
                        <h3 class="text-2xl font-extrabold text-gray-900 mb-5 pb-2 border-b-2 border-gray-200">
                            <span data-translate="other_activities">Diğer Aktiviteler</span>
                        </h3>
                        
                        <div class="space-y-3">
                            {% for other_activity in other_activities %}
                            <a href="{{ url_for('activities.activity_detail', slug=other_activity.slug) }}" 
                               class="block group">
                                <div class="flex items-center space-x-3 py-2 border-b border-gray-50 last:border-0">
                                    <!-- Aktivite Resmi -->
                                    {% if other_activity.featured_image %}
                                    <img src="{{ url_for('static', filename='uploads/activities/' + other_activity.featured_image) }}" 
                                         alt="{{ other_activity.get_title(current_language) }}"
                                         class="w-12 h-12 object-cover rounded-md">
                                    {% else %}
                                    <div class="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                                        <i class="fas fa-mountain text-gray-400"></i>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Aktivite Bilgileri -->
                                    <div class="flex-1">
                                        <h4 class="text-base font-semibold text-gray-800 group-hover:text-gold transition-colors">
                                            {{ other_activity.get_title(current_language) }}
                                        </h4>
                                        
                                        {% if other_activity.duration %}
                                        <p class="text-xs text-gray-500 mt-0.5">{{ other_activity.duration }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                            {% endfor %}
                        </div>
                        
                        <!-- Tüm Aktiviteler Butonu -->
                        <div class="mt-4 pt-2 border-t border-gray-100">
                            <a href="{{ url_for('activities.activity_list') }}"
                               class="w-full bg-gold hover:bg-gold/90 text-white py-3 px-6 rounded-full font-semibold transition-colors duration-300 flex items-center justify-center">
                                <i class="fas fa-list-ul mr-2"></i>
                                <span data-translate="view_all_activities">Tüm Aktiviteleri Gör</span>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Lightbox Modal -->
<div id="lightbox" class="fixed inset-0 bg-black/90 z-50 hidden items-center justify-center">
    <div class="relative max-w-4xl max-h-full p-4">
        <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
        <button onclick="closeLightbox()" 
                class="absolute top-4 right-4 text-white text xs hover:text-gold transition-colors">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
function openLightbox(imageSrc) {
    document.getElementById('lightbox-image').src = imageSrc;
    document.getElementById('lightbox').classList.remove('hidden');
    document.getElementById('lightbox').classList.add('flex');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    document.getElementById('lightbox').classList.add('hidden');
    document.getElementById('lightbox').classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Add event listeners to all gallery images
document.addEventListener('DOMContentLoaded', function() {
    const galleryImages = document.querySelectorAll('[data-image]');
    galleryImages.forEach(image => {
        image.addEventListener('click', function() {
            openLightbox(this.getAttribute('data-image'));
        });
    });
});

// ESC tuşu ile kapatma
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});

// Lightbox dışına tıklayınca kapatma
document.getElementById('lightbox').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLightbox();
    }
});
</script>
{% endblock %}
