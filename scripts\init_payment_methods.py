import sys
import os

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import app
from models.slider import db
from models.payment import PaymentMethod, PaymentProvider

def init_payment_methods():
    print("Initializing payment methods...")
    
    # Check for existing payment methods with ID 2 (K<PERSON><PERSON>) and 3 (Havale/EFT)
    credit_card = PaymentMethod.query.get(2)
    bank_transfer = PaymentMethod.query.get(3)
    
    # If the payment method with ID 2 doesn't exist, create it
    if not credit_card:
        # Delete if any payment method with ID 2 exists but is different
        existing = PaymentMethod.query.filter_by(id=2).first()
        if existing:
            db.session.delete(existing)
            db.session.commit()
        
        credit_card = PaymentMethod(
            id=2,
            name='<PERSON><PERSON><PERSON> Kart<PERSON>',
            provider=PaymentProvider.VAKIFBANK,
            description='<PERSON><PERSON><PERSON> ile ödeme',
            is_active=True,
            is_test_mode=True,
            currency='TRY'
        )
        db.session.add(credit_card)
        print("- Created Credit Card payment method with ID 2")
    else:
        print("- Credit Card payment method with ID 2 already exists")
    
    # If the payment method with ID 3 doesn't exist, create it
    if not bank_transfer:
        # Delete if any payment method with ID 3 exists but is different
        existing = PaymentMethod.query.filter_by(id=3).first()
        if existing:
            db.session.delete(existing)
            db.session.commit()
        
        bank_transfer = PaymentMethod(
            id=3,
            name='Havale / EFT',
            provider=PaymentProvider.VAKIFBANK,
            description='Banka havalesi ile ödeme',
            is_active=True,
            is_test_mode=True,
            currency='TRY'
        )
        db.session.add(bank_transfer)
        print("- Created Bank Transfer payment method with ID 3")
    else:
        print("- Bank Transfer payment method with ID 3 already exists")
    
    db.session.commit()
    print("Payment methods initialized successfully!")

if __name__ == '__main__':
    with app.app_context():
        init_payment_methods() 