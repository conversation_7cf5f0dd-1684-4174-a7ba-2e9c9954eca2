#!/usr/bin/env python3
"""
Google Search Console ve Indexing API entegrasyonu
Google dizine otomatik ekleme ve sitemap gönderimi için script
"""

import os
import sys
import json
import requests
from datetime import datetime, timedelta
from urllib.parse import urljoin
import time

# Flask app context için
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class GoogleIndexingManager:
    def __init__(self, site_url, service_account_file=None):
        self.site_url = site_url.rstrip('/')
        self.service_account_file = service_account_file
        self.access_token = None
        
        # Google Search Console API endpoints
        self.search_console_base = "https://searchconsole.googleapis.com/webmasters/v3"
        self.indexing_api_base = "https://indexing.googleapis.com/v3"
        
    def get_access_token(self):
        """Google API için access token al"""
        if not self.service_account_file or not os.path.exists(self.service_account_file):
            print("⚠️  Service account dosyası bulunamadı. Manuel işlemler yapılacak.")
            return None
            
        try:
            from google.oauth2 import service_account
            from google.auth.transport.requests import Request
            
            # Gerekli scope'lar
            scopes = [
                'https://www.googleapis.com/auth/webmasters',
                'https://www.googleapis.com/auth/indexing'
            ]
            
            credentials = service_account.Credentials.from_service_account_file(
                self.service_account_file, scopes=scopes
            )
            
            credentials.refresh(Request())
            self.access_token = credentials.token
            return self.access_token
            
        except ImportError:
            print("⚠️  Google API kütüphaneleri yüklü değil. 'pip install google-auth google-auth-oauthlib google-auth-httplib2' çalıştırın.")
            return None
        except Exception as e:
            print(f"❌ Access token alınırken hata: {e}")
            return None
    
    def submit_sitemap(self, sitemap_url=None):
        """Sitemap'i Google Search Console'a gönder"""
        try:
            if not sitemap_url:
                sitemap_url = f"{self.site_url}/sitemap.xml"
                
            print(f"📤 Sitemap gönderiliyor: {sitemap_url}")
            
            if not self.access_token:
                print("🔧 Manuel sitemap gönderimi için:")
                print(f"   1. https://search.google.com/search-console adresine gidin")
                print(f"   2. '{self.site_url}' property'sini seçin")
                print(f"   3. Sol menüden 'Sitemaps' seçin")
                print(f"   4. '{sitemap_url}' adresini ekleyin")
                return False
                
            try:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                # Sitemap gönder
                url = f"{self.search_console_base}/sites/{self.site_url}/sitemaps/{sitemap_url}"
                response = requests.put(url, headers=headers, timeout=30)
                
                if response.status_code == 200:
                    print("✅ Sitemap başarıyla gönderildi!")
                    return True
                else:
                    print(f"❌ Sitemap gönderilirken hata: {response.status_code} - {response.text}")
                    return False
                    
            except requests.exceptions.Timeout:
                print("❌ Sitemap gönderimi zaman aşımına uğradı, daha sonra tekrar deneyin.")
                return False
            except requests.exceptions.RequestException as e:
                print(f"❌ Sitemap gönderilirken ağ hatası: {e}")
                return False
            except Exception as e:
                print(f"❌ Sitemap gönderilirken beklenmeyen hata: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Sitemap işlemi sırasında kritik hata: {e}")
            return False
    
    def request_indexing(self, urls):
        """Belirli URL'leri Google'a indexleme için gönder"""
        try:
            if not isinstance(urls, list):
                urls = [urls]
                
            print(f"🔍 {len(urls)} URL indexleme için gönderiliyor...")
            
            if not self.access_token:
                print("🔧 Manuel indexleme için:")
                print("   1. https://search.google.com/search-console/index adresine gidin")
                print("   2. URL'leri tek tek 'URL inspection' aracına girin")
                print("   3. 'Request indexing' butonuna tıklayın")
                for url in urls:
                    print(f"      - {url}")
                return False
                
            success_count = 0
            
            for url in urls:
                try:
                    headers = {
                        'Authorization': f'Bearer {self.access_token}',
                        'Content-Type': 'application/json'
                    }
                    
                    data = {
                        "url": url,
                        "type": "URL_UPDATED"
                    }
                    
                    response = requests.post(
                        f"{self.indexing_api_base}/urlNotifications:publish",
                        headers=headers,
                        json=data,
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        print(f"✅ {url} - Indexleme isteği gönderildi")
                        success_count += 1
                    else:
                        print(f"❌ {url} - Hata: {response.status_code}")
                        
                    # Rate limiting için bekle
                    time.sleep(1)
                    
                except requests.exceptions.Timeout:
                    print(f"❌ {url} - İstek zaman aşımına uğradı")
                except requests.exceptions.RequestException as e:
                    print(f"❌ {url} - Ağ hatası: {e}")
                except Exception as e:
                    print(f"❌ {url} - Beklenmeyen hata: {e}")
                    
            print(f"📊 {success_count}/{len(urls)} URL başarıyla gönderildi")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ Indexleme işlemi sırasında kritik hata: {e}")
            return False
    
    def check_indexing_status(self, urls):
        """URL'lerin indexlenme durumunu kontrol et"""
        if not isinstance(urls, list):
            urls = [urls]
            
        print(f"🔍 {len(urls)} URL'nin indexlenme durumu kontrol ediliyor...")
        
        for url in urls:
            try:
                # Google'da site: operatörü ile arama yap
                search_url = f"https://www.google.com/search?q=site:{url}"
                print(f"🔗 {url} için kontrol: {search_url}")
                
                # Basit HTTP isteği ile kontrol
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ {url} - Erişilebilir")
                else:
                    print(f"❌ {url} - HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {url} - Hata: {e}")
    
    def generate_robots_txt(self):
        """Gelişmiş robots.txt içeriği oluştur"""
        robots_content = f"""# Robots.txt for {self.site_url}
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# Tüm robotlar için genel kurallar
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/
Disallow: /private/
Disallow: /*?*
Disallow: /search?
Disallow: /login
Disallow: /logout

# Medya dosyalarına erişimi sınırla
Disallow: /*.pdf$
Disallow: /*.doc$
Disallow: /*.xls$
Disallow: /*.zip$

# Özel bot kuralları
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Googlebot-Image
Allow: /static/images/
Allow: /static/uploads/
Disallow: /static/admin/

User-agent: Bingbot
Allow: /
Crawl-delay: 2

User-agent: facebookexternalhit
Allow: /

# Sitemap bildirimi
Sitemap: {self.site_url}/sitemap.xml

# Host bildirimi (tercih edilen domain)
Host: {self.site_url.replace('https://', '').replace('http://', '')}
"""
        return robots_content
    
    def validate_sitemap(self, sitemap_url=None):
        """Sitemap'in geçerliliğini kontrol et"""
        if not sitemap_url:
            sitemap_url = f"{self.site_url}/sitemap.xml"
            
        print(f"🔍 Sitemap doğrulanıyor: {sitemap_url}")
        
        try:
            response = requests.get(sitemap_url, timeout=10)
            
            if response.status_code != 200:
                print(f"❌ Sitemap erişilemez: HTTP {response.status_code}")
                return False
                
            # XML formatını kontrol et
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(response.content)
                urls = root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url')
                print(f"✅ Sitemap geçerli - {len(urls)} URL bulundu")
                
                # İlk 5 URL'yi göster
                for i, url_elem in enumerate(urls[:5]):
                    loc = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                    if loc is not None:
                        print(f"   {i+1}. {loc.text}")
                        
                if len(urls) > 5:
                    print(f"   ... ve {len(urls)-5} URL daha")
                    
                return True
                
            except ET.ParseError as e:
                print(f"❌ Sitemap XML formatı hatalı: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Sitemap kontrol edilirken hata: {e}")
            return False

def main():
    """Ana fonksiyon - komut satırından çalıştırılabilir"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Google Indexing Manager')
    parser.add_argument('--site-url', required=True, help='Site URL (örn: https://example.com)')
    parser.add_argument('--service-account', help='Google Service Account JSON dosyası')
    parser.add_argument('--submit-sitemap', action='store_true', help='Sitemap gönder')
    parser.add_argument('--validate-sitemap', action='store_true', help='Sitemap doğrula')
    parser.add_argument('--request-indexing', nargs='+', help='Belirli URL\'leri indexleme için gönder')
    parser.add_argument('--check-status', nargs='+', help='URL\'lerin durumunu kontrol et')
    parser.add_argument('--generate-robots', action='store_true', help='Robots.txt oluştur')
    
    args = parser.parse_args()
    
    # Manager oluştur
    manager = GoogleIndexingManager(args.site_url, args.service_account)
    
    # Access token al (eğer service account varsa)
    if args.service_account:
        manager.get_access_token()
    
    # İşlemleri gerçekleştir
    if args.validate_sitemap:
        manager.validate_sitemap()
    
    if args.submit_sitemap:
        manager.submit_sitemap()
    
    if args.request_indexing:
        manager.request_indexing(args.request_indexing)
    
    if args.check_status:
        manager.check_indexing_status(args.check_status)
    
    if args.generate_robots:
        robots_content = manager.generate_robots_txt()
        print("🤖 Robots.txt içeriği:")
        print("=" * 50)
        print(robots_content)
        print("=" * 50)

if __name__ == "__main__":
    main()
