from . import db
from datetime import datetime
import uuid

class Reservation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reservation_code = db.Column(db.String(50), unique=True, default=lambda: str(uuid.uuid4())[:8].upper())
    name = db.<PERSON>umn(db.String(100), nullable=False)
    surname = db.<PERSON>umn(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    check_in = db.Column(db.Date, nullable=False)
    check_out = db.Column(db.Date, nullable=False)
    guests = db.Column(db.Integer, nullable=False)
    room_type = db.Column(db.String(50), nullable=False)
    message = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, confirmed, cancelled, completed
    payment_status = db.Column(db.String(20), default='not_paid')  # not_paid, processing, paid, failed, refunded
    payment_transaction_id = db.Column(db.Integer, db.ForeignKey('payment_transaction.id', ondelete='SET NULL'), nullable=True)
    room_id = db.Column(db.Integer, db.ForeignKey('room.id', ondelete='SET NULL'), nullable=True)
    
    # Ödeme detayları
    amount = db.Column(db.Float, default=0.0)  # Ödeme tutarı
    currency = db.Column(db.String(3), default='TRY')  # Para birimi (TRY, USD, EUR)
    original_amount = db.Column(db.Float, default=0.0)  # Orijinal tutar (Euro cinsinden)
    exchange_rate = db.Column(db.Float)  # İşlem anındaki döviz kuru
    
    # İlave müşteri bilgileri
    country = db.Column(db.String(50))
    id_number = db.Column(db.String(20))  # TC kimlik no veya pasaport no
    id_type = db.Column(db.String(20))  # TC, Pasaport vs
    address = db.Column(db.Text)
    special_requests = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # İlişkiler
    room = db.relationship('Room', backref='reservations')
    payment_transaction = db.relationship('PaymentTransaction', backref='reservation')

    def __repr__(self):
        return f'<Reservation {self.name} {self.surname} - {self.reservation_code}>'
        
    def get_total_nights(self):
        """Toplam konaklama gecesi sayısını döndürür"""
        if self.check_in and self.check_out:
            delta = self.check_out - self.check_in
            return delta.days
        return 0
        
    def calculate_price(self, room=None):
        """Rezervasyon için toplam fiyatı hesaplar"""
        if not room and self.room_id:
            from models.room import Room
            room = Room.query.get(self.room_id)
            
        if room and room.price:
            nights = self.get_total_nights()
            return room.price * nights * self.guests
        return 0.0
        
    def get_pdf_filename(self):
        """Rezervasyon PDF'i için dosya adı oluşturur"""
        return f"reservation_{self.reservation_code}.pdf"