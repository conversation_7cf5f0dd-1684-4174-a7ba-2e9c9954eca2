from flask import Blueprint, render_template, request, redirect, url_for, flash, abort, jsonify, g
from flask_login import login_required, current_user
from models.blog import BlogPost
from models.setting import db
from slugify import slugify
from datetime import datetime
from werkzeug.utils import secure_filename
import os

blog_bp = Blueprint('blog', __name__)

# Blog blueprint'in başına ekleyin
def get_lang_text(obj, field, lang=None):
    """Dile göre içerik getir"""
    if not lang:
        lang = g.get('language', 'tr')
    method_name = f'get_{field}'
    if hasattr(obj, method_name):
        return getattr(obj, method_name)(lang)
    return getattr(obj, f'{field}_{lang}', '') or getattr(obj, f'{field}_tr', '')

# Context processor ekleyin
@blog_bp.context_processor
def utility_processor():
    def get_text(obj, field, lang=None):
        return get_lang_text(obj, field, lang)
    return dict(get_lang_text=get_text)

# Blog ana sayfası
@blog_bp.route('/blog')
def blog_index():
    page = request.args.get('page', 1, type=int)
    current_language = g.get('language', 'tr')
    
    posts = BlogPost.query.filter_by(status='published')\
        .order_by(BlogPost.created_at.desc())\
        .paginate(page=page, per_page=9)
    
    return render_template('blog/index.html', 
                         posts=posts,
                         current_language=current_language)

# Blog detay sayfası
@blog_bp.route('/blog/<slug>')
def blog_detail(slug):
    post = BlogPost.query.filter_by(slug=slug).first_or_404()
    current_language = g.get('language', 'tr')
    
    # Görüntülenme sayısını artır
    post.view_count += 1
    db.session.commit()
    
    # Benzer yazıları getir
    similar_posts = post.get_similar_posts(limit=3)
    
    return render_template('blog/detail.html', 
                         post=post, 
                         similar_posts=similar_posts,
                         current_language=current_language)

# Blog yazısı oluşturma
@blog_bp.route('/admin/blog/create', methods=['GET', 'POST'])
@login_required
def blog_create():
    if request.method == 'POST':
        try:
            # Form verilerini işle
            title_tr = request.form.get('title_tr')
            title_en = request.form.get('title_en')
            content_tr = request.form.get('content_tr')
            content_en = request.form.get('content_en')
            excerpt_tr = request.form.get('excerpt_tr')
            excerpt_en = request.form.get('excerpt_en')
            category = request.form.get('category', 'Genel')
            status = request.form.get('status', 'draft')
            meta_description_tr = request.form.get('meta_description_tr')
            meta_description_en = request.form.get('meta_description_en')
            
            # Slug oluştur veya al
            slug = request.form.get('slug')
            if not slug:
                slug = slugify(title_tr)
            
            # Dosya yükleme işlemi
            featured_image = None
            if 'featured_image' in request.files:
                file = request.files['featured_image']
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    file.save(os.path.join('static', 'uploads', 'blog', filename))
                    featured_image = filename

            # Blog kaydını yap
            new_post = BlogPost(
                title_tr=title_tr,
                title_en=title_en,
                slug=slug,
                content_tr=content_tr,
                content_en=content_en,
                excerpt_tr=excerpt_tr,
                excerpt_en=excerpt_en,
                category=category,
                status=status,
                meta_description_tr=meta_description_tr,
                meta_description_en=meta_description_en,
                featured_image=featured_image,
                author=current_user.username
            )
            
            db.session.add(new_post)
            db.session.commit()
            
            flash('Blog yazısı başarıyla oluşturuldu!', 'success')
            return redirect(url_for('blog.blog_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
            return redirect(url_for('blog.blog_create'))
    
    current_language = g.get('language', 'tr')
    return render_template('admin/blog/create.html',
                         current_language=current_language)

# Blog yazısı düzenleme
@blog_bp.route('/admin/blog/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def blog_edit(id):
    post = BlogPost.query.get_or_404(id)
    current_language = g.get('language', 'tr')
    
    if request.method == 'POST':
        try:
            post.title_tr = request.form.get('title_tr')
            post.title_en = request.form.get('title_en')
            post.content_tr = request.form.get('content_tr')
            post.content_en = request.form.get('content_en')
            post.excerpt_tr = request.form.get('excerpt_tr')
            post.excerpt_en = request.form.get('excerpt_en')
            post.category = request.form.get('category')
            post.status = request.form.get('status', 'draft')
            post.meta_description_tr = request.form.get('meta_description_tr')
            post.meta_description_en = request.form.get('meta_description_en')
            
            # Slug güncelleme
            if request.form.get('update_slug'):
                post.slug = slugify(post.title_tr)
            
            # Dosya yükleme işlemi
            if 'featured_image' in request.files:
                file = request.files['featured_image']
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    file.save(os.path.join('static', 'uploads', 'blog', filename))
                    post.featured_image = filename
            
            db.session.commit()
            flash('Blog yazısı başarıyla güncellendi.', 'success')
            return redirect(url_for('blog.blog_edit', id=post.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/blog/edit.html', 
                         post=post,
                         current_language=current_language)

# Blog yazısı önizleme
@blog_bp.route('/admin/blog/preview/<int:id>')
@login_required
def blog_preview(id):
    post = BlogPost.query.get_or_404(id)
    return render_template('blog/preview.html', post=post)

# Blog yazısı silme
@blog_bp.route('/admin/blog/delete/<int:id>', methods=['POST'])
@login_required
def blog_delete(id):
    post = BlogPost.query.get_or_404(id)
    
    try:
        db.session.delete(post)
        db.session.commit()
        flash('Blog yazısı başarıyla silindi.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Bir hata oluştu: ' + str(e), 'error')
        
    return redirect(url_for('blog.blog_list'))

# Etiketlere göre blog yazıları
@blog_bp.route('/blog/tag/<tag>')
def blog_by_tag(tag):
    page = request.args.get('page', 1, type=int)
    posts = BlogPost.query.filter(
        BlogPost.status == 'published',
        BlogPost.tags.like(f'%{tag}%')
    ).order_by(BlogPost.created_at.desc())\
     .paginate(page=page, per_page=9)
    
    return render_template('blog/index.html', 
                         posts=posts, 
                         tag=tag)

# Kategoriye göre blog yazıları
@blog_bp.route('/blog/category/<category>')
def blog_by_category(category):
    page = request.args.get('page', 1, type=int)
    posts = BlogPost.query.filter_by(
        status='published',
        category=category
    ).order_by(BlogPost.created_at.desc())\
     .paginate(page=page, per_page=9)
    
    return render_template('blog/index.html', 
                         posts=posts, 
                         category=category)

# Blog listesi route'u ekleyelim
@blog_bp.route('/admin/blog')
@login_required
def blog_list():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status')
    search = request.args.get('search')
    
    query = BlogPost.query
    
    # Filtreleme
    if status:
        query = query.filter_by(status=status)
    if search:
        query = query.filter(BlogPost.title.ilike(f'%{search}%'))
    
    # Sıralama ve sayfalama
    posts = query.order_by(BlogPost.created_at.desc())\
        .paginate(page=page, per_page=10)
    
    # Toplam post sayısını al
    total_posts = query.count()
    
    return render_template('admin/blog/list.html', 
                         posts=posts,
                         total_posts=total_posts,
                         min=min)

# Görsel silme route'u ekleyelim
@blog_bp.route('/admin/blog/remove-image/<int:id>', methods=['POST'])
@login_required
def remove_image(id):
    post = BlogPost.query.get_or_404(id)
    
    if post.featured_image:
        # Görseli dosya sisteminden sil
        image_path = os.path.join('static', 'uploads', 'blog', post.featured_image)
        if os.path.exists(image_path):
            os.remove(image_path)
        
        # Veritabanından görsel referansını kaldır
        post.featured_image = None
        db.session.commit()
        
        return {'success': True, 'message': 'Görsel başarıyla silindi'}
    
    return {'success': False, 'message': 'Görsel bulunamadı'}, 404 

# Blog modülüne görsel yükleme endpoint'i ekleyelim
@blog_bp.route('/admin/blog/upload-image', methods=['POST'])
@login_required
def upload_image():
    try:
        file = request.files.get('file')
        if file:
            filename = secure_filename(file.filename)
            # Benzersiz dosya adı oluştur
            base, ext = os.path.splitext(filename)
            filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
            
            # Dosyayı kaydet
            file_path = os.path.join('static', 'uploads', 'blog', filename)
            file.save(file_path)
            
            # URL'i döndür
            return jsonify({
                'location': url_for('static', filename=f'uploads/blog/{filename}')
            })
            
    except Exception as e:
        return jsonify({'error': str(e)}), 400
    
    return jsonify({'error': 'Dosya yüklenemedi'}), 400 

@blog_bp.route('/blog/<int:id>')
def blog_detail_id(id):
    post = BlogPost.query.get_or_404(id)
    return redirect(url_for('blog.blog_detail', slug=post.slug), code=301) 