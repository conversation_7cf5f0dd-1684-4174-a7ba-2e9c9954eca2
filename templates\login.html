<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ settings.site_title or 'Zeppelin Hotel' }} - Yö<PERSON>im <PERSON>i</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts: Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- Tailwind Config -->
    <script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    gold: '#C6A87D',
                    dark: '#141414',
                },
                fontFamily: {
                    sans: ['Inter', 'sans-serif'],
                },
                boxShadow: {
                    gold: '0 4px 32px 0 rgba(198,168,125,0.15)',
                }
            }
        }
    }
    </script>

    <style>
      body { font-family: 'Inter', sans-serif; }
      .gold-gradient {
        background: linear-gradient(90deg, #C6A87D 0%, #B39165 100%);
      }
      .glass {
        background: rgba(24,24,24,0.65);
        backdrop-filter: blur(18px) saturate(1.2);
        border: 1.5px solid rgba(198,168,125,0.13);
        box-shadow: 0 8px 40px 0 rgba(198,168,125,0.10), 0 1.5px 0 0 #C6A87D inset;
      }
      .input-glass {
        background: rgba(36,36,36,0.85);
        box-shadow: 0 2px 8px 0 rgba(198,168,125,0.07) inset;
      }
      .input-glass:focus {
        background: rgba(44,44,44,0.95);
        box-shadow: 0 0 0 2px #C6A87D33, 0 2px 8px 0 rgba(198,168,125,0.13) inset;
      }
      .gold-shadow {
        box-shadow: 0 2px 16px 0 rgba(198,168,125,0.18);
      }
    </style>
</head>
<body class="bg-gradient-to-br from-[#181c23] via-[#23272f] to-[#141414] min-h-screen flex items-center justify-center relative">
    <div class="relative z-10 w-full max-w-lg mx-auto p-0 sm:p-2 flex flex-col items-center">
        <div class="glass gold-shadow w-full rounded-3xl px-8 py-10 sm:py-14 flex flex-col items-center">
            <div class="flex flex-col items-center mb-8">
                {% if settings.site_logo_small %}
                    <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo_small) }}" alt="{{ settings.site_title }}" class="h-20 mb-3 drop-shadow-lg">
                {% else %}
                    <div class="flex flex-col items-center mb-3">
                        <i class="fas fa-hotel text-5xl text-gold mb-2 drop-shadow"></i>
                        <h1 class="text-2xl sm:text-3xl font-extrabold text-white/95 text-center leading-tight tracking-tight">
                            {{ settings.site_title or 'Zeppelin Hotel' }}
                            <span class="block text-base font-normal text-gold mt-1">Kapadokya - Resmi Web Sitesi</span>
                        </h1>
                    </div>
                {% endif %}
            </div>
            <h2 class="text-xl sm:text-2xl font-bold text-white/90 text-center mb-2">Yönetici Girişi</h2>
            <p class="text-zinc-400 text-center mb-7">Yönetim panelinize erişmek için giriş yapın</p>
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-4 px-4 py-3 bg-red-900/20 border border-red-700/30 text-red-400 text-sm rounded-xl text-center">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            <form method="POST" action="{{ url_for('login') }}" class="space-y-6 w-full max-w-md mx-auto">
                <div class="relative">
                    <input type="text" name="username" required placeholder="Kullanıcı Adı" class="input-glass w-full px-5 py-4 rounded-xl text-white/90 placeholder-zinc-400 border border-zinc-700 focus:border-gold focus:ring-2 focus:ring-gold outline-none transition pr-12">
                    <i class="fas fa-user absolute right-5 top-1/2 -translate-y-1/2 text-gold/70 text-lg pointer-events-none"></i>
                </div>
                <div class="relative">
                    <input type="password" name="password" required placeholder="Şifre" class="input-glass w-full px-5 py-4 rounded-xl text-white/90 placeholder-zinc-400 border border-zinc-700 focus:border-gold focus:ring-2 focus:ring-gold outline-none transition pr-12">
                    <i class="fas fa-lock absolute right-5 top-1/2 -translate-y-1/2 text-gold/70 text-lg pointer-events-none"></i>
                </div>
                <div class="flex items-center select-none">
                    <input type="checkbox" name="remember_me" id="remember_me" class="rounded border-zinc-600 bg-zinc-800 text-gold focus:ring-gold">
                    <label for="remember_me" class="ml-2 text-sm text-zinc-400 hover:text-white cursor-pointer">Beni hatırla</label>
                </div>
                <button type="submit" class="gold-gradient w-full py-4 rounded-xl text-white font-semibold tracking-wide shadow-md hover:brightness-110 hover:scale-[1.03] active:scale-95 transition-all duration-150 text-lg mt-2">Giriş Yap</button>
            </form>
            <div class="mt-8 text-center w-full">
                <a href="#" class="inline-flex items-center justify-center gap-2 text-sm text-zinc-300 hover:text-gold transition font-medium px-3 py-2 rounded-lg hover:bg-gold/10">
                    <i class="fas fa-question-circle"></i>
                    <span>Şifrenizi mi unuttunuz?</span>
                </a>
            </div>
        </div>
    </div>
</body>
</html>