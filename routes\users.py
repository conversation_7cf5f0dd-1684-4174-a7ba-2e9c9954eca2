from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models.user import User, db
from datetime import datetime

users_bp = Blueprint('users', __name__)

@users_bp.route('/admin/users')
@login_required
def user_list():
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok!', 'error')
        return redirect(url_for('admin'))
    
    users = User.query.all()
    return render_template('admin/users/list.html', users=users)

@users_bp.route('/admin/users/create', methods=['GET', 'POST'])
@login_required
def user_create():
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok!', 'error')
        return redirect(url_for('admin'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'

        if User.query.filter_by(username=username).first():
            flash('Bu kullanıcı adı zaten kullanılıyor!', 'error')
            return render_template('admin/users/create.html')

        if User.query.filter_by(email=email).first():
            flash('Bu e-posta adresi zaten kullanılıyor!', 'error')
            return render_template('admin/users/create.html')

        user = User(username=username, email=email, is_admin=is_admin)
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Kullanıcı başarıyla oluşturuldu!', 'success')
        return redirect(url_for('users.user_list'))

    return render_template('admin/users/create.html')

@users_bp.route('/admin/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def user_edit(id):
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok!', 'error')
        return redirect(url_for('admin'))

    user = User.query.get_or_404(id)
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        new_password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        active = request.form.get('active') == 'on'

        existing_user = User.query.filter_by(username=username).first()
        if existing_user and existing_user.id != id:
            flash('Bu kullanıcı adı zaten kullanılıyor!', 'error')
            return render_template('admin/users/edit.html', user=user)

        existing_email = User.query.filter_by(email=email).first()
        if existing_email and existing_email.id != id:
            flash('Bu e-posta adresi zaten kullanılıyor!', 'error')
            return render_template('admin/users/edit.html', user=user)

        user.username = username
        user.email = email
        user.is_admin = is_admin
        user.active = active
        
        if new_password:
            user.set_password(new_password)
        
        db.session.commit()
        flash('Kullanıcı başarıyla güncellendi!', 'success')
        return redirect(url_for('users.user_list'))
    
    return render_template('admin/users/edit.html', user=user)

@users_bp.route('/admin/users/delete/<int:id>')
@login_required
def user_delete(id):
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok!', 'error')
        return redirect(url_for('admin'))

    user = User.query.get_or_404(id)
    
    if user.id == current_user.id:
        flash('Kendinizi silemezsiniz!', 'error')
        return redirect(url_for('users.user_list'))
    
    db.session.delete(user)
    db.session.commit()
    flash('Kullanıcı başarıyla silindi!', 'success')
    return redirect(url_for('users.user_list'))