from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.promotion import Promotion, PromotionView, db, get_turkey_time
from datetime import datetime, timedelta

# Admin promosyon işlemleri için blueprint
promotions_bp = Blueprint('admin_promotions', __name__, url_prefix='/admin/promotions')

# API için ayrı blueprint
promotion_api_bp = Blueprint('promotion_api', __name__, url_prefix='/api/promotion')

@promotions_bp.route('/')
@login_required
def promotion_list():
    """Promosyon listesi"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))
    
    try:
        # Süresi dolmuş promosyonları temizle
        Promotion.cleanup_expired()
        
        # Tüm promosyonları getir
        promotions = Promotion.query.order_by(Promotion.created_at.desc()).all()
        
        # Aktif promosyon sayısı
        active_count = Promotion.query.filter_by(is_active=True).count()
        
        return render_template('admin/promotions/list.html', 
                             promotions=promotions,
                             active_count=active_count)
    except Exception as e:
        flash(f'Promosyonlar yüklenirken hata oluştu: {str(e)}', 'error')
        return redirect(url_for('admin_bp.dashboard'))

@promotions_bp.route('/create', methods=['GET', 'POST'])
@login_required
def promotion_create():
    """Yeni promosyon oluştur"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))
    
    if request.method == 'POST':
        try:
            code = request.form.get('code', '').strip()
            description = request.form.get('description', '').strip()
            duration_minutes = int(request.form.get('duration_minutes', 30))

            # Validasyon
            if not code:
                flash('Promosyon kodu gereklidir.', 'error')
                return render_template('admin/promotions/create.html')

            if duration_minutes < 1 or duration_minutes > 1440:  # Max 24 saat
                flash('Süre 1-1440 dakika arasında olmalıdır.', 'error')
                return render_template('admin/promotions/create.html')

            # Aynı kodda aktif promosyon var mı kontrol et
            existing = Promotion.query.filter_by(code=code, is_active=True).first()
            if existing:
                flash('Bu kod ile aktif bir promosyon zaten mevcut.', 'error')
                return render_template('admin/promotions/create.html')

            # Yeni promosyon oluştur (artık otomatik olarak aktif ve hazır durumda)
            promotion = Promotion(
                code=code,
                description=description,
                duration_minutes=duration_minutes,
                created_by=current_user.id
            )

            db.session.add(promotion)
            db.session.commit()

            flash(f'Promosyon "{code}" başarıyla oluşturuldu! Kullanıcılar çarkı çevirdiğinde {duration_minutes} dakikalık süre başlayacak.', 'success')

            return redirect(url_for('admin_promotions.promotion_list'))
            
        except ValueError:
            flash('Geçersiz süre değeri.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'Promosyon oluşturulurken hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/promotions/create.html')

@promotions_bp.route('/<int:promotion_id>/edit', methods=['GET', 'POST'])
@login_required
def promotion_edit(promotion_id):
    """Promosyon düzenle"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))
    
    promotion = Promotion.query.get_or_404(promotion_id)
    
    if request.method == 'POST':
        try:
            code = request.form.get('code', '').strip()
            description = request.form.get('description', '').strip()
            duration_minutes = int(request.form.get('duration_minutes', 30))
            
            # Validasyon
            if not code:
                flash('Promosyon kodu gereklidir.', 'error')
                return render_template('admin/promotions/edit.html', promotion=promotion)
            
            if duration_minutes < 1 or duration_minutes > 1440:
                flash('Süre 1-1440 dakika arasında olmalıdır.', 'error')
                return render_template('admin/promotions/edit.html', promotion=promotion)
            
            # Aynı kodda başka aktif promosyon var mı kontrol et
            existing = Promotion.query.filter_by(code=code, is_active=True).filter(
                Promotion.id != promotion_id
            ).first()
            if existing:
                flash('Bu kod ile başka bir aktif promosyon mevcut.', 'error')
                return render_template('admin/promotions/edit.html', promotion=promotion)
            
            # Güncelle
            promotion.code = code
            promotion.description = description
            promotion.duration_minutes = duration_minutes
            
            # Eğer promosyon çalışıyorsa bitiş zamanını güncelle
            if promotion.is_running():
                promotion.end_time = promotion.start_time + timedelta(minutes=duration_minutes)
            
            db.session.commit()
            flash('Promosyon başarıyla güncellendi!', 'success')
            return redirect(url_for('admin_promotions.promotion_list'))
            
        except ValueError:
            flash('Geçersiz süre değeri.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'Promosyon güncellenirken hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/promotions/edit.html', promotion=promotion)

@promotions_bp.route('/<int:promotion_id>/start', methods=['POST'])
@login_required
def promotion_start(promotion_id):
    """Promosyonu aktif yap"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))

    try:
        promotion = Promotion.query.get_or_404(promotion_id)

        if promotion.is_active:
            flash('Promosyon zaten aktif.', 'warning')
        else:
            promotion.is_active = True
            db.session.commit()
            flash(f'Promosyon "{promotion.code}" aktif hale getirildi! Kullanıcılar çarkı çevirdiğinde süre başlayacak.', 'success')
    except Exception as e:
        flash(f'Promosyon aktif edilirken hata oluştu: {str(e)}', 'error')

    return redirect(url_for('admin_promotions.promotion_list'))

@promotions_bp.route('/<int:promotion_id>/stop', methods=['POST'])
@login_required
def promotion_stop(promotion_id):
    """Promosyonu pasif yap"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))

    try:
        promotion = Promotion.query.get_or_404(promotion_id)
        promotion.stop_promotion()
        flash(f'Promosyon "{promotion.code}" pasif hale getirildi!', 'success')
    except Exception as e:
        flash(f'Promosyon pasif edilirken hata oluştu: {str(e)}', 'error')

    return redirect(url_for('admin_promotions.promotion_list'))

@promotions_bp.route('/<int:promotion_id>/delete', methods=['POST'])
@login_required
def promotion_delete(promotion_id):
    """Promosyonu sil"""
    if not current_user.is_admin:
        flash('Bu işlem için yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))
    
    try:
        promotion = Promotion.query.get_or_404(promotion_id)
        code = promotion.code
        
        db.session.delete(promotion)
        db.session.commit()
        
        flash(f'Promosyon "{code}" başarıyla silindi!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Promosyon silinirken hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('admin_promotions.promotion_list'))

@promotions_bp.route('/<int:promotion_id>/views')
@login_required
def promotion_views(promotion_id):
    """Promosyon görüntüleme istatistikleri"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('admin_bp.dashboard'))
    
    promotion = Promotion.query.get_or_404(promotion_id)
    views = PromotionView.query.filter_by(promotion_id=promotion_id).order_by(
        PromotionView.first_view.desc()
    ).all()
    
    return render_template('admin/promotions/views.html',
                         promotion=promotion,
                         views=views)

# Frontend API Endpoints - Yeni blueprint kullanımı
@promotion_api_bp.route('/current')
def api_current_promotion():
    """Aktif promosyonu getir (Frontend için)"""
    try:
        # IP adresini al
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip_address:
            ip_address = ip_address.split(',')[0].strip()

        # Aktif promosyonu bul
        promotion = Promotion.get_active_promotion()

        if not promotion:
            return jsonify({'success': False, 'message': 'Aktif promosyon bulunamadı'})

        # Kullanıcı bu promosyonu görebilir mi?
        if not promotion.can_user_view(ip_address):
            return jsonify({'success': False, 'message': 'Promosyon görüntüleme süresi doldu'})

        # Bu IP için kalan süreyi hesapla (record_view çağırmadan)
        remaining_seconds = promotion.get_remaining_time_for_ip(ip_address)

        # Promosyon bilgilerini döndür
        promotion_data = promotion.to_dict()
        promotion_data['remaining_seconds'] = remaining_seconds
        promotion_data['remaining_time'] = int(remaining_seconds / 60)

        return jsonify({
            'success': True,
            'promotion': promotion_data
        })

    except Exception as e:
        print(f"API hatası (current): {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@promotion_api_bp.route('/available')
def api_available_promotion():
    """Promosyon varsa ve aktif ise, kullanıcı için geçerli promosyon bilgilerini döndür"""
    try:
        print("🔍 API Available - IP:", request.remote_addr)
        
        # IP adresini al
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip_address:
            ip_address = ip_address.split(',')[0].strip()
        
        # Aktif promosyonu bul
        promotion = Promotion.get_active_promotion()
        
        if not promotion:
            # Aktif promosyon yok
            return jsonify({
                'success': False,
                'available': False, 
                'message': 'Promosyon bulunamadı'
            })
        
        # Bu IP'nin daha önce bu promosyonu görüp görmediğini kontrol et
        view = PromotionView.query.filter_by(
            promotion_id=promotion.id,
            ip_address=ip_address
        ).first()
        
        print(f"🔍 Existing view for IP {ip_address}: {view}")
        
        if view:
            # Süre geçmiş mi kontrol et
            if view.is_expired():
                print("⌛ Süre doldu, promosyon kullanılamaz")
                # Süre doldu, artık bu promosyonu göremez
                return jsonify({
                    'success': False,
                    'available': False, 
                    'message': 'Promosyon süresi doldu'
                })
            else:
                # Süre hala geçerli, kalan zamanı hesapla
                remaining = view.get_remaining_seconds()
                print(f"⏱️ Kalan süre: {remaining} saniye")
                
                # Süre 5 dakikadan az ise, özel mesaj
                if remaining < 300:  # 5 dakikadan az
                    return jsonify({
                        'success': True,
                        'available': True,
                        'urgent': True,
                        'message': f'Promosyon kodunuzu kullanmak için {int(remaining / 60)} dakika {remaining % 60} saniyeniz kaldı!',
                        'code': promotion.code,
                        'remaining': remaining
                    })
                else:
                    # Normal görünüm
                    return jsonify({
                        'success': True,
                        'available': True,
                        'urgent': False,
                        'message': f'Promosyon kodunuz: {promotion.code}',
                        'code': promotion.code,
                        'remaining': remaining
                    })
        else:
            # İlk defa görüyor - artık promosyon kullanılabilir
            print("🎯 No record found, promotion available")
            return jsonify({
                'success': True,
                'available': False, 
                'message': 'Promosyon henüz başlatılmadı, çarkı çevirin!'
            })
        
    except Exception as e:
        print(f"API hatası (available): {str(e)}")
        return jsonify({
            'success': False,
            'available': False, 
            'message': 'Bir hata oluştu'
        })

@promotion_api_bp.route('/start', methods=['POST'])
def api_start_promotion():
    """Promosyonu başlat (Frontend'den çağrılır)"""
    try:
        # IP adresini al
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip_address:
            ip_address = ip_address.split(',')[0].strip()

        # Aktif promosyonu bul
        promotion = Promotion.get_active_promotion()
        
        if not promotion:
            # Aktif promosyon yok
            return jsonify({'success': False, 'message': 'Promosyon bulunamadı'})

        # Bu IP'nin daha önce bu promosyonu görüp görmediğini kontrol et
        view = PromotionView.query.filter_by(
            promotion_id=promotion.id,
            ip_address=ip_address
        ).first()

        if view and not view.is_expired():
            # Zaten aktif bir görüntüleme var, tekrar başlatma
            return jsonify({
                'success': True, 
                'message': 'Promosyon zaten başlatılmış',
                'code': promotion.code,
                'description': promotion.description,
                'duration': promotion.duration_minutes,
                'remaining': view.get_remaining_seconds(),
                'end_time': view.end_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # Yeni bir görüntüleme kaydı oluştur veya eskisini güncelle
        promotion.record_view(ip_address)
        
        # Güncellenmiş görüntüleme kaydını al
        view = PromotionView.query.filter_by(
            promotion_id=promotion.id,
            ip_address=ip_address
        ).first()
        
        return jsonify({
            'success': True,
            'message': 'Promosyon başarıyla başlatıldı!',
            'code': promotion.code,
            'description': promotion.description,
            'duration': promotion.duration_minutes,
            'end_time': view.end_time.strftime('%Y-%m-%d %H:%M:%S') if view else None
        })
        
    except Exception as e:
        print(f"API hatası (start): {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@promotion_api_bp.route('/check/<ip>')
def api_check_promotion(ip):
    """Belirli bir IP için promosyon durumunu kontrol et"""
    if not ip:
        return jsonify({'success': False, 'error': 'IP adresi gerekli'})
        
    # Aktif promosyonu bul
    promotion = Promotion.get_active_promotion()
    
    if not promotion:
        return jsonify({'success': False, 'message': 'Aktif promosyon yok'})
    
    # Bu IP'nin görüntüleme kaydını kontrol et
    view = PromotionView.query.filter_by(
        promotion_id=promotion.id,
        ip_address=ip
    ).first()
    
    if not view:
        return jsonify({
            'success': True,
            'can_view': False,
            'message': 'Promosyon mevcut ama henüz başlatılmamış',
            'promotion': None
        })
    
    # Süre geçmiş mi kontrol et
    if view.is_expired():
        return jsonify({
            'success': True,
            'can_view': False,
            'message': 'Bu promosyonun süresi dolmuş',
            'promotion': None
        })
    
    # Aktif ve süresi geçmemiş
    remaining = view.get_remaining_seconds()
    
    # Promosyon bilgilerini döndür
    promotion_data = promotion.to_dict()
    promotion_data['remaining_seconds'] = remaining
    promotion_data['remaining_time'] = int(remaining / 60)
    
    return jsonify({
        'success': True,
        'can_view': True,
        'promotion': promotion_data
    })
