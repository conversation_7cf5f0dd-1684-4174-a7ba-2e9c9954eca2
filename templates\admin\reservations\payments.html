{% extends "admin/base.html" %}

{% block admin_content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Ödeme İşlemleri</h1>
    </div>

    <!-- Filtreler -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-800 mb-4">Filtreleme ve Arama</h2>
        <form action="{{ url_for('reservation.payment_list') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Ödeme Durumu</label>
                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold">
                    <option value="">Tüm Durumlar</option>
                    <option value="paid" {% if status == 'paid' %}selected{% endif %}>Ödendi</option>
                    <option value="processing" {% if status == 'processing' %}selected{% endif %}>İşlemde</option>
                    <option value="failed" {% if status == 'failed' %}selected{% endif %}>Başarısız</option>
                    <option value="refunded" {% if status == 'refunded' %}selected{% endif %}>İade Edildi</option>
                </select>
            </div>
            <div>
                <label for="code" class="block text-sm font-medium text-gray-700 mb-1">İşlem/Rezervasyon Kodu</label>
                <input type="text" id="code" name="code" value="{{ code or '' }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold"
                       placeholder="İşlem kodu veya rezervasyon kodu">
            </div>
            <div class="flex items-end">
                <button type="submit" class="px-4 py-2 bg-gold hover:bg-dark-gold text-white rounded-md transition-colors h-10">
                    <i class="fas fa-search mr-2"></i> Ara
                </button>
                {% if status or code %}
                <a href="{{ url_for('reservation.payment_list') }}" class="ml-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors h-10 inline-flex items-center">
                    <i class="fas fa-times mr-2"></i> Temizle
                </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Helper function to convert UTC to Turkey time -->
    {% macro to_turkey_time(utc_time) %}
        {% set turkey_time = utc_time.replace(tzinfo=None) + timedelta(hours=3) %}
        {{ turkey_time }}
    {% endmacro %}

    <!-- Ödeme Listesi -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        {% if payments %}
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="bg-gray-50 text-left">
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">İşlem Bilgileri</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Rezervasyon</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Tutar</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Ödeme Yöntemi</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                            <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for payment in payments %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ payment.transaction_id }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ payment.order_id }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                {% if payment.reservation %}
                                <div class="text-sm font-medium text-gray-900">
                                    {{ payment.reservation.reservation_code }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ payment.reservation.name }} {{ payment.reservation.surname }}
                                </div>
                                {% else %}
                                <div class="text-sm text-gray-500">
                                    -
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {% if payment.currency == 'TRY' %}
                                        {{ payment.amount|int }} ₺
                                    {% elif payment.currency == 'USD' %}
                                        ${{ payment.amount|int }}
                                    {% elif payment.currency == 'EUR' %}
                                        €{{ payment.amount|int }}
                                    {% else %}
                                        {{ payment.amount|int }} {{ payment.currency }}
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full 
                                    {% if payment.status == 'SUCCESS' %}bg-green-100 text-green-800
                                    {% elif payment.status == 'PENDING' %}bg-yellow-100 text-yellow-800
                                    {% elif payment.status == 'FAILED' %}bg-red-100 text-red-800
                                    {% elif payment.status == 'REFUNDED' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %} inline-flex items-center justify-center">
                                    {% if payment.status == 'SUCCESS' %}
                                        <i class="fas fa-check-circle mr-1"></i> Başarılı
                                    {% elif payment.status == 'PENDING' %}
                                        <i class="fas fa-clock mr-1"></i> İşlemde
                                    {% elif payment.status == 'FAILED' %}
                                        <i class="fas fa-times-circle mr-1"></i> Başarısız
                                    {% elif payment.status == 'REFUNDED' %}
                                        <i class="fas fa-undo mr-1"></i> İade
                                    {% else %}
                                        <i class="fas fa-question-circle mr-1"></i> {{ payment.status }}
                                    {% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    {% if payment.payment_method %}
                                        {{ payment.payment_method.name }}
                                    {% else %}
                                        Bilinmeyen Ödeme Yöntemi
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {% if payment.installment and payment.installment > 1 %}
                                        {{ payment.installment }} Taksit
                                    {% else %}
                                        Tek Çekim
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% set turkey_time = payment.created_at.replace(tzinfo=None) + timedelta(hours=3) %}
                                <div class="text-sm text-gray-900">
                                    {{ turkey_time.strftime('%d.%m.%Y') }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ turkey_time.strftime('%H:%M') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    {% if payment.reservation %}
                                    <a href="{{ url_for('reservation.reservation_detail', id=payment.reservation.id) }}" 
                                       class="text-gold hover:text-dark-gold transition-colors">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if pagination.pages > 1 %}
            <div class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {{ pagination.total }} sonuçtan
                            <span class="font-medium">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span>
                            -
                            <span class="font-medium">{{ min(pagination.page * pagination.per_page, pagination.total) }}</span>
                            arası gösteriliyor
                        </p>
                    </div>
                    <div>
                        <nav class="flex items-center">
                            {% if pagination.has_prev %}
                            <a href="{{ url_for('reservation.payment_list', page=pagination.prev_num, status=status, code=code) }}" 
                               class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 mr-2">
                                Önceki
                            </a>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == pagination.page %}
                                    <a href="{{ url_for('reservation.payment_list', page=page_num, status=status, code=code) }}" 
                                       class="px-3 py-1 rounded-md bg-gold text-white">{{ page_num }}</a>
                                    {% else %}
                                    <a href="{{ url_for('reservation.payment_list', page=page_num, status=status, code=code) }}" 
                                       class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50">{{ page_num }}</a>
                                    {% endif %}
                                {% else %}
                                    <span class="px-3 py-1">…</span>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <a href="{{ url_for('reservation.payment_list', page=pagination.next_num, status=status, code=code) }}" 
                               class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 ml-2">
                                Sonraki
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="p-12 text-center">
                <div class="text-6xl text-gray-300 mb-4">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-700 mb-2">Henüz ödeme kaydı bulunmuyor</h3>
                <p class="text-gray-500 mb-6">Belirtilen kriterlere uygun ödeme işlemi bulunamadı.</p>
                {% if status or code %}
                <a href="{{ url_for('reservation.payment_list') }}" 
                   class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors">
                    Tüm Ödemeleri Göster
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 