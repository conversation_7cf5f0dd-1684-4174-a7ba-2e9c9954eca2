from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.setting import db
import os
import shutil
import psutil
from datetime import datetime
import glob

cache_bp = Blueprint('cache', __name__)

@cache_bp.route('/admin/cache')
@login_required
def cache_dashboard():
    """Cache yönetim dashboard"""
    if not current_user.is_admin:
        flash('Bu say<PERSON>ya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))
    
    try:
        # Cache bilgilerini topla
        cache_info = get_cache_info()
        return render_template('admin/cache.html', cache_info=cache_info)
    except Exception as e:
        flash(f'Cache bilgileri alınırken hata oluştu: {str(e)}', 'error')
        return render_template('admin/cache.html', cache_info={})

@cache_bp.route('/admin/cache/clear', methods=['POST'])
@login_required
def clear_cache():
    """Cache temizleme"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'})
    
    cache_type = request.json.get('type', 'all')
    
    try:
        if cache_type == 'all':
            result = clear_all_cache()
        elif cache_type == 'flask':
            result = clear_flask_cache()
        elif cache_type == 'session':
            result = clear_session_cache()
        elif cache_type == 'static':
            result = clear_static_cache()
        elif cache_type == 'logs':
            result = clear_logs()
        else:
            return jsonify({'success': False, 'message': 'Geçersiz cache türü'})
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': f'Hata: {str(e)}'})

@cache_bp.route('/admin/cache/info')
@login_required
def cache_info_api():
    """Cache bilgilerini JSON olarak döndür"""
    if not current_user.is_admin:
        return jsonify({'error': 'Yetkiniz yok'})
    
    try:
        cache_info = get_cache_info()
        return jsonify(cache_info)
    except Exception as e:
        return jsonify({'error': str(e)})

def get_cache_info():
    """Cache bilgilerini topla"""
    info = {
        'flask_cache': get_flask_cache_info(),
        'session_cache': get_session_cache_info(),
        'static_cache': get_static_cache_info(),
        'logs': get_logs_info(),
        'system': get_system_cache_info()
    }
    return info

def get_flask_cache_info():
    """Flask cache bilgileri"""
    try:
        from app import cache
        # Cache istatistikleri (basit cache için)
        return {
            'type': 'Simple Memory Cache',
            'status': 'Aktif',
            'size': 'Bilinmiyor',
            'items': 'Bilinmiyor'
        }
    except:
        return {
            'type': 'Bilinmiyor',
            'status': 'Pasif',
            'size': '0 MB',
            'items': 0
        }

def get_session_cache_info():
    """Session cache bilgileri"""
    try:
        session_dir = os.path.join(os.getcwd(), 'flask_session')
        if os.path.exists(session_dir):
            files = os.listdir(session_dir)
            total_size = sum(os.path.getsize(os.path.join(session_dir, f)) 
                           for f in files if os.path.isfile(os.path.join(session_dir, f)))
            return {
                'path': session_dir,
                'files': len(files),
                'size': f"{total_size / 1024 / 1024:.2f} MB",
                'status': 'Aktif'
            }
        else:
            return {
                'path': session_dir,
                'files': 0,
                'size': '0 MB',
                'status': 'Dizin yok'
            }
    except Exception as e:
        return {
            'path': 'Bilinmiyor',
            'files': 0,
            'size': '0 MB',
            'status': f'Hata: {str(e)}'
        }

def get_static_cache_info():
    """Static dosya cache bilgileri"""
    try:
        static_dir = os.path.join(os.getcwd(), 'static')
        if os.path.exists(static_dir):
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(static_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
            
            return {
                'path': static_dir,
                'files': file_count,
                'size': f"{total_size / 1024 / 1024:.2f} MB",
                'status': 'Aktif'
            }
        else:
            return {
                'path': static_dir,
                'files': 0,
                'size': '0 MB',
                'status': 'Dizin yok'
            }
    except Exception as e:
        return {
            'path': 'Bilinmiyor',
            'files': 0,
            'size': '0 MB',
            'status': f'Hata: {str(e)}'
        }

def get_logs_info():
    """Log dosyaları bilgileri"""
    try:
        log_files = glob.glob('*.log') + glob.glob('logs/*.log')
        total_size = sum(os.path.getsize(f) for f in log_files if os.path.exists(f))
        
        return {
            'files': len(log_files),
            'size': f"{total_size / 1024 / 1024:.2f} MB",
            'status': 'Aktif' if log_files else 'Log yok'
        }
    except Exception as e:
        return {
            'files': 0,
            'size': '0 MB',
            'status': f'Hata: {str(e)}'
        }

def get_system_cache_info():
    """Sistem cache bilgileri"""
    try:
        memory = psutil.virtual_memory()
        return {
            'memory_total': f"{memory.total / 1024 / 1024 / 1024:.2f} GB",
            'memory_used': f"{memory.used / 1024 / 1024 / 1024:.2f} GB",
            'memory_percent': f"{memory.percent:.1f}%",
            'memory_available': f"{memory.available / 1024 / 1024 / 1024:.2f} GB"
        }
    except Exception as e:
        return {
            'memory_total': 'Bilinmiyor',
            'memory_used': 'Bilinmiyor',
            'memory_percent': 'Bilinmiyor',
            'memory_available': 'Bilinmiyor'
        }

def clear_all_cache():
    """Tüm cache'leri temizle"""
    results = []
    
    # Flask cache temizle
    flask_result = clear_flask_cache()
    results.append(f"Flask Cache: {flask_result['message']}")
    
    # Session cache temizle
    session_result = clear_session_cache()
    results.append(f"Session Cache: {session_result['message']}")
    
    # Log temizle
    log_result = clear_logs()
    results.append(f"Logs: {log_result['message']}")
    
    return {
        'success': True,
        'message': 'Tüm cache temizlendi',
        'details': results
    }

def clear_flask_cache():
    """Flask cache temizle"""
    try:
        from app import cache
        cache.clear()
        return {'success': True, 'message': 'Flask cache temizlendi'}
    except Exception as e:
        return {'success': False, 'message': f'Flask cache temizlenemedi: {str(e)}'}

def clear_session_cache():
    """Session cache temizle"""
    try:
        session_dir = os.path.join(os.getcwd(), 'flask_session')
        if os.path.exists(session_dir):
            shutil.rmtree(session_dir)
            os.makedirs(session_dir)
            return {'success': True, 'message': 'Session cache temizlendi'}
        else:
            return {'success': True, 'message': 'Session cache dizini zaten yok'}
    except Exception as e:
        return {'success': False, 'message': f'Session cache temizlenemedi: {str(e)}'}

def clear_static_cache():
    """Static cache temizle (sadece geçici dosyalar)"""
    try:
        # Sadece geçici dosyaları temizle, asıl static dosyaları dokunma
        temp_patterns = [
            'static/temp/*',
            'static/cache/*',
            'static/*.tmp'
        ]
        
        deleted_count = 0
        for pattern in temp_patterns:
            files = glob.glob(pattern)
            for file in files:
                try:
                    os.remove(file)
                    deleted_count += 1
                except:
                    pass
        
        return {'success': True, 'message': f'{deleted_count} geçici dosya temizlendi'}
    except Exception as e:
        return {'success': False, 'message': f'Static cache temizlenemedi: {str(e)}'}

def clear_logs():
    """Log dosyalarını temizle"""
    try:
        log_files = glob.glob('*.log') + glob.glob('logs/*.log')
        deleted_count = 0
        
        for log_file in log_files:
            try:
                # Log dosyasını sil değil, içeriğini temizle
                with open(log_file, 'w') as f:
                    f.write('')
                deleted_count += 1
            except:
                pass
        
        return {'success': True, 'message': f'{deleted_count} log dosyası temizlendi'}
    except Exception as e:
        return {'success': False, 'message': f'Log dosyaları temizlenemedi: {str(e)}'}
