{% extends "admin/base.html" %}

{% block page_title %}<PERSON><PERSON><PERSON>{% endblock %}
{% block breadcrumb %}<PERSON>ucu İzleme{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <!-- <PERSON><PERSON>ğı -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-server me-2"></i>
            Sunucu İzleme
        </h1>
        <button onclick="refreshServerInfo()" class="btn btn-primary">
            <i class="fas fa-sync-alt me-1"></i>
            Yenile
        </button>
    </div>

    <!-- Sistem Özeti -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                CPU Kullanımı
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="cpu-usage">
                                {{ server_info.cpu.cpu_usage if server_info.cpu else 'Yükleniyor...' }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-microchip fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Bellek Kullanımı
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-usage">
                                {{ server_info.memory.percentage if server_info.memory else 'Yükleniyor...' }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-memory fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Disk Kullanımı
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="disk-usage">
                                {% if server_info.disk and server_info.disk|length > 0 %}
                                    {{ server_info.disk[0].percentage }}%
                                {% else %}
                                    Yükleniyor...
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Çalışma Süresi
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="uptime">
                                {{ server_info.uptime if server_info.uptime else 'Yükleniyor...' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sistem Bilgileri -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Sistem Bilgileri
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>İşletim Sistemi:</strong></td>
                            <td id="system-platform">{{ server_info.system.platform if server_info.system else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Hostname:</strong></td>
                            <td id="system-hostname">{{ server_info.system.hostname if server_info.system else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>IP Adresi:</strong></td>
                            <td id="system-ip">{{ server_info.system.ip_address if server_info.system else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>İşlemci:</strong></td>
                            <td id="system-processor">{{ server_info.system.processor if server_info.system else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Başlatma Zamanı:</strong></td>
                            <td id="boot-time">{{ server_info.boot_time if server_info.boot_time else 'Yükleniyor...' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- CPU Detayları -->
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-microchip me-2"></i>
                        CPU Detayları
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Fiziksel Çekirdek:</strong></td>
                            <td id="cpu-physical">{{ server_info.cpu.physical_cores if server_info.cpu else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Toplam Çekirdek:</strong></td>
                            <td id="cpu-total">{{ server_info.cpu.total_cores if server_info.cpu else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Maksimum Frekans:</strong></td>
                            <td id="cpu-max-freq">{{ server_info.cpu.max_frequency if server_info.cpu else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Mevcut Frekans:</strong></td>
                            <td id="cpu-current-freq">{{ server_info.cpu.current_frequency if server_info.cpu else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Kullanım:</strong></td>
                            <td id="cpu-usage-detail">{{ server_info.cpu.cpu_usage if server_info.cpu else 'Yükleniyor...' }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bellek ve Disk Bilgileri -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-memory me-2"></i>
                        Bellek Bilgileri
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Toplam Bellek:</strong></td>
                            <td id="memory-total">{{ server_info.memory.total if server_info.memory else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Kullanılan:</strong></td>
                            <td id="memory-used">{{ server_info.memory.used if server_info.memory else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Kullanılabilir:</strong></td>
                            <td id="memory-available">{{ server_info.memory.available if server_info.memory else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Swap Toplam:</strong></td>
                            <td id="swap-total">{{ server_info.memory.swap_total if server_info.memory else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Swap Kullanılan:</strong></td>
                            <td id="swap-used">{{ server_info.memory.swap_used if server_info.memory else 'Yükleniyor...' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Disk Bilgileri -->
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-hdd me-2"></i>
                        Disk Bilgileri
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="disk-table">
                            <thead>
                                <tr>
                                    <th>Cihaz</th>
                                    <th>Toplam</th>
                                    <th>Kullanılan</th>
                                    <th>Boş</th>
                                    <th>%</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if server_info.disk %}
                                    {% for disk in server_info.disk %}
                                    <tr>
                                        <td>{{ disk.device }}</td>
                                        <td>{{ disk.total }}</td>
                                        <td>{{ disk.used }}</td>
                                        <td>{{ disk.free }}</td>
                                        <td>{{ disk.percentage }}%</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">Yükleniyor...</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ağ ve İşlem Bilgileri -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-network-wired me-2"></i>
                        Ağ Bilgileri
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Gönderilen Veri:</strong></td>
                            <td id="bytes-sent">{{ server_info.network.bytes_sent if server_info.network else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Alınan Veri:</strong></td>
                            <td id="bytes-received">{{ server_info.network.bytes_received if server_info.network else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Gönderilen Paket:</strong></td>
                            <td id="packets-sent">{{ server_info.network.packets_sent if server_info.network else 'Yükleniyor...' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Alınan Paket:</strong></td>
                            <td id="packets-received">{{ server_info.network.packets_received if server_info.network else 'Yükleniyor...' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- En Çok CPU Kullanan İşlemler -->
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tasks me-2"></i>
                        En Çok CPU Kullanan İşlemler
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="processes-table">
                            <thead>
                                <tr>
                                    <th>PID</th>
                                    <th>İşlem Adı</th>
                                    <th>CPU %</th>
                                    <th>Bellek %</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if server_info.processes %}
                                    {% for process in server_info.processes %}
                                    <tr>
                                        <td>{{ process.pid }}</td>
                                        <td>{{ process.name }}</td>
                                        <td>{{ "%.1f"|format(process.cpu_percent or 0) }}%</td>
                                        <td>{{ "%.1f"|format(process.memory_percent or 0) }}%</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">Yükleniyor...</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshServerInfo() {
    fetch('/admin/server/info')
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert('error', 'Sunucu bilgileri alınamadı: ' + data.error);
            return;
        }
        
        // Özet bilgileri güncelle
        if (data.cpu) {
            document.getElementById('cpu-usage').textContent = data.cpu.cpu_usage + '%';
            document.getElementById('cpu-usage-detail').textContent = data.cpu.cpu_usage + '%';
            document.getElementById('cpu-physical').textContent = data.cpu.physical_cores;
            document.getElementById('cpu-total').textContent = data.cpu.total_cores;
            document.getElementById('cpu-max-freq').textContent = data.cpu.max_frequency;
            document.getElementById('cpu-current-freq').textContent = data.cpu.current_frequency;
        }
        
        if (data.memory) {
            document.getElementById('memory-usage').textContent = data.memory.percentage + '%';
            document.getElementById('memory-total').textContent = data.memory.total;
            document.getElementById('memory-used').textContent = data.memory.used;
            document.getElementById('memory-available').textContent = data.memory.available;
            document.getElementById('swap-total').textContent = data.memory.swap_total;
            document.getElementById('swap-used').textContent = data.memory.swap_used;
        }
        
        if (data.disk && data.disk.length > 0) {
            document.getElementById('disk-usage').textContent = data.disk[0].percentage + '%';
        }
        
        if (data.uptime) {
            document.getElementById('uptime').textContent = data.uptime;
        }
        
        if (data.system) {
            document.getElementById('system-platform').textContent = data.system.platform;
            document.getElementById('system-hostname').textContent = data.system.hostname;
            document.getElementById('system-ip').textContent = data.system.ip_address;
            document.getElementById('system-processor').textContent = data.system.processor;
        }
        
        if (data.boot_time) {
            document.getElementById('boot-time').textContent = data.boot_time;
        }
        
        if (data.network) {
            document.getElementById('bytes-sent').textContent = data.network.bytes_sent;
            document.getElementById('bytes-received').textContent = data.network.bytes_received;
            document.getElementById('packets-sent').textContent = data.network.packets_sent;
            document.getElementById('packets-received').textContent = data.network.packets_received;
        }
        
        showAlert('success', 'Sunucu bilgileri güncellendi');
    })
    .catch(error => {
        showAlert('error', 'Bir hata oluştu: ' + error);
    });
}

function showAlert(type, message) {
    // Admin base template'ine uygun flash message oluştur
    const iconClass = type === 'success' ? 'fas fa-check text-green-600' : 'fas fa-exclamation text-red-600';
    const bgClass = type === 'success' ? 'bg-green-100' : 'bg-red-100';

    const alertHtml = `
        <div class="flash-message bg-white border border-gray-200 shadow-lg px-4 py-3 rounded-lg flex items-center justify-between min-w-80 max-w-md">
            <div class="flex items-center">
                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center mr-3">
                    <i class="${iconClass} text-sm"></i>
                </div>
                <span class="text-gray-800 text-sm font-medium">${message}</span>
            </div>
            <button class="close-flash ml-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded hover:bg-gray-100">
                <i class="fas fa-times text-xs"></i>
            </button>
        </div>
    `;

    // Fixed position container oluştur veya mevcut olanı kullan
    let container = document.querySelector('.flash-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'flash-container fixed top-4 right-4 z-50 space-y-3';
        document.body.appendChild(container);
    }

    container.insertAdjacentHTML('beforeend', alertHtml);

    const flashMessage = container.lastElementChild;

    // Close button event
    flashMessage.querySelector('.close-flash').addEventListener('click', () => {
        flashMessage.style.opacity = '0';
        flashMessage.style.transform = 'translateX(100%)';
        setTimeout(() => flashMessage.remove(), 300);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (flashMessage.parentNode) {
            flashMessage.style.opacity = '0';
            flashMessage.style.transform = 'translateX(100%)';
            setTimeout(() => flashMessage.remove(), 300);
        }
    }, 5000);
}

// Sayfa yüklendiğinde otomatik yenile
document.addEventListener('DOMContentLoaded', function() {
    // 30 saniyede bir otomatik yenile
    setInterval(refreshServerInfo, 30000);
});
</script>
{% endblock %}
