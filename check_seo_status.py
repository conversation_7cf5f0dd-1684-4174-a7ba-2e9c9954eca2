import os
from sqlalchemy import create_engine, text

def check_seo_settings_sqlite():
    """
    Connects directly to the SQLite database to check SEO settings.
    """
    print("SQLite veritabanına bağlanarak SEO 'robots' ayarları kontrol ediliyor...")

    try:
        # Proje kök dizinini bul
        basedir = os.path.abspath(os.path.dirname(__file__))
        # Veritabanı dosyasının tam yolunu oluştur
        db_path = os.path.join(basedir, 'instance', 'hotel.db')

        if not os.path.exists(db_path):
            print(f"HATA: Veritabanı dosyası bulunamadı: {db_path}")
            print("Lütfen projenin doğru dizinde olduğundan ve 'instance/hotel.db' dosyasının mevcut olduğundan emin olun.")
            return

        database_uri = f"sqlite:///{db_path}"
        engine = create_engine(database_uri)

        with engine.connect() as connection:
            print("Veritabanı bağlantısı başarılı.")
            
            # 'seos' tablosunun varlığını kontrol et
            table_check_query = text("SELECT name FROM sqlite_master WHERE type='table' AND name='seos'")
            table_exists = connection.execute(table_check_query).fetchone()
            if not table_exists:
                print("HATA: 'seos' tablosu veritabanında bulunamadı.")
                return

            # 'seos' tablosundan ilgili sütunları çek
            query = text("SELECT page_name, robots FROM seos")
            result = connection.execute(query)
            seo_settings = result.fetchall()

            if not seo_settings:
                print("Veritabanında hiç SEO ayarı bulunamadı.")
                return

            print("-" * 60)
            print(f"{'Sayfa Endpoint':<35} | {'Robots Değeri'}")
            print("-" * 60)
            
            noindex_found = False
            for page_name, robots_value in seo_settings:
                robots_display = robots_value or "(Ayarlanmamış)"
                print(f"{page_name:<35} | {robots_display}")
                if robots_value and 'noindex' in robots_value:
                    noindex_found = True
            
            print("-" * 60)
            
            if noindex_found:
                print("\nUYARI: Bazı sayfalarda 'noindex' değeri bulundu. Bu sayfalar Google tarafından indekslenmeyecektir.")
                print("Lütfen admin panelinden bu ayarları 'index, follow' olarak güncelleyin.")
            else:
                print("\nBİLGİ: 'noindex' içeren bir ayar bulunamadı. Tüm sayfalar indekslenmeye uygun görünüyor.")

    except Exception as e:
        print(f"Veritabanı işlemi sırasında bir hata oluştu: {e}")

if __name__ == "__main__":
    check_seo_settings_sqlite()
