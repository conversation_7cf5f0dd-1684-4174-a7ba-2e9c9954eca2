<!DOCTYPE html>
<html lang="{{ g.language }}">
<head>
    {% if settings.google_analytics_active and settings.google_analytics_id %}
    <!-- Google Analytics (GA4) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ settings.google_analytics_id }}"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '{{ settings.google_analytics_id }}');
    </script>
    {% endif %}
    
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Google Search Console Verification -->
    {% if settings.google_search_console_verification %}
    <meta name="google-site-verification" content="{{ settings.google_search_console_verification }}">
    {% endif %}

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Hotel",
      "name": "{{ settings.site_title or 'Zeppelin Hotel' }}",
      "description": "{{ settings.site_description or 'Kapadokya bölgesinde lüks ve konforun buluştugu nokta. Eşsiz deneyimler için sizi bekliyoruz.' }}",
      "url": "{{ request.url_root }}",
      "logo": "{{ url_for('static', filename='images/logo.png', _external=True) }}",
      "image": "{{ url_for('static', filename='images/seo-image.jpg', _external=True) }}",
      "telephone": "{{ settings.contact_phone or '+90 ************' }}",
      "email": "{{ settings.contact_email or '<EMAIL>' }}",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "{{ settings.address_street or '' }}",
        "addressLocality": "{{ settings.address_city or 'Nevşehir' }}",
        "addressRegion": "{{ settings.address_region or 'Kapadokya' }}",
        "postalCode": "{{ settings.address_postal or '' }}",
        "addressCountry": "TR"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "{{ settings.geo_lat or '38.626615' }}",
        "longitude": "{{ settings.geo_lng or '34.798558' }}"
      },
      "priceRange": "₺₺₺",
      "starRating": {
        "@type": "Rating",
        "ratingValue": "{{ settings.hotel_rating or '4.5' }}"
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday"
        ],
        "opens": "00:00",
        "closes": "23:59"
      }
    }
    </script>
    
    <!-- Dinamik SEO Meta Etiketleri -->
    {% if seo %}
        <title>{{ seo.title }} | {{ settings.site_title or 'Zeppelin Hotel' }}</title>
        <meta name="description" content="{{ seo.description }}">
        <meta name="keywords" content="{{ seo.keywords }}">
        
        <!-- Gelişmiş Meta Etiketleri -->
        <meta name="author" content="{{ seo.meta_author }}">
        <meta name="publisher" content="{{ seo.meta_publisher }}">
        <meta name="copyright" content="{{ seo.meta_copyright }}">
        <meta name="language" content="{{ seo.meta_language }}">
        <meta name="revisit-after" content="7 days">
        <meta name="distribution" content="global">
        <meta name="rating" content="general">
        
        <!-- Open Graph Meta Etiketleri -->
        <meta property="og:title" content="{{ seo.og_title or seo.title }}">
        <meta property="og:description" content="{{ seo.og_description or seo.description }}">
        <meta property="og:type" content="{{ seo.og_type }}">
        <meta property="og:url" content="{{ request.url }}">
        <meta property="og:site_name" content="{{ seo.og_site_name }}">
        <meta property="og:locale" content="{{ seo.og_locale }}">
        {% if seo.og_image %}
        <meta property="og:image" content="{{ url_for('static', filename='uploads/seo/' + seo.og_image, _external=True) }}">
        <meta property="og:image:type" content="image/jpeg">
        <meta property="og:image:width" content="1200">
        <meta property="og:image:height" content="630">
        {% endif %}
        {% if seo.og_updated_time %}
        <meta property="og:updated_time" content="{{ seo.og_updated_time.isoformat() }}">
        {% endif %}
        
        <!-- Twitter Card Meta Etiketleri -->
        <meta name="twitter:card" content="{{ seo.twitter_card }}">
        <meta name="twitter:site" content="{{ seo.twitter_site }}">
        <meta name="twitter:creator" content="{{ seo.twitter_creator }}">
        <meta name="twitter:title" content="{{ seo.twitter_title or seo.title }}">
        <meta name="twitter:description" content="{{ seo.twitter_description or seo.description }}">
        {% if seo.twitter_image %}
        <meta name="twitter:image" content="{{ url_for('static', filename='uploads/seo/' + seo.twitter_image, _external=True) }}">
        {% endif %}
        
        <!-- Canonical URL -->
        {% if seo and seo.canonical_url %}
        <link rel="canonical" href="{{ seo.canonical_url }}">
        {% else %}
        <link rel="canonical" href="{{ request.url }}">
        {% endif %}
        
        <!-- Alternatif Dil Bağlantıları -->
        {% if seo.alternate_urls %}
        {% for lang, url in seo.alternate_urls.items() %}
        <link rel="alternate" hreflang="{{ lang }}" href="{{ url }}">
        {% endfor %}
        {% endif %}
        
        <!-- Robots Direktifi -->
        <meta name="robots" content="{{ seo.robots or 'index, follow' }}">
        
        <!-- Schema.org İşaretlemesi -->
        {% if seo.schema_data %}
        <script type="application/ld+json">
            {{ seo.schema_data|tojson|safe }}
        </script>
        {% endif %}
    {% else %}
        <title>{% block title %}{% endblock %} | {{ settings.site_title or 'Zeppelin Hotel' }}</title>
        <meta name="application-name" content="{{ settings.site_title or 'Zeppelin Hotel' }}">
        <meta property="og:site_name" content="{{ settings.site_title or 'Zeppelin Hotel' }}">
        <meta name="robots" content="index, follow">
        <link rel="canonical" href="{{ request.url }}">
    {% endif %}
    <meta name="description" content="{{ settings.site_description or '' }}">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;300;400;500;600;700&family=Great+Vibes&family=Libre+Caslon+Display&display=swap" rel="stylesheet">

    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Çarkıfelek için gerekli kütüphaneler -->
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@700&family=Lato&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gold': '#C6A87D',
                    },
                    fontFamily: {
                        'great-vibes': ['"Great Vibes"', 'cursive'],
                        'poppins': ['Poppins', 'sans-serif'],
                        'libre': ['"Libre Caslon Display"', 'serif'],
                    },
                }
            }
        }
    </script>

    <!-- Translations -->
    <script src="{{ url_for('static', filename='js/translations.js') }}"></script>

    <!-- Google Analytics - Moved to head section -->

    <!-- Google Tag Manager -->
    {% if settings.google_tag_manager_active and settings.google_tag_manager_id %}
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ settings.google_tag_manager_id }}');</script>
    {% endif %}

    {% block head %}{% endblock %}
    
    <style>
        /* Font tanımlamaları */
        .font-perpetua {
            font-family: 'Poppins', sans-serif;
            font-weight: 100;
            font-size: 1.125rem; /* 18px */
        }

        /* El yazısı font sınıfı */
        .font-great-vibes {
            font-family: 'Great Vibes', cursive !important;
        }

        .handwriting {
            font-family: 'Great Vibes', cursive !important;
        }

        /* Varsayılan font boyutunu artır */
        body {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 300;
            font-size: 1.125rem !important; /* 18px */
            line-height: 1.75rem !important; /* 28px */
            background: #E3DCD3 !important;
            background-image: url('/static/assets/images/noise.png');
            background-repeat: repeat;
            background-size: auto;
            background-blend-mode: multiply;
        }

        /* Menü fontu için özel boyutlar */
        .menu-font {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 300;
            font-size: 1.25rem !important; /* 20px */
        }

        /* Başlık boyutlarını artır */
        h1 { font-size: 2.5rem !important; }    /* 40px */
        h2 { font-size: 2rem !important; }      /* 32px */
        h3 { font-size: 1.75rem !important; }   /* 28px */
        h4 { font-size: 1.5rem !important; }    /* 24px */
        h5 { font-size: 1.25rem !important; }   /* 20px */
        h6 { font-size: 1.125rem !important; }  /* 18px */

        /* Paragraf ve diğer metin elementleri için */
        p, span, div, li, a {
            font-size: 1.125rem; /* 18px */
            line-height: 1.75rem; /* 28px */
        }

        /* Logo stilleri */
        .text-logo {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            text-align: center;
            padding: 1rem;
        }

        /* Header transition stilleri */
        #mainHeader {
            transition: background-color 0.3s ease-in-out;
        }
        
        #mainHeader::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent);
            pointer-events: none;
            z-index: -1;
        }

        /* Modal stilleri */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.75);
            transition: opacity 0.3s ease;
        }

        .modal-content {
            transform: scale(0.95);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }

        /* Galeri stilleri */
        .gallery-item img {
            transition: transform 0.3s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
        }

        /* Animasyonlar */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }
        
        .animate-slide-in-right {
            animation: slideInRight 0.3s ease-out forwards;
        }
        
        .animate-fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }

        /* Temel responsive ayarlar */
        @media (max-width: 640px) {
            body {
                font-size: 1rem !important;
                line-height: 1.5rem !important;
                background: #E3DCD3 !important;
                background-image: url('/static/assets/images/noise.png');
                background-repeat: repeat;
                background-size: auto;
                background-blend-mode: multiply;
            }

            h1 { font-size: 2rem !important; }    
            h2 { font-size: 1.75rem !important; } 
            h3 { font-size: 1.5rem !important; }  
            h4 { font-size: 1.25rem !important; } 
            h5 { font-size: 1.125rem !important; }
            h6 { font-size: 1rem !important; }

            p, span, div, li, a {
                font-size: 1rem;
                line-height: 1.5rem;
            }
        }

        /* Tablet için ayarlar */
        @media (min-width: 641px) and (max-width: 1024px) {
            body {
                font-size: 1.05rem !important;
                background: #E3DCD3 !important;
                background-image: url('/static/assets/images/noise.png');
                background-repeat: repeat;
                background-size: auto;
                background-blend-mode: multiply;
            }
        }

        /* Container genişlikleri */
        .container {
            width: 100%;
            padding-left: 1rem;
            padding-right: 1rem;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 640px) {
            .container {
                max-width: 640px;
            }
        }

        @media (min-width: 768px) {
            .container {
                max-width: 768px;
            }
        }

        @media (min-width: 1024px) {
            .container {
                max-width: 1024px;
            }
        }

        @media (min-width: 1280px) {
            .container {
                max-width: 1280px;
            }
        }

        /* Çarkıfelek Stilleri */
        :root {
            --gold-dark: #B8860B;
            --gold-medium: #D4AF37;
            --gold-light: #FFD700;
            --wheel-red-dark: #A61C1C;
            --wheel-red-light: #E02424;
            --wheel-white: #FDFDFD;
            --text-light: #EEEEEE;
        }

        #sparkle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .sparkle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            opacity: 0;
            animation: sparkle-anim 7s linear infinite;
        }

        @keyframes sparkle-anim {
            0% { transform: translateY(100vh) scale(0.5); opacity: 1; }
            99.9% { transform: translateY(-10vh) scale(1.5); opacity: 1; }
            100% { transform: translateY(-10vh) scale(1.5); opacity: 0; }
        }

        .wheel-main-container {
            position: relative;
            z-index: 10;
            text-align: center;
        }

        .wheel-main-container h1 {
            font-family: 'Cinzel', serif;
            font-size: 3.5rem;
            color: var(--gold-light);
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 5px;
            margin-bottom: 20px;
            text-shadow: 0 4px 2px rgba(0,0,0,0.4), 0 6px 15px rgba(0,0,0,0.4);
        }

        .wheel-container-inner {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .marker {
            width: 4px;
            height: 30px;
            background: linear-gradient(to right, var(--gold-dark) 0%, var(--gold-light) 50%, var(--gold-dark) 100%);
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 20;
            border-radius: 2px;
            filter: drop-shadow(0 2px 3px rgba(0,0,0,0.5));
        }

        .marker::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 14px solid transparent;
            border-right: 14px solid transparent;
            border-top: 18px solid var(--gold-medium);
            filter: drop-shadow(0 2px 2px rgba(0,0,0,0.4));
        }

        .wheel-container {
            position: relative;
            width: 520px;
            height: 520px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(145deg, var(--gold-dark), var(--gold-light), var(--gold-dark));
            border-radius: 50%;
            box-shadow: 0 10px 25px rgba(0,0,0,0.5), inset 0 0 15px rgba(0,0,0,0.6);
        }

        .light {
            position: absolute;
            width: 18px;
            height: 18px;
            background: #fff;
            border-radius: 50%;
            border: 1px solid var(--gold-dark);
            box-shadow: 0 0 10px #fff, 0 0 20px var(--gold-light);
            animation: light-pulse 1.5s infinite ease-in-out;
        }

        @keyframes light-pulse {
            0% { background: var(--gold-light); box-shadow: 0 0 10px var(--gold-light), 0 0 20px var(--gold-light); }
            50% { background: #fff; box-shadow: 0 0 20px #fff, 0 0 30px #fff, 0 0 40px var(--gold-light); }
            100% { background: var(--gold-light); box-shadow: 0 0 10px var(--gold-light), 0 0 20px var(--gold-light); }
        }

        #wheel {
            width: 500px;
            height: 500px;
            border-radius: 50%;
            transition: transform 7s cubic-bezier(0.25, 1, 0.5, 1);
        }

        #spin-btn {
            position: relative;
            margin-top: 40px;
            padding: 15px 50px;
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: #4B2808;
            background: linear-gradient(145deg, var(--gold-dark), var(--gold-light));
            border: 2px solid var(--gold-dark);
            border-radius: 50px;
            cursor: pointer;
            outline: none;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4), inset 0 -4px 5px rgba(0,0,0,0.2);
            text-transform: uppercase;
        }

        #spin-btn:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.4), inset 0 -2px 2px rgba(0,0,0,0.2);
        }

        #spin-btn:active {
            transform: scale(1) translateY(0);
            box-shadow: 0 5px 10px rgba(0,0,0,0.3), inset 0 2px 5px rgba(0,0,0,0.3);
        }

        #spin-btn:disabled {
            background: #777;
            color: #aaa;
            cursor: not-allowed;
            box-shadow: none;
            transform: scale(1);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 30px rgba(245, 158, 11, 0.6),
                           0 0 60px rgba(245, 158, 11, 0.3);
            }
            50% {
                box-shadow: 0 0 50px rgba(245, 158, 11, 0.9),
                           0 0 100px rgba(245, 158, 11, 0.5);
            }
        }

        .bounce-in {
            animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes bounce-in {
            0% { transform: scale(0.3) rotate(180deg); opacity: 0; }
            50% { transform: scale(1.05) rotate(0deg); }
            70% { transform: scale(0.9) rotate(0deg); }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }

        /* Promosyon butonu stilleri */
        .promotion-trigger-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            z-index: 40;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
            border: 3px solid #FFD700;
            border-radius: 50%;
            width: 70px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
            animation: promotion-pulse 2s infinite;
        }

        .promotion-trigger-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
        }

        @keyframes promotion-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Responsive ayarlar */
        @media (max-width: 768px) {
            .wheel-container {
                width: 350px;
                height: 350px;
            }

            #wheel {
                width: 330px;
                height: 330px;
            }

            .wheel-main-container h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body class="bg-[#E3DCD3]" data-page="{{ request.endpoint.split('.')[-1] if request.endpoint else '' }}">
    <!-- Google Tag Manager (noscript) -->
    {% if settings.google_tag_manager_active and settings.google_tag_manager_id %}
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ settings.google_tag_manager_id }}"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    {% endif %}

    {% include 'partials/header.html' %}
    {% block content %}{% endblock %}
    {% include 'partials/footer.html' %}

    <!-- Promosyon Çarkıfelek Sistemi -->
    <!-- Promosyon Tetikleme Butonu -->
    <button id="promotion-trigger" class="promotion-trigger-btn" title="Promosyon Kazan!" style="display: flex;">
        <i class="fas fa-gift text-white text-2xl"></i>
    </button>

    <!-- Sağ Alt Köşe Promosyon Timer -->
    <div id="promotion-timer" class="hidden fixed bottom-6 right-6 z-40">
        <div class="glass-card rounded-xl shadow-2xl p-4 border-2 border-yellow-400 cursor-pointer hover:scale-105 transition-all pulse-glow" onclick="openPromotionDetails()">
            <div class="text-center">
                <div class="text-2xl mb-2">🎁</div>
                <div class="text-sm text-yellow-300 font-bold">Promosyon Aktif</div>
                <div id="timer-countdown" class="text-lg font-bold text-yellow-100"></div>
                <div class="text-xs text-yellow-200 mt-1">Detaylar için tıkla</div>
            </div>
        </div>
    </div>

    <!-- Çarkıfelek Modal -->
    <div id="wheel-modal" class="hidden fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center">
        <div id="sparkle-container"></div>
        <div class="relative">
            <!-- Modal Kapatma Butonu -->
            <button id="close-wheel-btn" class="absolute -top-4 -right-4 bg-red-500 hover:bg-red-600 text-white w-10 h-10 rounded-full z-60 transition-all shadow-lg">
                <i class="fas fa-times"></i>
            </button>

            <!-- Çark Container -->
            <div class="wheel-main-container">
                <h1 class="text-4xl mb-6">🎰 ŞANS ÇARKI</h1>
                <div class="wheel-container-inner">
                    <div class="marker"></div>
                    <div class="wheel-container">
                        <canvas id="wheel" width="500" height="500"></canvas>
                    </div>
                    <button id="spin-btn">ÇARKI ÇEVİR</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Promosyon Sonuç Popup -->
    <div id="result-popup" class="hidden fixed inset-0 flex items-center justify-center z-50">
        <div class="glass-card rounded-xl shadow-2xl p-8 border-2 border-yellow-400 text-center max-w-md mx-4">
            <div class="text-6xl mb-4">🎉</div>
            <h2 class="text-2xl font-bold text-yellow-300 mb-4">Tebrikler!</h2>
            <p class="text-lg text-yellow-200 mb-4">Kazandığınız promosyon:</p>
            <div id="result-content" class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-lg mb-4">
                <div id="result-value" class="text-2xl font-bold"></div>
            </div>
            <button id="close-popup" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg font-bold hover:from-green-600 hover:to-green-700 transition-all">
                Tamam
            </button>
        </div>
    </div>

    <!-- Promosyon Detay Modal -->
    <div id="promotion-modal" class="hidden fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center">
        <div class="glass-card rounded-xl shadow-2xl p-8 border-2 border-yellow-400 max-w-md mx-4 relative">
            <!-- Modal Kapatma Butonu -->
            <button id="close-promotion-btn" class="absolute -top-4 -right-4 bg-red-500 hover:bg-red-600 text-white w-10 h-10 rounded-full transition-all shadow-lg">
                <i class="fas fa-times"></i>
            </button>

            <div class="text-center">
                <div class="text-6xl mb-4">🎉</div>
                <h2 class="text-2xl font-bold text-yellow-300 mb-4">Aktif Promosyonunuz!</h2>
                <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-lg mb-4">
                    <div id="modal-promotion-code" class="text-2xl font-bold"></div>
                </div>
                <p id="modal-promotion-description" class="text-lg text-yellow-200 mb-4"></p>
                <div class="text-center">
                    <div class="text-sm text-yellow-300 mb-2">Kalan Süre</div>
                    <div id="modal-countdown" class="text-3xl font-bold text-yellow-100"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40"></div>

    <!-- Ses dosyaları -->
    <audio id="tick-sound" preload="auto">
        <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
    </audio>
    <audio id="win-sound" preload="auto">
        <source src="https://www.soundjay.com/misc/sounds/magic-chime-02.wav" type="audio/wav">
    </audio>

    {% block scripts %}{% endblock %}

    <!-- Çarkıfelek JavaScript -->
    <script>
        // Çarkıfelek sistemi - sadece gerekli elementler varsa çalışır
        document.addEventListener('DOMContentLoaded', function() {
            // Element kontrolü
            const canvas = document.getElementById('wheel');
            const promotionTrigger = document.getElementById('promotion-trigger');

            if (!canvas || !promotionTrigger) return; // Elementler yoksa çıkış yap

            // Promosyon kontrolü yap
            checkIfAlreadyWon();

            // Canvas ve DOM elementleri
            const ctx = canvas.getContext('2d');
            const spinBtn = document.getElementById('spin-btn');
            const resultPopup = document.getElementById('result-popup');
            const resultValue = document.getElementById('result-value');
            const closePopupBtn = document.getElementById('close-popup');
            const overlay = document.getElementById('overlay');
            const wheelContainer = document.querySelector('.wheel-container');
            const sparkleContainer = document.getElementById('sparkle-container');
            const tickSound = document.getElementById('tick-sound');
            const winSound = document.getElementById('win-sound');

            // Modal elementleri
            const wheelModal = document.getElementById('wheel-modal');
            const closeWheelBtn = document.getElementById('close-wheel-btn');
            const promotionTimer = document.getElementById('promotion-timer');
            const promotionModal = document.getElementById('promotion-modal');
            const closePromotionBtn = document.getElementById('close-promotion-btn');

            // Promosyon değişkenleri
            let countdownInterval;
            let currentPromotion = null;
            let isSpinning = false;
            let hasWonToday = false;
            let lastSegment = null;

            // Çark segmentleri
            const segments = [
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
                { color: ['#E02424', '#A61C1C'], label: 'PROMOSYON', textColor: '#fff' },
                { color: ['#FFFFFF', '#E0E0E0'], label: 'DISCOUNT', textColor: '#333' },
            ];

            const numSegments = segments.length;
            const arcSize = (2 * Math.PI) / numSegments;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = canvas.width / 2 - 10;

            // Çark çizim fonksiyonu
            const drawWheel = (winningSegmentIndex = -1) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                segments.forEach((segment, i) => {
                    const startAngle = i * arcSize;
                    const endAngle = (i + 1) * arcSize;
                    const midAngle = startAngle + arcSize / 2;

                    // Segment rengi
                    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
                    gradient.addColorStop(0, segment.color[0]);
                    gradient.addColorStop(1, segment.color[1]);
                    ctx.fillStyle = gradient;

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.lineTo(centerX, centerY);
                    ctx.fill();

                    // İç altın çerçeve
                    ctx.save();
                    ctx.strokeStyle = '#B8860B';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius - 5, startAngle, endAngle);
                    ctx.stroke();
                    ctx.restore();

                    // Kazanan segment glow efekti
                    if (i === winningSegmentIndex) {
                        ctx.save();
                        ctx.fillStyle = 'rgba(255, 215, 0, 0.2)';
                        ctx.shadowColor = '#FFD700';
                        ctx.shadowBlur = 30;
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                        ctx.lineTo(centerX, centerY);
                        ctx.fill();
                        ctx.restore();
                    }

                    // Ayırıcı çizgiler
                    ctx.save();
                    ctx.strokeStyle = '#D4AF37';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(centerX + (radius) * Math.cos(endAngle), centerY + (radius) * Math.sin(endAngle));
                    ctx.stroke();
                    ctx.restore();

                    // Segment yazıları
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.rotate(midAngle);

                    ctx.textAlign = 'center';
                    ctx.fillStyle = segment.textColor || '#fff';
                    ctx.font = 'bold 14px Lato';
                    ctx.textShadow = '1px 1px 2px rgba(0,0,0,0.3)';
                    ctx.fillText(segment.label, radius * 0.75, 0);
                    ctx.restore();
                });

                // Merkez daire
                const centerRadius = radius * 0.35;
                ctx.beginPath();
                ctx.arc(centerX, centerY, centerRadius, 0, 2 * Math.PI, false);

                const grad = ctx.createRadialGradient(centerX, centerY, centerRadius * 0.9, centerX, centerY, centerRadius);
                grad.addColorStop(0, '#A61C1C');
                grad.addColorStop(1, '#6d1a1b');

                ctx.fillStyle = grad;
                ctx.fill();
                ctx.strokeStyle = "#B8860B";
                ctx.lineWidth = 4;
                ctx.stroke();

                // Merkez yazısı
                ctx.fillStyle = '#FFD700';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                ctx.font = 'bold 22px Cinzel';
                ctx.fillText('Zeppelin', centerX, centerY - 12);

                ctx.font = '16px Cinzel';
                ctx.fillText('Cappadocia', centerX, centerY + 12);
            };

            // Segment belirleme fonksiyonu
            const getSegment = (rotationDegrees) => {
                const segmentAngle = 360 / numSegments;
                const normalizedRotation = ( (rotationDegrees % 360) + 360) % 360;
                const winningAngle = ( (270 - normalizedRotation) % 360 + 360) % 360;
                const index = Math.floor(winningAngle / segmentAngle);
                return { segment: segments[index], index: index };
            };

            // Popup gösterme fonksiyonu
            const showPopup = (segment) => {
                if (winSound) {
                    winSound.currentTime = 0;
                    winSound.play();
                }

                // Confetti efekti
                if (typeof confetti !== 'undefined') {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 }
                    });
                }

                resultValue.textContent = segment.label;

                overlay.classList.remove('hidden');
                resultPopup.classList.remove('hidden');

                // API'den promosyon bilgilerini al
                checkPromotionAfterWin();
            };

            // Popup gizleme fonksiyonu
            const hidePopup = () => {
                overlay.classList.add('hidden');
                resultPopup.classList.add('hidden');
                spinBtn.disabled = false;
                drawWheel(); // Çarkı highlight olmadan yeniden çiz
            };

            // Modal fonksiyonları
            function openWheelModal() {
                wheelModal.classList.remove('hidden');
                promotionTrigger.style.display = 'none';
            }

            function closeWheelModal() {
                wheelModal.classList.add('hidden');
                if (!hasWonToday) {
                    promotionTrigger.style.display = 'flex';
                }
            }

            function openPromotionDetails() {
                if (currentPromotion) {
                    document.getElementById('modal-promotion-code').textContent = currentPromotion.code;
                    document.getElementById('modal-promotion-description').textContent = currentPromotion.description || 'Bu kodu kullanarak özel indirimden yararlanabilirsiniz!';
                    promotionModal.classList.remove('hidden');
                }
            }

            function closePromotionDetails() {
                promotionModal.classList.add('hidden');
            }

            // Global fonksiyon
            window.openPromotionDetails = openPromotionDetails;

            // Event listener'lar
            promotionTrigger.addEventListener('click', openWheelModal);
            closeWheelBtn.addEventListener('click', closeWheelModal);
            closePromotionBtn.addEventListener('click', closePromotionDetails);
            closePopupBtn.addEventListener('click', hidePopup);
            overlay.addEventListener('click', hidePopup);

            // ESC tuşu ile modal kapatma
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!wheelModal.classList.contains('hidden')) {
                        closeWheelModal();
                    }
                    if (!promotionModal.classList.contains('hidden')) {
                        closePromotionDetails();
                    }
                    if (!resultPopup.classList.contains('hidden')) {
                        hidePopup();
                    }
                }
            });

            // Çark döndürme event listener
            spinBtn.addEventListener('click', () => {
                if (isSpinning || hasWonToday) return;

                spinBtn.disabled = true;
                isSpinning = true;
                lastSegment = null;

                const randomSpin = Math.random() * 5000 + 5000;

                if (typeof gsap !== 'undefined') {
                    gsap.to(canvas, {
                        rotation: `+=${randomSpin}`,
                        duration: 7,
                        ease: 'cubic-bezier(0.25, 1, 0.5, 1)',
                        onUpdate: function() {
                            const currentRotation = this.targets()[0].rotation;
                            const { segment } = getSegment(currentRotation);
                            if (segment && segment !== lastSegment) {
                                if (tickSound) {
                                    tickSound.currentTime = 0;
                                    tickSound.play();
                                }
                                lastSegment = segment;
                            }
                        },
                        onComplete: () => {
                            const finalRotation = gsap.getProperty(canvas, "rotation");
                            const { segment, index } = getSegment(finalRotation);
                            drawWheel(index); // Kazanan segmenti highlight et

                            setTimeout(() => {
                                showPopup(segment);
                                isSpinning = false;
                            }, 800);
                        }
                    });
                }
            });

            // LED ışıkları oluşturma
            const createLights = () => {
                if (!wheelContainer) return;
                const lightCount = 24;
                const angleStep = 360 / lightCount;
                for(let i=0; i < lightCount; i++){
                    const angle = angleStep * i;
                    const light = document.createElement('div');
                    light.className = 'light';
                    light.style.transform = `rotate(${angle}deg) translateY(-260px)`;
                    wheelContainer.appendChild(light);
                }
            };

            // Sparkle efektleri oluşturma
            const createSparkles = () => {
                if (!sparkleContainer) return;
                const sparkleCount = 60;
                for(let i=0; i<sparkleCount; i++){
                    const sparkle = document.createElement('div');
                    sparkle.className = 'sparkle';
                    sparkle.style.left = `${Math.random() * 100}%`;
                    sparkle.style.top = `${Math.random() * 100}%`;
                    sparkle.style.animationDelay = `${Math.random() * 5}s`;
                    sparkle.style.animationDuration = `${Math.random() * 3 + 2}s`;
                    sparkleContainer.appendChild(sparkle);
                }
            };

            // Promosyon API fonksiyonları
            function clearPromotionStorage() {
                console.log('🧹 Clearing promotion storage');
                localStorage.removeItem('hasActivePromotion');
                localStorage.removeItem('promotionData');
                localStorage.removeItem('promotionStartTime');
            }

            // Sayfa yüklendiğinde localStorage'ı temizle (geçici)
            function forceCleanStorage() {
                console.log('🧹 Force cleaning all promotion storage');
                clearPromotionStorage();
                hasWonToday = false;
                if (promotionTimer) promotionTimer.classList.add('hidden');
                if (promotionModal) promotionModal.classList.add('hidden');
                if (promotionTrigger) promotionTrigger.style.display = 'none'; // Başlangıçta gizle
                if (spinBtn) {
                    spinBtn.disabled = false;
                    spinBtn.innerHTML = 'ÇARKI ÇEVİR';
                }
            }

            function checkIfAlreadyWon() {
                // Önce sunucudan aktif promosyon kontrolü yap
                fetch('/api/promotion/available')
                    .then(response => response.json())
                    .then(data => {
                        console.log('🔍 Server promotion check:', data);

                        // Promosyon henüz başlatılmadı, çarkı göster
                        if (data.success && !data.available && data.message && data.message.includes('Promosyon henüz başlatılmadı')) {
                            console.log('🎯 Promotion available but not started, showing trigger');
                            clearPromotionStorage();
                            promotionTrigger.style.display = 'flex'; // Promosyon mevcut, hediye ikonunu göster
                            hasWonToday = false;
                            return;
                        } 
                        // Promosyon yok veya süresi doldu
                        else if (!data.success) {
                            console.log('❌ No active promotion on server, hiding trigger and clearing localStorage');
                            clearPromotionStorage();
                            promotionTrigger.style.display = 'none'; // Promosyon yoksa hediye ikonunu gizle
                            hasWonToday = false;
                            return;
                        }
                        // Promosyon aktif ve görüntülenebilir
                        else if (data.success && data.available) {
                            // Sunucuda aktif promosyon var, localStorage kontrolü yap
                            const hasActivePromotion = localStorage.getItem('hasActivePromotion');
                            const promotionData = localStorage.getItem('promotionData');
                            const promotionStartTime = localStorage.getItem('promotionStartTime');

                            console.log('🔍 Checking localStorage promotion status:', {hasActivePromotion, promotionData, promotionStartTime});

                            if (hasActivePromotion === 'true' && promotionData && promotionStartTime) {
                                try {
                                    const promotion = JSON.parse(promotionData);
                                    const startTime = parseInt(promotionStartTime);
                                    const currentTime = Date.now();
                                    const elapsedMinutes = (currentTime - startTime) / (1000 * 60);

                                    console.log('✅ Found promotion in localStorage:', promotion);
                                    console.log('⏰ Elapsed minutes:', elapsedMinutes, 'Duration:', promotion.duration_minutes);

                                    if (elapsedMinutes < promotion.duration_minutes) {
                                        // Hala aktif, göster
                                        const remainingSeconds = Math.max(0, (promotion.duration_minutes * 60) - (elapsedMinutes * 60));
                                        promotion.remaining_seconds = Math.floor(remainingSeconds);

                                        console.log('⏰ Promotion still active, showing timer with', remainingSeconds, 'seconds');
                                        hasWonToday = true;
                                        showPromotion(promotion);
                                        spinBtn.disabled = true;
                                        spinBtn.innerHTML = 'BUGÜN KAZANDINIZ!';
                                        promotionTrigger.style.display = 'none';
                                    } else {
                                        // Süre dolmuş, temizle
                                        console.log('⏰ Promotion expired, clearing storage');
                                        clearPromotionStorage();
                                        promotionTrigger.style.display = 'flex';
                                        hasWonToday = false;
                                    }
                                    return;
                                } catch (e) {
                                    console.error('❌ Error parsing promotion data:', e);
                                    clearPromotionStorage();
                                }
                            }

                            // API'den gelen promosyon bilgilerini kullan
                            console.log('🎁 Using promotion data from API');
                            hasWonToday = true;
                            showPromotion({
                                code: data.code,
                                description: data.message,
                                remaining_seconds: data.remaining
                            });
                            spinBtn.disabled = true;
                            spinBtn.innerHTML = 'BUGÜN KAZANDINIZ!';
                            promotionTrigger.style.display = 'none';
                            return;
                        }

                        // LocalStorage'da promosyon yoksa ve sunucuda promosyon varsa trigger'ı göster
                        console.log('🎯 Default case - showing trigger button');
                        promotionTrigger.style.display = 'flex';
                        hasWonToday = false;
                    })
                    .catch(error => {
                        console.error('❌ Error checking promotion:', error);
                        // Hata durumunda localStorage'ı temizle ve trigger'ı gizle
                        clearPromotionStorage();
                        promotionTrigger.style.display = 'none'; // Hata durumunda da gizle
                        hasWonToday = false;
                    });
            }

            function checkPromotionAfterWin() {
                console.log('🎰 Starting promotion after wheel spin...');
                // Çark çevirildiğinde promosyonu başlat
                fetch('/api/promotion/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        console.log('🎰 Promotion start response:', data);
                        if (data.success) {
                            console.log('✅ Promotion started successfully');
                            hasWonToday = true;
                            setTimeout(() => {
                                hidePopup();
                                showPromotion({
                                    code: data.code,
                                    description: data.message || 'Tebrikler! Hediye paketi kazandınız!',
                                    remaining_seconds: data.duration ? data.duration * 60 : 1800
                                });
                            }, 3000);
                        } else {
                            console.log('⚠️ No promotion from API, using default');
                            console.log('⚠️ API Response details:', {
                                success: data.success,
                                message: data.message,
                                hasPromotion: !!data.promotion
                            });
                            // Promosyon yoksa genel mesaj göster
                            setTimeout(() => {
                                hidePopup();
                                showPromotion({
                                    code: 'HEDIYE2024',
                                    description: 'Tebrikler! Hediye paketi kazandınız!',
                                    remaining_seconds: 1800 // 30 dakika
                                });
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('❌ Promotion start error:', error);
                        setTimeout(() => {
                            hidePopup();
                            showPromotion({
                                code: 'HEDIYE2024',
                                description: 'Tebrikler! Hediye paketi kazandınız!',
                                remaining_seconds: 1800
                            });
                        }, 3000);
                    });
            }

            function showPromotion(promotion) {
                console.log('🎁 Showing promotion:', promotion);
                currentPromotion = promotion;

                // Çark modalını kapat
                closeWheelModal();

                // Sağ alt köşede timer'ı göster
                promotionTimer.classList.remove('hidden');

                // Geri sayımı başlat
                startCountdown(promotion.remaining_seconds || 1800);

                // Çark butonunu güncelle
                hasWonToday = true;
                spinBtn.disabled = true;
                spinBtn.innerHTML = 'BUGÜN KAZANDINIZ!';

                // Promosyon trigger'ı gizle
                promotionTrigger.style.display = 'none';

                // LocalStorage'a promosyon durumunu kaydet (daha kalıcı)
                console.log('💾 Saving to localStorage:', {
                    hasActivePromotion: 'true',
                    promotionData: promotion
                });
                localStorage.setItem('hasActivePromotion', 'true');
                localStorage.setItem('promotionData', JSON.stringify(promotion));
                localStorage.setItem('promotionStartTime', Date.now().toString());
            }

            function hidePromotion() {
                promotionTimer.classList.add('hidden');
                promotionModal.classList.add('hidden');
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }
                // Promosyon bittiğinde trigger'ı gizle (sunucudan tekrar kontrol edilecek)
                promotionTrigger.style.display = 'none';

                // LocalStorage'dan promosyon durumunu temizle
                clearPromotionStorage();
            }

            function startCountdown(seconds) {
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }

                function updateCountdown() {
                    if (seconds <= 0) {
                        clearInterval(countdownInterval);
                        hidePromotion();
                        hasWonToday = false;
                        spinBtn.disabled = false;
                        spinBtn.innerHTML = 'ÇARKI ÇEVİR';
                        // Promosyon süresi bittiğinde trigger'ı gizle (promosyon artık yok)
                        promotionTrigger.style.display = 'none';
                        // LocalStorage'ı temizle
                        clearPromotionStorage();
                        return;
                    }

                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = seconds % 60;
                    const timeText = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;

                    // Timer'da göster
                    const timerCountdown = document.getElementById('timer-countdown');
                    if (timerCountdown) {
                        timerCountdown.textContent = timeText;
                    }

                    // Modal'da göster
                    const modalCountdown = document.getElementById('modal-countdown');
                    if (modalCountdown) {
                        modalCountdown.textContent = timeText;
                    }

                    seconds--;
                }

                updateCountdown();
                countdownInterval = setInterval(updateCountdown, 1000);
            }

            // Başlatma
            drawWheel();
            createLights();
            createSparkles();

            // Sadece sayfa ilk yüklendiğinde kontrol et
            checkIfAlreadyWon();
        });
    </script>

    <!-- WhatsApp Butonu -->
    {% if whatsapp and whatsapp.active %}
    <a href="https://wa.me/{{ whatsapp.formatted_phone }}{% if whatsapp.message %}?text={{ whatsapp.message|urlencode }}{% endif %}"
       target="_blank"
       class="fixed bottom-6 right-6 z-50 flex items-center gap-2 rounded-full shadow-lg hover:scale-105 transition-all duration-300 bg-[#25D366]">
        <!-- WhatsApp İkonu -->
        <div class="p-3">
            <i class="fab fa-whatsapp text-2xl text-white"></i>
        </div>
        
        {% if whatsapp.show_text %}
        <span class="pr-4 text-white text-sm">{{ whatsapp.text }}</span>
        {% endif %}
    </a>
    {% endif %}

    <!-- Flash Mesajları -->
    <div id="flash-messages" class="fixed top-4 right-4 z-50 space-y-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% set flash = format_flash_message(message, category) %}
                    <div class="flash-message animate-slide-in-right bg-white rounded-lg shadow-lg border-l-4 
                        {% if category == 'error' %}border-red-500{% elif category == 'success' %}border-green-500
                        {% elif category == 'warning' %}border-yellow-500{% else %}border-blue-500{% endif %} 
                        p-4 w-80 relative">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-semibold text-gray-800">{{ flash.title }}</h3>
                                <p class="text-sm text-gray-600 mt-1">{{ flash.message }}</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" 
                                    class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <script>
        // Global yardımcı fonksiyonlar
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // Modal yardımcı fonksiyonları
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scroll için
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    const href = this.getAttribute('href');
                    if (href === '#') return; // # linklerini atla
                    
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Flash mesajlarını otomatik kaldır
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    message.classList.add('animate-fade-out');
                    setTimeout(() => message.remove(), 300);
                }, 5000);
            });
        });
    </script>

    {% if seo and seo.schema_type %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "{{ seo.schema_type }}",
        "name": "{{ seo.title }}",
        "description": "{{ seo.description }}",
        "url": "{{ request.url }}",
        {% if seo.og_image %}
        "image": "{{ url_for('static', filename='uploads/seo/' + seo.og_image, _external=True) }}",
        {% endif %}
        "inLanguage": "{{ seo.locale }}"
    }
    </script>
    {% endif %}
</body>
</html> 