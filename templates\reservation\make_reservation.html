{% extends "base.html" %}

{% block title %}{{ get_lang_text(room, 'title') }} - {{ _('Rezervasyon') }}{% endblock title %}

{% block head %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/tr.js"></script>

<style>
    /* Modern tasarım için genel stiller */
    .reservation-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 16px;
    }
    
    .page-title {
        font-size: 1.8rem;
        color: #333;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }
    
    /* Oda bilgileri kartı */
    .room-card {
        display: flex;
        flex-direction: column;
        margin-bottom: 1.5rem;
    }
    
    @media (min-width: 768px) {
        .room-card {
            flex-direction: row;
        }
    }
    
    .room-image {
        width: 100%;
        height: 220px;
        object-fit: cover;
        object-position: center;
    }
    
    @media (min-width: 768px) {
        .room-image {
            width: 35%;
            height: auto;
        }
    }
    
    .room-details {
        padding: 1.25rem;
        flex: 1;
    }
    
    .room-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
    }
    
    .room-feature {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }
    
    .room-feature-icon {
        width: 18px;
        height: 18px;
        margin-right: 10px;
        color: #C6A87D;
    }
    
    .room-feature-label {
        font-size: 0.75rem;
        color: #666;
        margin-bottom: 0.15rem;
    }
    
    .room-feature-value {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
    }
    
    .room-price {
        margin-top: 1.25rem;
        padding: 1rem;
        background-color: #f9f7f4;
        border-radius: 6px;
        border-left: 3px solid #C6A87D;
    }
    
    .room-price-label {
        font-size: 0.75rem;
        color: #666;
        margin-bottom: 0.25rem;
    }
    
    .room-price-value {
        font-size: 1.4rem;
        font-weight: 700;
        color: #C6A87D;
    }
    
    .room-price-alt {
        font-size: 0.75rem;
        color: #888;
        margin-top: 0.25rem;
    }
    
    /* Rezervasyon formu */
    .reservation-form {
        padding: 1.5rem;
    }
    
    .form-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.25rem;
        display: flex;
        align-items: center;
    }
    
    .form-title-icon {
        width: 18px;
        height: 18px;
        margin-right: 10px;
        color: #C6A87D;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    @media (min-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr 1fr;
        }
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-size: 0.8rem;
        font-weight: 500;
        color: #555;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: #fff;
        transition: all 0.2s ease;
    }
    
    .form-input:focus {
        border-color: #C6A87D;
        box-shadow: 0 0 0 2px rgba(198, 168, 125, 0.2);
        outline: none;
    }
    
    .form-input:hover {
        border-color: #bbb;
    }
    
    .input-icon-wrapper {
        position: relative;
    }
    
    .input-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        color: #C6A87D;
    }
    
    .input-with-icon {
        padding-left: 2.5rem;
    }
    
    /* Fiyat özeti kartı */
    .price-summary {
        padding: 1.25rem;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .price-summary-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .price-summary-title {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
    }
    
    .currency-selector {
        display: flex;
        gap: 0.4rem;
    }
    
    .currency-btn {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid #ddd;
        background-color: #fff;
        font-size: 0.8rem;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .currency-btn:hover {
        background-color: #f5f5f5;
    }
    
    .currency-btn.active {
        background-color: #C6A87D;
        color: white;
        border-color: #C6A87D;
    }
    
    .price-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .price-item:last-child {
        border-bottom: none;
    }
    
    .price-item-label {
        font-size: 0.8rem;
        color: #666;
    }
    
    .price-item-value {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
    }
    
    .price-total {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f9f7f4;
        border-radius: 6px;
    }
    
    .price-total-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
    }
    
    .price-total-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #C6A87D;
    }
    
    /* Buton */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-primary {
        background-color: #C6A87D;
        color: white;
        border: none;
    }
    
    .btn-primary:hover {
        background-color: #b39669;
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(198, 168, 125, 0.25);
    }
    
    .btn-primary:active {
        transform: translateY(0);
    }
    
    .btn-icon {
        width: 16px;
        height: 16px;
        margin-left: 0.5rem;
    }
    
    /* Flatpickr özelleştirmeleri */
    .flatpickr-day.selected, 
    .flatpickr-day.startRange, 
    .flatpickr-day.endRange, 
    .flatpickr-day.selected.inRange, 
    .flatpickr-day.startRange.inRange, 
    .flatpickr-day.endRange.inRange, 
    .flatpickr-day.selected:focus, 
    .flatpickr-day.startRange:focus, 
    .flatpickr-day.endRange:focus, 
    .flatpickr-day.selected:hover, 
    .flatpickr-day.startRange:hover, 
    .flatpickr-day.endRange:hover, 
    .flatpickr-day.selected.prevMonthDay, 
    .flatpickr-day.startRange.prevMonthDay, 
    .flatpickr-day.endRange.prevMonthDay, 
    .flatpickr-day.selected.nextMonthDay, 
    .flatpickr-day.startRange.nextMonthDay, 
    .flatpickr-day.endRange.nextMonthDay {
        background: #C6A87D;
        border-color: #C6A87D;
    }
    
    .flatpickr-day.inRange, 
    .flatpickr-day.prevMonthDay.inRange, 
    .flatpickr-day.nextMonthDay.inRange, 
    .flatpickr-day.today.inRange, 
    .flatpickr-day.prevMonthDay.today.inRange, 
    .flatpickr-day.nextMonthDay.today.inRange, 
    .flatpickr-day:hover, 
    .flatpickr-day.prevMonthDay:hover, 
    .flatpickr-day.nextMonthDay:hover, 
    .flatpickr-day:focus, 
    .flatpickr-day.prevMonthDay:focus, 
    .flatpickr-day.nextMonthDay:focus {
        background: rgba(198, 168, 125, 0.2);
        border-color: rgba(198, 168, 125, 0.3);
    }
    
    .flatpickr-day.today {
        border-color: #C6A87D;
    }
    
    .flatpickr-day.disabled {
        color: #ccc;
        background-color: rgba(255, 0, 0, 0.1);
        text-decoration: line-through;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-gray-50 py-8 pt-32 min-h-screen">
    <div class="reservation-container">
        <!-- Sayfa Başlığı -->
        <div class="text-center mb-8">
            <h1 class="page-title">{{ _('Rezervasyon Yap') }}</h1>
            <p class="text-gray-600 text-sm">{{ get_lang_text(room, 'title') }}</p>
        </div>
        
        <!-- Oda Bilgileri Kartı -->
        <div class="card room-card mb-8">
            <!-- Oda Resmi -->
            {% if room.gallery_images %}
            <img src="{{ url_for('static', filename='uploads/rooms/' + room.images_list[0]) }}" 
                 alt="{{ get_lang_text(room, 'title') }}" 
                 class="room-image">
            {% else %}
            <div class="room-image flex items-center justify-center bg-gray-200">
                <span class="text-gray-400">Resim Yok</span>
            </div>
            {% endif %}
            
            <!-- Oda Detayları -->
            <div class="room-details">
                <h2 class="room-title">{{ get_lang_text(room, 'title') }}</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <!-- Oda Boyutu -->
                        <div class="room-feature">
                            <svg class="room-feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                            </svg>
                            <div>
                                <p class="room-feature-label" data-translate="room_size">Oda Boyutu</p>
                                <p class="room-feature-value">{{ room.size }} m²</p>
                            </div>
                        </div>
                        
                        <!-- Oda Tipi -->
                        <div class="room-feature">
                            <svg class="room-feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <div>
                                <p class="room-feature-label" data-translate="room_type">Oda Tipi</p>
                                <p class="room-feature-value">{{ room.category.name_tr if room.category else '-' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <!-- Kapasite -->
                        <div class="room-feature">
                            <svg class="room-feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <div>
                                <p class="room-feature-label" data-translate="max_capacity">Kapasite</p>
                                <p class="room-feature-value">{{ room.capacity }} <span data-translate="person">Kişi</span></p>
                            </div>
                        </div>
                        
                        <!-- Manzara -->
                        <div class="room-feature">
                            <svg class="room-feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 22V12h6v10" />
                            </svg>
                            <div>
                                <p class="room-feature-label" data-translate="view">Manzara</p>
                                <p class="room-feature-value">{{ room.get_view_type_display() }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Fiyat Bilgisi -->
                <div class="room-price">
                    <p class="room-price-label" data-translate="price_per_night">Gecelik Fiyat</p>
                    <p class="room-price-value" id="room-main-price">
                        {% if room.currency == 'TRY' %}
                            {{ room.price|round|int }} ₺
                        {% elif room.currency == 'USD' %}
                            ${{ room.price|round|int }}
                        {% elif room.currency == 'EUR' %}
                            €{{ room.price|round|int }}
                        {% else %}
                            {{ room.price|round|int }} {{ room.currency }}
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Rezervasyon Formu -->
        <div class="card">
            <div class="reservation-form">
                <h2 class="form-title">
                    <svg class="form-title-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {{ _('Rezervasyon Detayları') }}
                </h2>
                
                <form action="{{ url_for('reservation.make_reservation', room_id=room.id) }}" method="POST" id="reservationForm"
                      data-room-id="{{ room.id }}"
                      data-room-price="{{ room.price }}"
                      data-room-currency="{{ room.currency }}"
                      data-min-stay="1"
                      data-unavailable-dates='{{ unavailable_dates | tojson | safe }}'
                      data-today="{{ today }}">
                    
                    <div class="form-grid">
                        <!-- Sol: Tarih ve Misafir Seçimi -->
                        <div>
                            <div class="form-group">
                                <label for="check_in" class="form-label">{{ _('Giriş Tarihi') }} <span class="text-red">*</span></label>
                                <div class="input-icon-wrapper">
                                    <i class="fas fa-calendar input-icon"></i>
                                    <input type="text" id="check_in" name="check_in" class="form-input pl-10" value="{{ check_in }}" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="check_out" class="form-label">{{ _('Çıkış Tarihi') }} <span class="text-red">*</span></label>
                                <div class="input-icon-wrapper">
                                    <i class="fas fa-calendar input-icon"></i>
                                    <input type="text" id="check_out" name="check_out" class="form-input pl-10" value="{{ check_out }}" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="guests" class="form-label">{{ _('Misafir Sayısı') }} <span class="text-red">*</span></label>
                                <div class="input-icon-wrapper">
                                    <i class="fas fa-user input-icon"></i>
                                    <select id="guests" name="guests" class="form-input pl-10" required>
                                        {% for i in range(1, room.capacity + 1) %}
                                            <option value="{{ i }}" {% if guests == i %}selected{% endif %}>{{ i }} {{ _('Kişi') }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sağ: Fiyat Özeti -->
                        <div class="price-summary">
                            <div class="price-summary-header">
                                <h3 class="price-summary-title" data-translate="price_summary">Fiyat Özeti</h3>
                                
                                <div class="currency-selector">
                                    <button type="button" class="currency-btn" data-currency="EUR">€</button>
                                    <button type="button" class="currency-btn" data-currency="USD">$</button>
                                    <button type="button" class="currency-btn" data-currency="TRY">₺</button>
                                </div>
                            </div>
                            
                            <div class="price-item">
                                <span class="price-item-label" data-translate="price_per_night">Gecelik Fiyat</span>
                                <span class="price-item-value" id="nightlyPrice">
                                    {% if room.currency == 'TRY' %}
                                        {{ room.price|round|int }} ₺
                                    {% elif room.currency == 'USD' %}
                                        ${{ room.price|round|int }}
                                    {% elif room.currency == 'EUR' %}
                                        €{{ room.price|round|int }}
                                    {% else %}
                                        {{ room.price|round|int }} {{ room.currency }}
                                    {% endif %}
                                </span>
                            </div>
                            
                            <div class="price-item">
                                <span class="price-item-label" data-translate="nights">Gece Sayısı</span>
                                <span class="price-item-value" id="nightCount">-</span>
                            </div>
                            
                            <div class="price-total">
                                <div class="flex justify-between items-center">
                                    <span class="price-total-label" data-translate="total_price">Toplam Tutar</span>
                                    <span class="price-total-value" id="totalPrice">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ödeme Yöntemi Seçimi (Gizli) -->
                    <input type="hidden" name="payment_method_id" value="1">
                    <input type="hidden" name="selected_currency" id="selected_currency" value="{{ room.currency }}">
                    
                    <div class="flex justify-end mt-6">
                        <button type="submit" class="btn btn-primary">
                            <span data-translate="continue_to_payment">Ödemeye Devam Et</span>
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{{ url_for('static', filename='js/simple-datepicker.js') }}"></script>
<script src="{{ url_for('static', filename='js/price-calculator.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tarih seçicileri
        const checkInPicker = flatpickr('#check_in', {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            locale: 'tr',
            disableMobile: true,
            disable: JSON.parse('{{ unavailable_dates | tojson | safe }}'),
            onChange: function(selectedDates, dateStr) {
                // Check-out tarihi en az check-in tarihinden bir gün sonra olmalı
                checkOutPicker.set('minDate', new Date(selectedDates[0].getTime() + 86400000));
                
                // Eğer check-out tarihi check-in tarihinden önceyse, check-out tarihini güncelle
                if (checkOutPicker.selectedDates[0] <= selectedDates[0]) {
                    const nextDay = new Date(selectedDates[0].getTime() + 86400000);
                    checkOutPicker.setDate(nextDay);
                }
            }
        });
        
        const checkOutPicker = flatpickr('#check_out', {
            dateFormat: 'Y-m-d',
            minDate: new Date(new Date().getTime() + 86400000), // Yarın
            locale: 'tr',
            disableMobile: true,
            disable: JSON.parse('{{ unavailable_dates | tojson | safe }}')
        });

        // Para birimi butonlarını ayarla
        const currencyButtons = document.querySelectorAll('.currency-btn');
        const roomCurrency = '{{ room.currency }}';
        
        // Varsayılan para birimini aktif yap
        currencyButtons.forEach(btn => {
            if (btn.getAttribute('data-currency') === roomCurrency) {
                btn.classList.add('active');
            }
        });
    });
</script>
{% endblock %} 