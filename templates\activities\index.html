{% extends "base.html" %}

{% block title %}{% if current_language == 'tr' %}Aktiviteler{% else %}Activities{% endif %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="relative h-[50vh] bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
        <div class="text-center">
            <h1 class="text-6xl font-great-vibes text-gold mb-4" data-translate="activities_title">Aktiviteler</h1>
            <p class="text-xl text-gray-700 max-w-2xl mx-auto" data-translate="activities_subtitle">
                Kapadokya'nın eşsiz güzelliklerini keşfetmek için unutulmaz deneyimler
            </p>
        </div>
    </section>

    <!-- Aktiviteler Listesi -->
    <section class="py-16 bg-[#F5F5F5]">
        <div class="max-w-7xl mx-auto px-4">
            {% if activities %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for activity in activities %}
                <div class="activity-card group bg-white rounded-3xl overflow-hidden hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                    <div class="relative overflow-hidden">
                        {% if activity.featured_image %}
                        <img src="{{ url_for('static', filename='uploads/activities/' + activity.featured_image) }}"
                             alt="{{ activity.get_title(current_language) }}"
                             class="w-full h-[280px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                        {% else %}
                        <div class="w-full h-[280px] bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
                            <i class="fas fa-mountain text-gold text-4xl"></i>
                        </div>
                        {% endif %}

                        <!-- Overlay Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="flex items-center justify-center space-x-4 text-white/80">
                                    {% if activity.duration %}
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-clock text-gold"></i>
                                        <span class="text-sm">{{ activity.duration }}</span>
                                    </div>
                                    {% endif %}
                                    {% if activity.difficulty %}
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-signal text-gold"></i>
                                        <span class="text-sm">{{ activity.get_difficulty_display() }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Fiyat Badge -->
                        {% if activity.price %}
                        <div class="absolute top-4 right-4">
                            <div class="bg-gold text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {{ "%.0f"|format(activity.price) }} ₺
                            </div>
                        </div>
                        {% else %}
                        <div class="absolute top-4 right-4">
                            <div class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                Ücretsiz
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Aktivite Bilgileri -->
                    <div class="p-6 bg-white">
                        <div class="text-center">
                            <h3 class="text-2xl font-great-vibes text-gray-800 mb-3">{{ activity.get_title(current_language) }}</h3>
                            
                            {% if activity.get_description(current_language) %}
                            <p class="text-gray-600 text-sm mb-6 line-clamp-3">
                                {{ activity.get_description(current_language) }}
                            </p>
                            {% endif %}

                            <!-- Aktivite Özellikleri -->
                            <div class="flex items-center justify-center space-x-4 mb-6 text-sm text-gray-500">
                                {% if activity.max_participants %}
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-users text-gold"></i>
                                    <span>{{ activity.max_participants }} kişi</span>
                                </div>
                                {% endif %}
                                {% if activity.difficulty %}
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-signal text-gold"></i>
                                    <span>{{ activity.get_difficulty_display() }}</span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Detayları Gör Butonu -->
                            <a href="{{ url_for('activities.activity_detail', slug=activity.slug) }}"
                               class="inline-flex items-center bg-gold hover:bg-gold/90 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                                <span data-translate="view_details">Detayları Gör</span>
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- Aktivite Yok Mesajı -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-mountain text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-bold text-gray-700 mb-4" data-translate="no_activities_yet">Henüz Aktivite Yok</h3>
                    <p class="text-gray-500 mb-8" data-translate="no_activities_message">
                        Şu anda mevcut aktivite bulunmuyor. Yakında yeni aktiviteler eklenecek.
                    </p>
                    <a href="{{ url_for('main.index') }}" 
                       class="inline-flex items-center bg-gold hover:bg-gold/90 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300">
                        <i class="fas fa-home mr-2"></i>
                        <span data-translate="back_to_home">Ana Sayfaya Dön</span>
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </section>
</div>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.activity-card {
    transition: all 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-8px);
}

.activity-card:hover .activity-image {
    transform: scale(1.05);
}
</style>
{% endblock %}
