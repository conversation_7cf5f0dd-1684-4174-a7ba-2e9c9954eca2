from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.room_category import RoomCategory
from models.room import Room
from models.room_feature import RoomFeature
from models.reservation import Reservation
from models import db
from datetime import datetime, timedelta, date
import calendar
from sqlalchemy import and_, or_

room_settings_bp = Blueprint('room_settings', __name__)

# Kategori route'ları
@room_settings_bp.route('/admin/room-categories')
@login_required
def category_list():
    categories = RoomCategory.query.order_by(RoomCategory.sort_order).all()
    return render_template('admin/rooms/categories/list.html', categories=categories)

@room_settings_bp.route('/admin/room-categories/create', methods=['GET', 'POST'])
@login_required
def category_create():
    if request.method == 'POST':
        try:
            category = RoomCategory(
                name_tr=request.form.get('name_tr'),
                name_en=request.form.get('name_en'),
                description_tr=request.form.get('description'),
                icon=request.form.get('icon'),
                sort_order=request.form.get('sort_order', type=int, default=0),
                status=request.form.get('status', 'active')
            )
            
            db.session.add(category)
            db.session.commit()
            
            flash('Kategori başarıyla oluşturuldu!', 'success')
            return redirect(url_for('room_settings.category_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/rooms/categories/create.html')

@room_settings_bp.route('/admin/room-categories/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def category_edit(id):
    category = RoomCategory.query.get_or_404(id)
    
    if request.method == 'POST':
        category.name_tr = request.form.get('name_tr')
        category.name_en = request.form.get('name_en')
        category.description_tr = request.form.get('description')
        category.icon = request.form.get('icon')
        category.sort_order = request.form.get('sort_order', type=int, default=0)
        category.order = request.form.get('order', type=int, default=0)
        category.status = request.form.get('status', 'active')
        
        try:
            db.session.commit()
            flash('Kategori başarıyla güncellendi!', 'success')
            return redirect(url_for('room_settings.category_list'))
        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')
        
    return render_template('admin/rooms/categories/edit.html', category=category)

@room_settings_bp.route('/admin/room-categories/delete/<int:id>', methods=['POST'])
@login_required
def category_delete(id):
    try:
        category = RoomCategory.query.get_or_404(id)
        
        # İlişkili odaları kontrol et
        rooms = Room.query.filter_by(category_id=category.id).all()
        if rooms:
            room_names = ", ".join([room.title_tr for room in rooms])
            flash(f'Bu kategori şu odalarda kullanılıyor: {room_names}. Lütfen önce bu odaların kategorisini değiştirin.', 'error')
            return redirect(url_for('room_settings.category_list'))
        
        # Kategoriyi sil
        db.session.delete(category)
        db.session.commit()
        
        flash('Kategori başarıyla silindi!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Kategori silinirken bir hata oluştu: {str(e)}', 'error')
        
    return redirect(url_for('room_settings.category_list'))

# Özellik route'ları
@room_settings_bp.route('/admin/room-features')
@login_required
def feature_list():
    features = RoomFeature.query.order_by(RoomFeature.order).all()
    return render_template('admin/rooms/features/list.html', features=features)

@room_settings_bp.route('/admin/room-features/create', methods=['GET', 'POST'])
@login_required
def feature_create():
    if request.method == 'POST':
        feature = RoomFeature(
            name=request.form.get('name'),
            description=request.form.get('description'),
            icon=request.form.get('icon'),
            order=request.form.get('order', type=int, default=0),
            status=request.form.get('status', 'active')
        )
        db.session.add(feature)
        db.session.commit()
        flash('Özellik başarıyla oluşturuldu!', 'success')
        return redirect(url_for('room_settings.feature_list'))
    return render_template('admin/rooms/features/create.html')

@room_settings_bp.route('/admin/room-features/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def feature_edit(id):
    feature = RoomFeature.query.get_or_404(id)
    
    if request.method == 'POST':
        feature.name = request.form.get('name')
        feature.description = request.form.get('description')
        feature.icon = request.form.get('icon')
        feature.order = request.form.get('order', type=int, default=0)
        feature.status = request.form.get('status', 'active')
        
        db.session.commit()
        flash('Özellik başarıyla güncellendi!', 'success')
        return redirect(url_for('room_settings.feature_list'))
        
    return render_template('admin/rooms/features/edit.html', feature=feature)

@room_settings_bp.route('/admin/room-features/delete/<int:id>', methods=['POST'])
@login_required
def feature_delete(id):
    feature = RoomFeature.query.get_or_404(id)
    
    try:
        db.session.delete(feature)
        db.session.commit()
        flash('Özellik başarıyla silindi!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Özellik silinirken bir hata oluştu!', 'error')
        
    return redirect(url_for('room_settings.feature_list')) 

@room_settings_bp.route('/admin/rooms/categories', methods=['GET'])
@login_required
def room_categories():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('main.index'))

    categories = RoomCategory.query.order_by(RoomCategory.sort_order).all()
    return render_template('admin/rooms/categories/list.html', categories=categories)

@room_settings_bp.route('/admin/rooms/availability', methods=['GET'])
@login_required
def room_availability():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('main.index'))
    
    # Tarih aralığını belirle (varsayılan olarak bugünden itibaren 30 gün)
    today = date.today()
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    category_id = request.args.get('category_id')
    
    try:
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        else:
            start_date = today
            
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        else:
            end_date = today + timedelta(days=30)
    except ValueError:
        start_date = today
        end_date = today + timedelta(days=30)
    
    # Tarih aralığını oluştur
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)
    
    # Odaları filtrele
    if category_id and category_id.isdigit():
        rooms = Room.query.filter_by(category_id=int(category_id)).order_by(Room.category_id, Room.title_tr).all()
    else:
        rooms = Room.query.order_by(Room.category_id, Room.title_tr).all()
    
    # Oda kategorilerini al
    categories = RoomCategory.query.all()
    
    # Müsaitlik durumunu hesapla
    availability = {}
    for room in rooms:
        availability[room.id] = {}
        
        # Tarihleri kontrol et
        for current_date in date_range:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # Bu oda ve tarih için rezervasyonları kontrol et
            reservation = Reservation.query.filter(
                and_(
                    Reservation.room_id == room.id,
                    current_date >= Reservation.check_in,
                    current_date < Reservation.check_out
                )
            ).first()
            
            if reservation:
                if reservation.status == 'confirmed':
                    availability[room.id][date_str] = 'occupied'
                elif reservation.status == 'pending':
                    if reservation.payment_status == 'not_paid':
                        availability[room.id][date_str] = 'payment_pending'
                    else:
                        availability[room.id][date_str] = 'pending'
            else:
                availability[room.id][date_str] = 'available'
    
    return render_template(
        'admin/rooms/availability.html', 
        rooms=rooms,
        categories=categories,
        date_range=date_range,
        availability=availability,
        start_date=start_date,
        end_date=end_date
    )

@room_settings_bp.route('/admin/api/reservation', methods=['GET'])
@login_required
def get_reservation_details():
    if not current_user.is_admin:
        return jsonify({'error': 'Yetkisiz erişim'}), 403
    
    room_id = request.args.get('room_id')
    date_str = request.args.get('date')
    
    if not room_id or not date_str:
        return jsonify({'error': 'Eksik parametreler'}), 400
    
    try:
        check_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': 'Geçersiz tarih formatı'}), 400
    
    # Bu oda ve tarih için rezervasyonları kontrol et
    reservation = Reservation.query.filter(
        and_(
            Reservation.room_id == room_id,
            check_date >= Reservation.check_in,
            check_date < Reservation.check_out
        )
    ).first()
    
    if reservation:
        return jsonify({
            'reservation': {
                'id': reservation.id,
                'name': reservation.name,
                'email': reservation.email,
                'phone': reservation.phone,
                'check_in': reservation.check_in.strftime('%Y-%m-%d'),
                'check_out': reservation.check_out.strftime('%Y-%m-%d'),
                'guests': reservation.guests,
                'status': reservation.status,
                'payment_status': reservation.payment_status,
                'message': reservation.message
            }
        })
    
    return jsonify({'reservation': None}) 