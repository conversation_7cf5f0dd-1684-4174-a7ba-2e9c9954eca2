{% extends 'admin/base.html' %}

{% block breadcrumb %}<PERSON><PERSON>{% endblock %}
{% block page_title %}<PERSON><PERSON>{% endblock %}
{% block page_subtitle %}<PERSON><PERSON>ye yeni bir resim ekleyin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <form method="POST" enctype="multipart/form-data" class="space-y-6">
        <div class="grid grid-cols-1 gap-6">
            <!-- Başlık -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Başlık</label>
                <input type="text" name="title" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>

            <!-- Açıklama -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Açıklama</label>
                <textarea name="description" rows="3"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
            </div>

            <!-- Kategori -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Kategori</label>
                <select name="category" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="hotel">Otel</option>
                    <option value="restaurant">Restoran</option>
                    <option value="spa">SPA</option>
                    <option value="room">Odalar</option>
                    <option value="other">Diğer</option>
                </select>
            </div>

            <!-- Sıralama Numarası -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Sıralama Numarası</label>
                <input type="number" name="order" value="1" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <p class="mt-2 text-sm text-gray-500">Küçük numaralar önce görünür. Örnek: 1, 2, 3...</p>
            </div>

            <!-- Resim -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Resim</label>
                <div class="mt-1 flex items-center">
                    <input type="file" name="image" required accept="image/*"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                </div>
                <p class="mt-2 text-sm text-gray-500">PNG, JPG, GIF veya WEBP. Max 5MB.</p>
            </div>
        </div>

        <!-- Butonlar -->
        <div class="flex justify-end space-x-3">
            <a href="{{ url_for('gallery.admin_gallery_list') }}"
               class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                İptal
            </a>
            <button type="submit"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Kaydet
            </button>
        </div>
    </form>
</div>
{% endblock %} 