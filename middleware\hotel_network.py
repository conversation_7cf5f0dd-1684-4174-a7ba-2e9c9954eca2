import ipaddress
from functools import wraps
from flask import request, session, flash, redirect, url_for, current_app
from models.ip_settings import IPSettings, VisitorStats, CountryVisitorStats
from models import db
from datetime import datetime, date, timedelta
import re
from utils.helpers import get_country_from_ip

def is_hotel_network(ip):
    """
    Verilen IP'nin otel ağında olup olmadığını kontrol eder
    """
    if not ip:
        return False
        
    # IP adresini temizle
    if ',' in ip:
        ip = ip.split(',')[0].strip()
    
    # Veritabanından izin verilen IP aralıklarını al
    allowed_ranges = []
    try:
        ip_settings = IPSettings.query.filter_by(is_active=True).all()
        allowed_ranges = [setting.ip_range for setting in ip_settings]
    except Exception as e:
        print(f"IP settings error: {str(e)}")
    
    # Eğer izin verilen IP aralığı yoksa, hiçbir IP'yi otel ağı olarak kabul etme
    if not allowed_ranges:
        return False
    
    # IP adresini kontrol et
    for ip_range in allowed_ranges:
        try:
            network = ipaddress.ip_network(ip_range, strict=False)
            ip_obj = ipaddress.ip_address(ip)
            if ip_obj in network:
                return True
        except Exception as e:
            print(f"IP check error: {str(e)}")
    
    return False

def check_hotel_network(ip):
    """
    Verilen IP'nin otel ağında olup olmadığını kontrol eder ve
    ziyaretçi istatistiklerini günceller
    """
    print(f"Checking IP: {ip}")
    is_hotel = is_hotel_network(ip)
    
    # Ziyaretçi istatistiklerini güncelle
    try:
        # Request konteksti dışında çalışıyorsak session'a erişmeyi atlayalım
        from flask import has_request_context
        
        today = date.today()
        
        # Günlük ziyaretçi istatistiklerini güncelle
        stats = VisitorStats.get_or_create_today()
        
        # Sayfa görüntüleme sayısını her zaman artır
        stats.increment_page_views()
        
        # Session kontrolü sadece request konteksti içindeyse yap
        if has_request_context():
            # Ülke bazlı istatistikleri güncelle - yalnızca bu IP için bugün daha önce sayılmadıysa
            today_str = today.strftime('%Y-%m-%d')
            session_key = f"visitor_tracked_{ip}_{today_str}"
            
            if session_key not in session:
                # Bu IP bugün daha önce sayılmamış, şimdi say
                session[session_key] = True
                
                # Ziyaretçi sayısını artır
                stats.increment_visitors()
                
                # Ülke bazlı istatistikleri güncelle
                country_info = get_country_from_ip(ip)
                if country_info and 'country_code' in country_info and 'country_name' in country_info:
                    country_code = country_info.get('country_code')
                    country_name = country_info.get('country_name')
                    
                    country_stats = CountryVisitorStats.get_or_create(country_code, country_name)
                    country_stats.increment_visitors()
        else:
            # Request konteksti dışındaysa, bu IP için session kontrolü yapmadan doğrudan artır
            print("Working outside of request context, incrementing visitor count directly")
            stats.increment_visitors()
            
            # Ülke bazlı istatistikleri güncelle
            country_info = get_country_from_ip(ip)
            if country_info and 'country_code' in country_info and 'country_name' in country_info:
                country_code = country_info.get('country_code')
                country_name = country_info.get('country_name')
                
                country_stats = CountryVisitorStats.get_or_create(country_code, country_name)
                country_stats.increment_visitors()
            
        # Tüm değişiklikleri kaydet
        db.session.commit()
    except Exception as e:
        print(f"Error updating visitor stats: {str(e)}")
        db.session.rollback()
    
    return is_hotel

def require_hotel_network():
    """Sadece otel ağından erişime izin veren dekoratör"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            if not is_hotel_network(client_ip):
                flash('Bu işlem sadece otel misafirleri için kullanılabilir.', 'error')
                return redirect(url_for('food.menu'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_client_ip():
    """
    İstemcinin IP adresini döndürür
    """
    # X-Forwarded-For header'ı proxy arkasında çalışırken gerçek IP'yi almak için kullanılır
    if request.environ.get('HTTP_X_FORWARDED_FOR'):
        ip = request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
    else:
        ip = request.remote_addr or '127.0.0.1'
    
    return ip

def track_visitor():
    try:
        ip_address = get_client_ip()
        
        # Skip tracking for admin routes
        if request.path.startswith('/admin'):
            return
            
        # Skip tracking for static files
        if request.path.startswith('/static'):
            return
            
        # Veritabanında bugünün kaydını oluştur
        today = date.today()
        stats = VisitorStats.query.filter_by(date=today).first()
        
        if not stats:
            stats = VisitorStats(date=today, unique_visitors=1, page_views=1)
            db.session.add(stats)
        else:
            stats.page_views += 1
        
        # Check if this IP has been seen today
        ip_setting = IPSettings.query.filter_by(ip_range=ip_address+"/32").first()
        
        if ip_setting:
            # Update existing IP record
            ip_setting.created_at = datetime.utcnow()
            
            # Check if this is a new visit today
            last_visit_date = ip_setting.created_at.date() if ip_setting.created_at else None
            is_new_today = last_visit_date != today if last_visit_date else True
            
            if is_new_today:
                stats.unique_visitors += 1
        else:
            # New IP, create record
            ip_setting = IPSettings(
                ip_range=ip_address+"/32",
                description=f"Auto-created from {ip_address}",
                is_active=True
            )
            db.session.add(ip_setting)
            stats.unique_visitors += 1
        
        # Ülke bazlı istatistikleri güncelle - sadece bu IP bugün daha önce sayılmadıysa
        today_str = today.strftime('%Y-%m-%d')
        session_key = f"visitor_tracked_{ip_address}_{today_str}"
        
        if session_key not in session:
            # Bu IP bugün daha önce sayılmamış, istatistiği artır
            session[session_key] = True
            
            # Ülke bazlı istatistikleri güncelle
            country_info = get_country_from_ip(ip_address)
            if country_info and 'country_code' in country_info and 'country_name' in country_info:
                country_code = country_info.get('country_code')
                country_name = country_info.get('country_name')
                
                country_stats = CountryVisitorStats.query.filter_by(
                    date=today,
                    country_code=country_code
                ).first()
                
                if not country_stats:
                    country_stats = CountryVisitorStats(
                        date=today,
                        country_code=country_code,
                        country_name=country_name,
                        visitors=1
                    )
                    db.session.add(country_stats)
                else:
                    country_stats.visitors += 1
                
        db.session.commit()
        print(f"Visitor tracked: {ip_address} - Stats updated")
    except Exception as e:
        print(f"Error tracking visitor: {str(e)}")
        import traceback
        print(traceback.format_exc())
        db.session.rollback()

class HotelNetworkMiddleware:
    """
    Otel ağı kontrolü için middleware
    """
    def __init__(self, app, flask_app=None):
        self.app = app
        self.flask_app = flask_app
    
    def __call__(self, environ, start_response):
        path_info = environ.get('PATH_INFO', '')
        
        # Statik dosyalar için atlama
        if path_info.startswith('/static/'):
            return self.app(environ, start_response)
        
        # Admin sayfaları için atlama (ziyaretçi istatistikleri sayfası dahil)
        if path_info.startswith('/admin/'):
            return self.app(environ, start_response)
        
        # IP adresini al
        ip_address = environ.get('HTTP_X_FORWARDED_FOR', environ.get('REMOTE_ADDR'))
        if ip_address and ',' in ip_address:
            ip_address = ip_address.split(',')[0].strip()
        
        print(f"Processing request from IP: {ip_address}")
        
        if self.flask_app:
            # Flask app kontekstinde çalış
            with self.flask_app.app_context():
                self.flask_app.logger.info(f"Checking IP: {ip_address}")
                
                # IP'nin otel ağında olup olmadığını kontrol et
                is_hotel_network(ip_address)
                
                # Ziyaretçi istatistiklerini güncelle
                today = date.today()
                today_str = today.strftime('%Y-%m-%d')
                
                # Veritabanında bugünün kaydını oluştur/güncelle
                stats = VisitorStats.query.filter_by(date=today).first()
                if not stats:
                    stats = VisitorStats(date=today, unique_visitors=0, page_views=1)
                    db.session.add(stats)
                else:
                    # Sayfa görüntüleme sayısını her zaman artır
                    stats.page_views += 1
                
                try:
                    # Ziyaretçi takibini doğrudan yapalım, test_request_context kullanmayalım
                    # Session kontrolü olmadan ziyaretçi sayısını güncelle
                    # Her istek için tekrar saymasın diye bir kısıtlama ekleyelim
                    
                    # Son 24 saatte aynı IP'nin istek yapıp yapmadığını kontrol et
                    from models.ip_settings import IPSettings
                    recent_visit = IPSettings.query.filter_by(ip_range=ip_address+"/32").first()
                    is_new_visitor = True
                    
                    if recent_visit:
                        # Eğer son ziyaret bugün olduysa, yeni ziyaretçi değil
                        if recent_visit.created_at and recent_visit.created_at.date() == today:
                            is_new_visitor = False
                        # Son ziyaret tarihini güncelle
                        recent_visit.created_at = datetime.now()
                    else:
                        # Yeni IP kaydı oluştur
                        recent_visit = IPSettings(
                            ip_range=ip_address+"/32",
                            description=f"Auto-tracked: {ip_address}",
                            is_active=False  # Sadece takip amaçlı, aktif olarak işaretleme
                        )
                        db.session.add(recent_visit)
                    
                    if is_new_visitor:
                        # Ziyaretçi sayısını artır
                        stats.unique_visitors += 1
                        
                        # Ülke bazlı istatistikleri güncelle
                        country_info = get_country_from_ip(ip_address)
                        if country_info and isinstance(country_info, dict) and 'country_code' in country_info and 'country_name' in country_info:
                            country_code = country_info.get('country_code')
                            country_name = country_info.get('country_name')
                            
                            country_stats = CountryVisitorStats.query.filter_by(
                                date=today,
                                country_code=country_code
                            ).first()
                            
                            if not country_stats:
                                country_stats = CountryVisitorStats(
                                    date=today,
                                    country_code=country_code,
                                    country_name=country_name,
                                    visitors=1
                                )
                                db.session.add(country_stats)
                            else:
                                country_stats.visitors += 1
                    
                    # Değişiklikleri kaydet
                    db.session.commit()
                except Exception as e:
                    print(f"Error updating visitor stats in middleware: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    db.session.rollback()
        
        return self.app(environ, start_response) 