from models.slider import db
from datetime import datetime
from enum import Enum

class PaymentProvider(Enum):
    VAKIFBANK = 'vakifbank'  # Sanal POS
    IYZICO = 'iyzico'        # Sanal POS

class PaymentStatus(Enum):
    PENDING = 'pending'
    SUCCESS = 'success'
    FAILED = 'failed'
    REFUNDED = 'refunded'

class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    provider = db.Column(db.Enum(PaymentProvider), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>an, default=False)
    is_test_mode = db.Column(db.Boolean, default=True)
    logo_url = db.Column(db.String(255))  # Ödeme sistemi logosu
    
    # Ortak Alanlar
    merchant_id = db.Column(db.String(100))
    api_key = db.Column(db.String(255))
    secret_key = db.Column(db.String(255))
    webhook_url = db.Column(db.String(255))
    
    # VakıfBank Özel Alanlar
    terminal_id = db.Column(db.String(50))
    terminal_user = db.Column(db.String(50))
    terminal_pass = db.Column(db.String(50))
    terminal_merch = db.Column(db.String(50))
    secure3d_url = db.Column(db.String(255))
    success_url = db.Column(db.String(255))
    fail_url = db.Column(db.String(255))
    
    # iyzico Özel Alanlar
    iyzico_api_key = db.Column(db.String(255))
    iyzico_secret_key = db.Column(db.String(255))
    iyzico_base_url = db.Column(db.String(255))
    

    
    # Genel Ayarlar
    supported_currencies = db.Column(db.JSON)  # Desteklenen para birimleri
    currency = db.Column(db.String(3), default='TRY')  # Varsayılan para birimi
    installment_options = db.Column(db.JSON)  # Taksit seçenekleri
    min_amount = db.Column(db.Float, default=0.0)  # Minimum ödeme tutarı
    max_amount = db.Column(db.Float)  # Maksimum ödeme tutarı
    transaction_fee = db.Column(db.Float, default=0.0)  # İşlem ücreti yüzdesi
    fixed_fee = db.Column(db.Float, default=0.0)  # Sabit işlem ücreti
    

    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<PaymentMethod {self.name}>'

    @property
    def is_international(self):
        """Uluslararası ödeme sistemi mi?"""
        return False  # Sadece yerel sanal POS sistemleri

    @property
    def supports_installments(self):
        """Taksit desteği var mı?"""
        return self.provider in [
            PaymentProvider.VAKIFBANK,
            PaymentProvider.IYZICO
        ]

    @property
    def supports_refund(self):
        """İade desteği var mı?"""
        return self.provider in [
            PaymentProvider.VAKIFBANK,
            PaymentProvider.IYZICO
        ]

class PaymentTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'))
    order_id = db.Column(db.String(50), unique=True)
    transaction_id = db.Column(db.String(100))
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='TRY')
    status = db.Column(db.Enum(PaymentStatus), default=PaymentStatus.PENDING)
    installment = db.Column(db.Integer, default=1)
    error_code = db.Column(db.String(50))
    error_message = db.Column(db.Text)
    response_data = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    payment_method = db.relationship('PaymentMethod', backref='transactions') 