document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('reservationForm');
    if (form) {
        try {
            const unavailableDates = JSON.parse(form.dataset.unavailableDates);
            const today = form.dataset.today;
            const roomPrice = parseFloat(form.dataset.roomPrice);
            const roomCurrency = form.dataset.roomCurrency;

            initializeReservationForm(unavailableDates, today, roomPrice, roomCurrency);
        } catch (e) {
            console.error("Rezervasyon verileri okunurken bir hata oluştu:", e);
        }
    }
});


/**
 * Rezervasyon sayfası için JavaScript fonksiyonları
 */

/**
 * Rezervasyon formunu başlat
 * @param {Array} unavailableDates - Müsait olmayan tarihler
 * @param {string} today - Bugünün tarihi
 * @param {number} roomPrice - Oda fiyatı
 * @param {string} roomCurrency - Para birimi
 */
function initializeReservationForm(unavailableDates, today, roomPrice, roomCurrency) {
    // <PERSON><PERSON>h seçiciyi başlat
    initializeDatePicker(unavailableDates, today, roomPrice, roomCurrency);
    
    // Misa<PERSON>r sayısı değişikliğini dinle
    initializeGuestSelection(roomPrice, roomCurrency);
    
    // Form gönderimini kontrol et
    validateFormSubmission();
}

/**
 * Tarih seçiciyi başlat
 */
function initializeDatePicker(unavailableDates, today, roomPrice, roomCurrency) {
    // Türkçe için localization
    flatpickr.localize(flatpickr.l10n.tr);
    
    // Tarih seçiciyi başlat
    var fp = flatpickr("#dateRangeWrapper", {
        wrap: true,
        mode: "range",
        minDate: today,
        dateFormat: "d.m.Y",
        disable: unavailableDates,
        disableMobile: false,
        allowInput: false,
        onChange: function(selectedDates, dateStr, instance) {
            if (selectedDates.length === 2) {
                var checkIn = selectedDates[0];
                var checkOut = selectedDates[1];
                
                document.getElementById('checkIn').value = formatDateForBackend(checkIn);
                document.getElementById('checkOut').value = formatDateForBackend(checkOut);
                
                // Toplam fiyatı hesapla ve göster
                updateTotalPrice(checkIn, checkOut, roomPrice, roomCurrency);
            }
        }
    });
}

/**
 * Misafir sayısı değişikliğini dinle
 */
function initializeGuestSelection(roomPrice, roomCurrency) {
    var guestsSelect = document.getElementById('guests');
    if (guestsSelect) {
        guestsSelect.addEventListener('change', function() {
            var checkIn = document.getElementById('checkIn').value;
            var checkOut = document.getElementById('checkOut').value;
            
            if (checkIn && checkOut) {
                updateTotalPrice(
                    new Date(checkIn), 
                    new Date(checkOut),
                    roomPrice,
                    roomCurrency
                );
            }
        });
    }
}

/**
 * Form gönderimini kontrol et
 */
function validateFormSubmission() {
    var reservationForm = document.getElementById('reservationForm');
    if (reservationForm) {
        reservationForm.addEventListener('submit', function(e) {
            var checkIn = document.getElementById('checkIn').value;
            var checkOut = document.getElementById('checkOut').value;
            
            if (!checkIn || !checkOut) {
                e.preventDefault();
                alert('Lütfen giriş ve çıkış tarihlerini seçin');
            }
        });
    }
}

/**
 * Tarihi backend için formatla
 * @param {Date} date - Formatlanacak tarih
 * @returns {string} - YYYY-MM-DD formatında tarih
 */
function formatDateForBackend(date) {
    // YYYY-MM-DD formatına çevir
    var month = '' + (date.getMonth() + 1);
    var day = '' + date.getDate();
    var year = date.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
}

/**
 * Toplam fiyatı hesapla ve göster
 * @param {Date} checkIn - Giriş tarihi
 * @param {Date} checkOut - Çıkış tarihi
 * @param {number} basePrice - Temel oda fiyatı
 * @param {string} currency - Para birimi
 */
function updateTotalPrice(checkIn, checkOut, basePrice, currency) {
    // Gece sayısını hesapla
    var nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    if (nights <= 0) nights = 1; // En az 1 gece
    
    // Misafir sayısını al
    var guests = parseInt(document.getElementById('guests').value) || 1;
    
    // Para birimi sembolünü belirle
    var currencySymbol = getCurrencySymbol(currency);
    
    // Toplam fiyatı hesapla (kişi sayısına göre)
    var totalPrice = basePrice * nights;
    
    // Misafir sayısına göre fiyatı güncelle
    // İlk kişi için tam fiyat, sonraki kişiler için %50 ek ücret
    if (guests > 1) {
        totalPrice += (basePrice * 0.5 * (guests - 1)) * nights;
    }
    
    // Fiyatı göster
    var totalPriceElement = document.getElementById('totalPrice');
    if (totalPriceElement) {
        if (currency === "TRY") {
            totalPriceElement.textContent = Math.round(totalPrice) + " " + currencySymbol;
        } else if (currency === "USD" || currency === "EUR") {
            totalPriceElement.textContent = currencySymbol + Math.round(totalPrice);
        } else {
            totalPriceElement.textContent = Math.round(totalPrice) + " " + currency;
        }
    }
    
    // Konsola bilgi yazdır (debug için)
    console.log("Fiyat hesaplandı:", {
        nights: nights,
        guests: guests,
        basePrice: basePrice,
        totalPrice: totalPrice,
        currency: currency
    });
}

/**
 * Para birimi sembolünü al
 * @param {string} currency - Para birimi kodu
 * @returns {string} - Para birimi sembolü
 */
function getCurrencySymbol(currency) {
    switch (currency) {
        case "TRY": return "₺";
        case "USD": return "$";
        case "EUR": return "€";
        default: return currency;
    }
} 