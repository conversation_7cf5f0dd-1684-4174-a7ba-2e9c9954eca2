from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from models.setting import Setting
from models.ip_settings import IPSettings, VisitorStats
from models import db
from datetime import datetime, date, timedelta
import os
import json
from sqlalchemy import func
from werkzeug.utils import secure_filename

admin_settings = Blueprint('admin_settings', __name__, url_prefix='/admin/settings')

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif', 'webp'}

@admin_settings.route('/', methods=['GET', 'POST'])
@login_required
def settings_list():
    if request.method == 'POST':
        # Form verilerini al
        site_title = request.form.get('site_title')
        site_description = request.form.get('site_description')
        site_font = request.form.get('site_font')
        menu_font = request.form.get('menu_font')
        use_text_logo = request.form.get('use_text_logo')
        site_text_logo = request.form.get('site_text_logo')
        main_page_content = request.form.get('main_page_content')
        reservation_link = request.form.get('reservation_link')

        # Ana logo dosyası
        if 'site_logo' in request.files:
            logo_file = request.files['site_logo']
            if logo_file and allowed_file(logo_file.filename):
                filename = secure_filename(logo_file.filename)
                upload_folder = current_app.config['UPLOAD_FOLDER']
                logo_file.save(os.path.join(upload_folder, 'settings', filename))
                Setting.set_setting('site_logo', filename)

        # Küçük logo dosyası
        if 'site_logo_small' in request.files:
            small_logo_file = request.files['site_logo_small']
            if small_logo_file and allowed_file(small_logo_file.filename):
                filename = secure_filename(small_logo_file.filename)
                upload_folder = current_app.config['UPLOAD_FOLDER']
                small_logo_file.save(os.path.join(upload_folder, 'settings', filename))
                Setting.set_setting('site_logo_small', filename)

        # Diğer ayarları kaydet
        settings_to_update = {
            'site_title': site_title,
            'site_description': site_description,
            'site_font': site_font,
            'menu_font': menu_font,
            'use_text_logo': 'True' if use_text_logo else 'False',
            'site_text_logo': site_text_logo,
            'main_page_content': main_page_content,
            'reservation_link': reservation_link
        }

        for key, value in settings_to_update.items():
            Setting.set_setting(key, value)

        flash('Ayarlar başarıyla güncellendi.', 'success')
        return redirect(url_for('admin_settings.settings_list'))

    settings = {setting.key: setting.value for setting in Setting.query.all()}
    return render_template('admin/settings/edit.html', settings=settings)

@admin_settings.route('/remove-logo/<type>', methods=['POST'])
@login_required
def remove_logo(type):
    try:
        if type == 'main':
            setting = Setting.query.filter_by(key='site_logo').first()
            if setting and setting.value:
                # Dosyayı sil
                upload_folder = current_app.config['UPLOAD_FOLDER']
                file_path = os.path.join(upload_folder, 'settings', setting.value)
                if os.path.exists(file_path):
                    os.remove(file_path)
                setting.value = None
                db.session.commit()
        elif type == 'small':
            setting = Setting.query.filter_by(key='site_logo_small').first()
            if setting and setting.value:
                # Dosyayı sil
                upload_folder = current_app.config['UPLOAD_FOLDER']
                file_path = os.path.join(upload_folder, 'settings', setting.value)
                if os.path.exists(file_path):
                    os.remove(file_path)
                setting.value = None
                db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Ziyaretçi istatistikleri için endpoint
@admin_settings.route('/visitor-stats', methods=['GET'])
@login_required
def visitor_stats():
    try:
        # Bugünün ziyaretçi sayısını al
        today = date.today()
        today_stats = VisitorStats.query.filter_by(date=today).first()
        today_visitors = today_stats.unique_visitors if today_stats else 0
        
        # Bu ayın ziyaretçi sayısını al
        first_day_of_month = today.replace(day=1)
        month_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).filter(
            VisitorStats.date >= first_day_of_month,
            VisitorStats.date <= today
        ).scalar() or 0
        
        # Toplam ziyaretçi sayısını al
        total_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).scalar() or 0
        
        return jsonify({
            'today': int(today_visitors),
            'month': int(month_visitors),
            'total': int(total_visitors)
        })
    except Exception as e:
        current_app.logger.error(f"Ziyaretçi istatistikleri alınırken hata: {str(e)}")
        return jsonify({
            'today': 0,
            'month': 0,
            'total': 0
        }), 500 