<header class="fixed top-0 left-0 right-0 z-50">
    <div id="mainHeader" class="w-full transition-all duration-500">
        <!-- Header Background -->
        <div class="transition-all duration-500 min-h-[120px]" id="headerBg">
            <div class="container mx-auto px-4">
                <!-- Header Content -->
                <div class="flex justify-between items-center h-[80px] relative px-4 sm:px-6 lg:px-8" id="headerContent">
                    <!-- Sol - Modern Burger Menü -->
                    <div class="flex items-center w-[60px] sm:w-[100px] md:w-[200px]">
                        <div class="menu-button-container">
                            <button id="menuButton" class="flex items-center text-white hover:text-gray-300 transition-all duration-300 group">
                                <!-- Modern Hamburger Icon -->
                                <div class="modern-hamburger">
                                    <span class="hamburger-line"></span>
                                    <span class="hamburger-line"></span>
                                    <span class="hamburger-line"></span>
                                </div>
                                <span class="hidden sm:inline-block ml-3 text-sm sm:text-lg font-medium tracking-wider" data-translate="menu">MENU</span>
                            </button>
                        </div>
                    </div>

                    <!-- Orta - Logo -->
                    <div class="flex-1 flex justify-center items-center transition-all duration-300 px-2 sm:px-4" id="logoContainer">
                        <a href="https://zeppelincappadocia.com/main" class="relative block">
                            <!-- Ana Logo -->
                            <img src="{% if settings.site_logo %}{{ url_for('static', filename='uploads/settings/' + settings.site_logo) }}{% else %}{{ url_for('static', filename='logo.png') }}{% endif %}"
                                 alt="{{ settings.site_title }}"
                                 class="h-[50px] sm:h-[55px] md:h-[65px] lg:h-[100px] w-auto transition-all duration-300"
                                 id="mainLogo">
                            
                            <!-- Scroll Logo -->
                            {% if settings.site_logo_small %}
                            <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo_small) }}"
                                 alt="{{ settings.site_title }}"
                                 class="h-8 sm:h-10 md:h-11 lg:h-12 w-auto absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transition-all duration-300 opacity-0 pointer-events-none"
                                 id="scrollLogo">
                            {% endif %}
                        </a>
                    </div>

                    <!-- Sağ - Rezervasyon ve Dil -->
                    <div class="flex items-center justify-end w-[60px] sm:w-[100px] md:w-[200px]">
                        <div class="flex items-center gap-3 sm:gap-4 md:gap-6">
                            <!-- Rezervasyon Butonu -->
                            <a href="{{ settings.reservation_link or '#' }}"
                               onclick="countReservationClick(event)"
                               class="hidden sm:flex items-center text-white hover:text-gray-300 transition font-medium space-x-2"
                               {% if settings.reservation_link %}target="_blank"{% endif %}>
                                <i class="fas fa-bell text-lg"></i>
                                <span class="ml-2" data-translate="reservation_button">REZERVASYON</span>
                            </a>

                            <!-- Mobil Rezervasyon İkonu -->
                            <a href="{{ settings.reservation_link or '#' }}"
                               onclick="countReservationClick(event)"
                               class="block sm:hidden text-white hover:text-gray-300 transition"
                               {% if settings.reservation_link %}target="_blank"{% endif %}>
                                <i class="fas fa-bell text-xl"></i>
                            </a>

                            <!-- Sosyal Medya İkonları -->
                            <div class="flex items-center gap-3">
                                {% if settings.whatsapp_active == 'True' and settings.whatsapp_link %}
                                    <a href="{{ settings.whatsapp_link }}"
                                       target="_blank"
                                       class="text-white hover:text-emerald-400 transition-colors duration-300">
                                        <i class="fab fa-whatsapp text-lg sm:text-xl"></i>
                                    </a>
                                {% endif %}

                                {% if settings.instagram_active == 'True' and settings.instagram_link %}
                                    <a href="{{ settings.instagram_link }}"
                                       target="_blank"
                                       class="text-white hover:text-pink-500 transition-colors duration-300">
                                        <i class="fab fa-instagram text-lg sm:text-xl"></i>
                                    </a>
                                {% endif %}
                            </div>

                            <!-- Dil Seçimi -->
                            <div class="relative group">
                                <button class="text-white hover:text-gray-300 transition flex items-center gap-1">
                                    <span class="text-[13px] sm:text-sm font-medium tracking-wider">{{ g.language|upper }}</span>
                                    <i class="fas fa-chevron-down text-[10px] mt-0.5"></i>
                                </button>
                                <div class="absolute right-0 mt-2 w-32 bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden transform scale-0 group-hover:scale-100 transition-transform origin-top shadow-lg">
                                    <a href="/change-language/tr"
                                       class="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-white/90 transition text-center {% if g.language == 'tr' %}text-gold{% endif %}">
                                        Türkçe
                                    </a>
                                    <a href="/change-language/en"
                                       class="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-white/90 transition text-center {% if g.language == 'en' %}text-gold{% endif %}">
                                        English
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Elegant Yan Menü -->
<div id="sideMenu" class="fixed top-0 left-0 h-full w-[380px] transform -translate-x-full z-[9999] transition-all duration-500 ease-out">
    <!-- Menü İçeriği -->
    <div class="relative h-full flex flex-col bg-black shadow-2xl rounded-r-3xl overflow-hidden">
        <!-- Üst Kısım: Logo ve Kapatma -->
        <div class="relative p-8 border-b border-gray-800">
            <!-- Logo -->
            <div class="flex-1 flex justify-center mb-4">
                {% if settings.use_text_logo == 'True' %}
                    <div class="text-2xl font-light text-white" style="font-family: 'Libre Caslon Display', serif;">
                        {{ settings.site_text_logo or 'Site Adı' }}
                    </div>
                {% else %}
                    {% if settings.site_logo_small %}
                        <img src="{{ url_for('static', filename='uploads/settings/' + settings.site_logo_small) }}"
                             alt="{{ settings.site_title }}"
                             class="h-14 w-auto">
                    {% endif %}
                {% endif %}
            </div>

            <!-- Elegant Divider -->
            <div class="flex justify-center">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-[1px] bg-gradient-to-r from-transparent to-gold"></div>
                    <div class="text-gold text-lg">✦</div>
                    <div class="w-12 h-[1px] bg-gradient-to-l from-transparent to-gold"></div>
                </div>
            </div>

            <!-- Elegant Close Button -->
            <button id="closeMenu" class="absolute top-6 right-6 w-8 h-8 flex items-center justify-center rounded-full
                                       text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>

        <!-- Elegant Ana Menü -->
        <nav class="flex-1 overflow-y-auto elegant-scrollbar" style="font-family: 'Poppins', sans-serif; font-weight: 200;">
            <div class="px-6 py-8 space-y-4">
                {% for menu in menus %}
                    {% if menu.active and not menu.parent_id %}
                        <div class="elegant-menu-item menu-item-animated">
                            {% if menu.has_children %}
                                <!-- Alt menülü öğe -->
                                <div class="flex items-center justify-between cursor-pointer parent-menu-elegant py-4 px-4 rounded-lg hover:bg-white/5 transition-all duration-500 group menu-hover-effect"
                                     onclick="window.toggleSubmenu(this)">
                                    <div class="flex items-center space-x-4">
                                        <span class="text-gold text-sm menu-icon-animated">⚜</span>
                                        <span class="text-white group-hover:text-gold transition-all duration-500 text-base tracking-wide menu-text-animated" style="font-weight: 200;">{{ menu.title }}</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-xs text-white/40 group-hover:text-gold transition-all duration-500 chevron-animated"></i>
                                </div>
                                <!-- Elegant Alt menü -->
                                <div class="submenu-elegant overflow-hidden transition-all duration-500 ml-6 mt-2">
                                    {% for submenu in menu.active_children %}
                                        {% if submenu.active %}
                                            <a href="{{ submenu.link }}" class="block py-3 px-4 text-white/70 hover:text-gold hover:bg-white/5 transition-all duration-400 rounded-lg group/sub mb-1 submenu-item-animated">
                                                <div class="flex items-center space-x-3">
                                                    <span class="text-white/30 text-xs submenu-dash-animated">—</span>
                                                    <span class="text-sm submenu-text-animated" style="font-weight: 200;">{{ submenu.title }}</span>
                                                </div>
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            {% else %}
                                <!-- Normal menü öğesi -->
                                <a href="{{ menu.link }}" class="flex items-center py-4 px-4 text-white hover:text-gold hover:bg-white/5 transition-all duration-500 rounded-lg group menu-hover-effect">
                                    <div class="flex items-center space-x-4">
                                        <span class="text-gold text-sm menu-icon-animated">⚜</span>
                                        <span class="text-base tracking-wide menu-text-animated" style="font-weight: 200;">{{ menu.title }}</span>
                                    </div>
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </nav>
    </div>
</div>

<style>
/* Poppins font import */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

/* Poppins ExtraLight font import */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400&display=swap');

/* Libre Caslon Display font import */
@import url('https://fonts.googleapis.com/css2?family=Libre+Caslon+Display&display=swap');

/* Modern Hamburger Menu Styles */
.modern-hamburger {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 24px;
    height: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background-color: currentColor;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.hamburger-line:nth-child(1) {
    width: 100%;
    align-self: flex-start;
}

.hamburger-line:nth-child(2) {
    width: 80%;
    align-self: flex-end;
}

.hamburger-line:nth-child(3) {
    width: 60%;
    align-self: flex-end;
}

/* Hover Effects */
#menuButton:hover .hamburger-line:nth-child(1) {
    width: 100%;
    transform: translateX(-2px);
}

#menuButton:hover .hamburger-line:nth-child(2) {
    width: 100%;
    transform: translateX(0);
}

#menuButton:hover .hamburger-line:nth-child(3) {
    width: 100%;
    transform: translateX(2px);
}

/* Active State (when menu is open) */
#menuButton.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
    width: 100%;
}

#menuButton.active .hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

#menuButton.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
    width: 100%;
}

/* Responsive Hamburger */
@media (max-width: 768px) {
    .modern-hamburger {
        width: 22px;
        height: 16px;
    }

    .hamburger-line {
        height: 2px;
    }

    /* Mobil active state düzeltmeleri */
    #menuButton.active .hamburger-line:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    #menuButton.active .hamburger-line:nth-child(3) {
        transform: rotate(-45deg) translate(5px, -5px);
    }
}

/* Elegant Side Menu Styles */
#sideMenu {
    backdrop-filter: blur(10px);
    font-family: 'Poppins', sans-serif;
    border-top-right-radius: 24px;
    border-bottom-right-radius: 24px;
    overflow: hidden;
}

#sideMenu > div {
    border-top-right-radius: 24px;
    border-bottom-right-radius: 24px;
    overflow: hidden;
}

/* Elegant Menu Items */
.elegant-menu-item {
    position: relative;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.elegant-menu-item:hover {
    transform: translateX(3px);
}

.parent-menu-elegant {
    position: relative;
}

.parent-menu-elegant::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(198, 168, 125, 0.1), transparent);
    transition: width 0.3s ease;
    border-radius: 8px;
}

.parent-menu-elegant:hover::before {
    width: 100%;
}

/* Submenu Elegant Styles */
.submenu-elegant {
    max-height: 0;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 0;
}

.parent-menu-elegant.active + .submenu-elegant {
    max-height: 500px;
    opacity: 1;
    margin-top: 4px;
}

.parent-menu-elegant.active .fas.fa-chevron-right {
    transform: rotate(90deg);
}

/* Elegant Icon Animation */
.elegant-menu-item .text-gold {
    transition: all 0.3s ease;
    display: inline-block;
}

.elegant-menu-item:hover .text-gold {
    transform: scale(1.1);
    text-shadow: 0 0 10px rgba(198, 168, 125, 0.5);
}

/* Elegant Scrollbar */
.elegant-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.elegant-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 2px;
}

.elegant-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(198, 168, 125, 0.6);
    border-radius: 2px;
    transition: background 0.3s ease;
}

.elegant-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(198, 168, 125, 0.8);
}

/* Elegant Typography - Poppins ExtraLight */
.elegant-menu-item span {
    font-family: 'Poppins', sans-serif;
    font-weight: 200 !important;
    letter-spacing: 0.5px;
}

/* Advanced Menu Animations */
.menu-item-animated {
    opacity: 0;
    transform: translateX(-20px);
    animation: slideInMenu 0.6s ease-out forwards;
}

.menu-item-animated:nth-child(1) { animation-delay: 0.1s; }
.menu-item-animated:nth-child(2) { animation-delay: 0.2s; }
.menu-item-animated:nth-child(3) { animation-delay: 0.3s; }
.menu-item-animated:nth-child(4) { animation-delay: 0.4s; }
.menu-item-animated:nth-child(5) { animation-delay: 0.5s; }
.menu-item-animated:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInMenu {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Menu Hover Effects */
.menu-hover-effect {
    position: relative;
    overflow: hidden;
}

.menu-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(198, 168, 125, 0.1), transparent);
    transition: left 0.6s ease;
}

.menu-hover-effect:hover::before {
    left: 100%;
}

/* Icon Animations */
.menu-icon-animated {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
}

.menu-hover-effect:hover .menu-icon-animated {
    transform: scale(1.2) rotate(15deg);
    text-shadow: 0 0 15px rgba(198, 168, 125, 0.6);
    filter: brightness(1.3);
}

/* Text Animations */
.menu-text-animated {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.menu-hover-effect:hover .menu-text-animated {
    transform: translateX(5px);
    letter-spacing: 1px;
}

/* Chevron Animations */
.chevron-animated {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-hover-effect:hover .chevron-animated {
    transform: translateX(5px) scale(1.1);
}

/* Submenu Animations */
.submenu-item-animated {
    opacity: 0;
    transform: translateX(-15px);
    animation: slideInSubmenu 0.4s ease-out forwards;
}

.submenu-item-animated:nth-child(1) { animation-delay: 0.1s; }
.submenu-item-animated:nth-child(2) { animation-delay: 0.2s; }
.submenu-item-animated:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInSubmenu {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.submenu-dash-animated {
    transition: all 0.3s ease;
}

.submenu-item-animated:hover .submenu-dash-animated {
    transform: scale(1.5);
    color: rgba(198, 168, 125, 0.8);
}

.submenu-text-animated {
    transition: all 0.3s ease;
}

.submenu-item-animated:hover .submenu-text-animated {
    transform: translateX(3px);
}

/* Pulse Animation for Icons */
@keyframes iconPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.menu-icon-animated:hover {
    animation: iconPulse 1s ease-in-out infinite;
}

/* Subtle Hover Effects */
.elegant-menu-item a:hover,
.parent-menu-elegant:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(3px);
}



/* Responsive Elegant Menu */
@media (max-width: 768px) {
    #sideMenu {
        width: 280px;
    }

    .elegant-menu-item span {
        font-size: 14px;
    }

    .elegant-menu-item .text-gold {
        font-size: 12px;
    }
}

/* Legacy submenu support */
.submenu {
    transition: max-height 0.3s ease-in-out;
}

.parent-menu .fa-chevron-right {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.parent-menu.active .fa-chevron-right {
    transform: rotate(90deg);
}

/* Scroll efektleri için animasyonlar */
#siteLogo {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    object-fit: contain;
    transform-origin: center;
}

#logoContainer {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

#mainLogo {
    height: 140px;
    width: auto;
    transition: all 0.5s;
}

#scrollLogo {
    height: 3.5rem; /* h-14 equivalent */
    width: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.5s;
    opacity: 0;
    pointer-events: none;
}

/* Scrolled State */
.header-scrolled #mainLogo {
    opacity: 0;
    transform: translateY(-20px);
}

.header-scrolled #scrollLogo {
    opacity: 1;
}

/* Header height transitions */
.header-scrolled #headerBg {
    min-height: 80px;
}

/* Header arkaplan geçiş efekti */
.header-bg {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(0);
    background: transparent;
}

.scrolled .header-bg {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Yan Menü Stilleri */
#sideMenu {
    background: #ffffff;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    font-family: 'Poppins', sans-serif;
    transition: transform 0.3s ease;
}

/* Menü başlığı */
.menu-header {
    color: #333333;
    letter-spacing: 1px;
    font-weight: 500;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Menü öğeleri için hover efektleri */
.menu-item .parent-menu {
    color: #333333;
    font-weight: 400;
    border: 1px solid transparent;
    position: relative;
    transition: all 0.3s ease;
}

.menu-item .parent-menu:hover {
    color: #C6A87D;
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Alt menü hover efektleri */
.submenu a {
    color: #666666;
    border: 1px solid transparent;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.submenu a:hover {
    color: #C6A87D;
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Aktif menü öğesi */
.menu-item .parent-menu.active {
    color: #C6A87D;
    background-color: #f8f9fa;
    font-weight: 500;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 3px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #C6A87D;
}

/* Menu item styles */
.menu-item {
    position: relative;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.3s;
    background: transparent;
    overflow: hidden;
}

.menu-item:hover {
    background: rgba(198, 168, 125, 0.05);
    box-shadow: 0 4px 15px rgba(198, 168, 125, 0.1);
}

/* Alt menü öğeleri için özel efekt */
.submenu .menu-item {
    padding: 0.5rem;
    margin-bottom: 0.25rem;
}

.submenu .menu-item:hover {
    background: rgba(198, 168, 125, 0.03);
    box-shadow: none;
}

/* Menü içeriği için efekt */
.menu-item > a,
.menu-item > div {
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

/* İkon hover animasyonu */
@keyframes iconFloat {
    0% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-3px) scale(1.3);
    }
    100% {
        transform: translateY(0) scale(1);
    }
}

/* İkon efekti */
.menu-item .text-gold {
    font-size: 1.5rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
}

.menu-item:hover .text-gold {
    animation: iconFloat 0.8s ease-in-out;
    text-shadow: 0 0 15px rgba(198, 168, 125, 0.7);
    color: #dfc298;
}

/* Alt menü ikonları için farklı boyut */
.submenu .menu-item .text-gold {
    font-size: 1.2rem;
}

/* Aktif menü ikonu için sürekli efekt */
@keyframes activeIconPulse {
    0% {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(198, 168, 125, 0.5);
    }
    50% {
        transform: scale(1.1);
        text-shadow: 0 0 15px rgba(198, 168, 125, 0.7);
    }
    100% {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(198, 168, 125, 0.5);
    }
}

.menu-item.active .text-gold {
    animation: activeIconPulse 2s ease-in-out infinite;
}

/* Alt çizgi efektini güncelle */
.menu-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 1px;
    background: #C6A87D;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.menu-item:hover::after {
    width: 90%;
}

/* Aktif menü öğesi için efekt */
.menu-item.active {
    background: rgba(198, 168, 125, 0.08);
}

.menu-item.active::after {
    width: 90%;
}

/* Parent menü ok işareti animasyonu */
.parent-menu .fa-chevron-right {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.parent-menu:hover .fa-chevron-right {
    transform: translateX(3px);
    color: #C6A87D;
}

.parent-menu.active .fa-chevron-right {
    transform: rotate(90deg);
    color: #C6A87D;
}

/* Logo focus outline kaldırma */
#logoContainer a {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#logoContainer a:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#logoContainer a:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Logo hover efekti */
#logoContainer a:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* Menü butonu focus outline kaldırma */
#menuButton {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#menuButton:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#menuButton:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Menü kapatma butonu focus outline kaldırma */
#closeMenu {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#closeMenu:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

#closeMenu:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Rezervasyon butonu focus outline kaldırma */
.header a[href*="reservation"] {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.header a[href*="reservation"]:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.header a[href*="reservation"]:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Sosyal medya ikonları focus outline kaldırma */
.fab {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

a .fab:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

a .fab:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Dil seçimi butonu focus outline kaldırma */
.relative.group button {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.relative.group button:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.relative.group button:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Tüm header linkleri için genel focus outline kaldırma */
header a {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

header a:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

header a:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

header button {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

header button:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

header button:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Dil seçimi hover efektleri */
.language-selector a {
    transition: all 0.3s ease;
}

.language-selector a:hover {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* Header transition stilleri */
#mainHeader {
    transition: transform 0.3s ease-out;
    will-change: transform;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 50;
}

#mainHeader.scrolled {
    transform: translateY(-100%);
}

/* Logo transition stilleri */
#mainLogo, #scrollLogo {
    transition: all 0.3s ease-out;
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* Header Styles */
#headerBg {
    background: rgba(0, 0, 0, 0);
    transition: all 0.3s ease-out;
}

#headerBg.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    min-height: 70px;
}

#headerContent {
    transition: height 0.3s ease-out;
    height: 80px;
}

#headerContent.scrolled {
    height: 60px;
}

.group:hover .scale-0 {
    transform: scale(1);
}

.bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.1);
}

.hover\:bg-white\/10:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Menü öğeleri için genel stiller */
.menu-item {
    font-family: 'Poppins', sans-serif;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.menu-item:last-child {
    border-bottom: none;
}

/* Parent menü hover efekti */
.parent-menu {
    position: relative;
    transition: all 0.2s ease;
}

.parent-menu:hover {
    padding-left: 0.25rem;
}

/* Alt menü animasyonu */
.submenu {
    max-height: 0;
    transition: all 0.2s ease;
}

.submenu.active {
    opacity: 1;
}

/* Alt menü öğeleri hover efekti */
.submenu a:hover {
    padding-left: 0.25rem;
}

/* Aktif menü öğesi stili */
.menu-item a.active,
.parent-menu.active {
    color: #C6A87D;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #fff;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #C6A87D;
}

/* Yan menü font stilini güncelle */
#sideMenu .menu-item span {
    font-family: 'Libre Caslon Display', serif;
}

/* Mobil menü ayarları güncellendi */
@media (max-width: 768px) {
    #mainHeader {
        min-height: 60px;
    }

    #headerContent {
        height: 70px !important;
        padding: 0 1rem;
    }

    #mainLogo {
        height: 50px !important;
        width: auto !important;
    }

    #scrollLogo {
        height: 32px !important;
    }

    .menu-button-container {
        padding: 0.75rem 0;
    }

    /* Mobil menü düğmesi */
    #menuButton {
        font-size: 1.25rem;
        padding: 0.75rem 0.5rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Dil seçimi ve sosyal medya ikonları */
    .menu-font {
        font-size: 0.875rem !important;
    }

    /* Yan menü genişliği */
    #sideMenu {
        width: 85%;
        max-width: 280px;
    }

    .language-selector button span {
        font-size: 13px;
    }

    /* İkonlar arası boşluk */
    .gap-3 {
        gap: 0.5rem !important;
    }

    /* İkon boyutları */
    .text-xl {
        font-size: 1.15rem !important;
    }

    /* Sağ ve sol kenar boşlukları */
    .px-4 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}

@media (min-width: 769px) {
    .language-selector button span {
        font-size: 14px;
    }
}

/* Tablet menü ayarları */
@media (min-width: 769px) and (max-width: 1024px) {
    #mainHeader {
        min-height: 70px;
    }

    #headerContent {
        height: 70px !important;
    }

    #mainLogo {
        height: 45px !important;
    }
}

/* Desktop */
@media (min-width: 1025px) {
    #headerContent {
        height: 120px !important;
    }

    #mainLogo {
        height: 100px !important;
    }

    #headerContent.scrolled {
        height: 80px !important;
    }

    #headerContent.scrolled #mainLogo {
        height: 60px !important;
    }
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Menü fonksiyonları
        const menuButton = document.getElementById('menuButton');
        const closeMenu = document.getElementById('closeMenu');
        const sideMenu = document.getElementById('sideMenu');
        const mainHeader = document.getElementById('mainHeader');
        const headerBg = document.getElementById('headerBg');
        const logoContainer = document.getElementById('logoContainer');
        const mainLogo = document.getElementById('mainLogo');
        const scrollLogo = document.getElementById('scrollLogo');
        
        let lastScroll = 0;
        const scrollThreshold = 50;

        function handleScroll() {
            const currentScroll = window.scrollY;
            
            // Header background ve içerik değişimi
            if (currentScroll > scrollThreshold) {
                headerBg.classList.add('scrolled');
                headerContent.classList.add('scrolled');
            } else {
                headerBg.classList.remove('scrolled');
                headerContent.classList.remove('scrolled');
            }

            lastScroll = currentScroll;
        }

        // Performans için throttled scroll listener
        let ticking = false;
        window.addEventListener('scroll', () => {
            if (!ticking) {
                window.requestAnimationFrame(() => {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }, { passive: true });

        // Yan menü scroll kontrolü
        function handleSideMenuScroll() {
            if (sideMenu && sideMenu.classList.contains('-translate-x-full') === false) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Menü işlemleri - modern hamburger animasyonu ile
        if (menuButton && closeMenu && sideMenu) {
            menuButton.addEventListener('click', function(e) {
                e.preventDefault();
                sideMenu.classList.remove('-translate-x-full');
                menuButton.classList.add('active');
                handleSideMenuScroll();
            });

            closeMenu.addEventListener('click', function() {
                sideMenu.classList.add('-translate-x-full');
                menuButton.classList.remove('active');
                handleSideMenuScroll();
            });

            // Dışarı tıklamada menüyü kapat
            document.addEventListener('click', function(event) {
                if (!sideMenu.contains(event.target) && !menuButton.contains(event.target)) {
                    sideMenu.classList.add('-translate-x-full');
                    menuButton.classList.remove('active');
                    handleSideMenuScroll();
                }
            });
        }

        // İlk yüklemede çağır
        if (headerBg && logoContainer && mainLogo && scrollLogo) {
            handleScroll();
        }
        handleSideMenuScroll();

        // Dil seçimi
        const langOptions = document.querySelectorAll('.lang-option');
        const selectedLang = document.getElementById('selectedLang');

        langOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                selectedLang.textContent = option.dataset.lang;
            });
        });

        // Alt menü fonksiyonunu global scope'a taşı
        window.toggleSubmenu = function(element) {
            const submenu = element.nextElementSibling;
            const icon = element.querySelector('.fa-chevron-right');
            element.classList.toggle('active');
            
            // Tüm diğer alt menüleri kapat
            document.querySelectorAll('.submenu').forEach(menu => {
                if (menu !== submenu && menu.style.maxHeight !== '0px') {
                    menu.style.maxHeight = '0px';
                    menu.previousElementSibling.classList.remove('active');
                    menu.previousElementSibling.querySelector('.fa-chevron-right').style.transform = 'rotate(0deg)';
                }
            });
            
            // Seçilen alt menüyü aç/kapat
            if (submenu.style.maxHeight === '0px' || !submenu.style.maxHeight) {
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                icon.style.transform = 'rotate(90deg)';
            } else {
                submenu.style.maxHeight = '0px';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Sayfa yüklendiğinde tüm alt menüleri kapat
        document.querySelectorAll('.submenu').forEach(submenu => {
            submenu.style.maxHeight = '0px';
        });

        // Rezervasyon tıklama sayacı
        window.countReservationClick = function(event) {
            if ('{{ settings.reservation_link }}') {
                fetch('/reservation/click', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Tıklama sayısı güncellenemedi');
                    }
                })
                .catch(error => {
                    console.error('Hata:', error);
                });
            }
        }
    });
</script>

<!-- Font Awesome 4.7.0 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> 