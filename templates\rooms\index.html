{% extends "base.html" %}

{% block title %}{% if current_language == 'tr' %}Odalarımız{% else %}Our Rooms{% endif %}{% endblock %}

{% block content %}
<div class="flex flex-col min-h-screen">
    <!-- <PERSON>ü -->
    <section class="relative w-full h-[calc(100vw*9/16)] max-h-[1080px] min-h-[480px] overflow-hidden">
        {% if rooms.items %}
            {% for room in rooms.items[:5] %}
            <div class="hero-slider-item absolute inset-0 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %} transition-opacity duration-1000">
                {% if room.images_list %}
                {% set first_image = room.images_list[0] %}
                <img src="{{ url_for('static', filename='uploads/rooms/' + first_image) }}" 
                     alt="{{ room.title }}"
                     class="w-full h-full object-cover">
                {% endif %}
                
                <!-- <PERSON><PERSON><PERSON> -->
                <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                    <div class="container mx-auto px-4">
                        <div class="max-w-2xl text-white {% if loop.index is odd %}ml-0{% else %}ml-auto text-right{% endif %}">
                            <h2 class="text-4xl md:text-6xl font-light mt-6 leading-tight">{{ get_lang_text(room, 'title') }}</h2>
                            <p class="mt-4 text-lg text-white/80">{{ get_lang_text(room, 'description')|striptags|truncate(100) }}</p>
                            <div class="flex items-center mt-8 {% if loop.index is even %}justify-end{% endif %} space-x-6">
                                {% if room.category %}
                                <span class="flex items-center space-x-2 text-white/80">
                                    <i class="{{ room.category.icon }}"></i>
                                    <span>{{ room.category.get_name(current_language) }}</span>
                                </span>
                                {% endif %}
                                <span class="flex items-center space-x-2 text-white/80">
                                    <i class="fas fa-user-friends"></i>
                                    <span>{{ room.capacity }} <span data-translate="person">Kişi</span></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Slider Kontrolleri -->
            <div class="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
                {% for room in rooms.items[:5] %}
                <button class="hero-slider-dot w-3 h-3 rounded-full bg-white/30 hover:bg-white/50 transition-all duration-300 {% if loop.first %}!bg-white{% endif %}"
                        data-index="{{ loop.index0 }}"></button>
                {% endfor %}
            </div>

            <!-- Slider Ok Kontrolleri -->
            <button class="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black/20 hover:bg-black/40 text-white flex items-center justify-center transition-all group focus:outline-none" id="prevSlide">
                <i class="fas fa-chevron-left group-hover:scale-110 transition-transform"></i>
            </button>
            <button class="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black/20 hover:bg-black/40 text-white flex items-center justify-center transition-all group focus:outline-none" id="nextSlide">
                <i class="fas fa-chevron-right group-hover:scale-110 transition-transform"></i>
            </button>
        {% endif %}
    </section>

    <!-- Mevcut içerik -->
    <div class="container mx-auto px-4 py-24">
        <div class="max-w-[1920px] mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                {% for room in rooms.items %}
                <div class="room-item">
                    <div class="bg-black/90 rounded-2xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/20 transition-all duration-500">
                        <div class="relative overflow-hidden">
                            {% if room.images_list %}
                            {% set first_image = room.images_list[0] %}
                            <img src="{{ url_for('static', filename='uploads/rooms/' + first_image) }}"
                                 alt="{{ room.title }}"
                                 class="w-full h-[600px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                            {% else %}
                            <img src="{{ url_for('static', filename='images/default-room.jpg') }}"
                                 alt="{{ room.title }}"
                                 class="w-full h-[600px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                            {% endif %}
                            
                            <div class="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/90 
                                        group-hover:via-black/30 group-hover:to-black/95 transition-all duration-500"></div>

                            <div class="absolute bottom-0 left-0 right-0 p-8">
                                <div class="space-y-4">
                                    {% if room.category %}
                                    <div class="text-center mb-4">
                                        <span class="inline-block px-3 py-1 bg-gold/20 text-gold text-sm font-medium rounded-full border border-gold/30">
                                            {{ room.category.get_name(current_language) }}
                                        </span>
                                    </div>
                                    {% endif %}

                                    <h3 class="text-3xl font-light text-white text-center">{{ get_lang_text(room, 'title') }}</h3>

                                    <div class="text-center text-white/80">
                                        <span class="text-2xl font-light">{{ room.get_formatted_price() }}</span>
                                    </div>
                                    
                                    <div class="flex justify-center pt-4 border-t border-white/10">
                                        <a href="{{ url_for('rooms.room_detail', slug=room.slug) }}" 
                                          class="inline-flex items-center text-white/80 hover:text-gold transition-colors group">
                                            <span class="text-lg tracking-wider font-light" data-translate="view_details">DETAYLI BİLGİ</span>
                                            <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

            <!-- Pagination -->
            {% if rooms.pages > 1 %}
            <div class="mt-8 flex justify-center">
                <div class="flex space-x-2">
                    {% if rooms.has_prev %}
                    <a href="{{ url_for('rooms.room_index', page=rooms.prev_num) }}" 
                       class="px-4 py-2 bg-white text-gray-700 rounded-md hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    {% endif %}

                    {% for page in rooms.iter_pages() %}
                        {% if page %}
                            <a href="{{ url_for('rooms.room_index', page=page) }}"
                               class="px-4 py-2 {% if page == rooms.page %}bg-teal-600 text-white{% else %}bg-white text-gray-700 hover:bg-gray-50{% endif %} rounded-md">
                                {{ page }}
                            </a>
                        {% else %}
                            <span class="px-4 py-2 text-gray-700">...</span>
                        {% endif %}
                    {% endfor %}

                    {% if rooms.has_next %}
                    <a href="{{ url_for('rooms.room_index', page=rooms.next_num) }}"
                       class="px-4 py-2 bg-white text-gray-700 rounded-md hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Slider Fonksiyonu
    function initHeroSlider() {
        const slides = document.querySelectorAll('.hero-slider-item');
        const dots = document.querySelectorAll('.hero-slider-dot');
        const prevButton = document.getElementById('prevSlide');
        const nextButton = document.getElementById('nextSlide');
        
        if (slides.length <= 1) return;

        let currentSlide = 0;
        let slideInterval;
        
        function showSlide(index) {
            slides.forEach(slide => slide.style.opacity = '0');
            dots.forEach(dot => dot.classList.remove('!bg-white'));
            
            slides[index].style.opacity = '1';
            dots[index].classList.add('!bg-white');
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        function startSlideShow() {
            if (slideInterval) {
                clearInterval(slideInterval);
            }
            slideInterval = setInterval(nextSlide, 5000);
        }

        prevButton.addEventListener('click', () => {
            prevSlide();
            startSlideShow();
        });

        nextButton.addEventListener('click', () => {
            nextSlide();
            startSlideShow();
        });

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
                startSlideShow();
            });
        });

        startSlideShow();

        slides.forEach(slide => {
            slide.addEventListener('mouseenter', () => clearInterval(slideInterval));
            slide.addEventListener('mouseleave', startSlideShow);
        });
    }

    // Slider'ı başlat
    initHeroSlider();
});
</script>
{% endblock %}
{% endblock %} 