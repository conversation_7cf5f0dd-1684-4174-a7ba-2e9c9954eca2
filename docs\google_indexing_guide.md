# Google Dizine Ekleme Kılavuzu

Bu kılavuz, sitenizin Google arama sonuçlarında görünmesi için gerekli adımları açıklar.

## 🚀 Hızlı Başlangıç

### 1. Sitemap Kontrolü
```bash
# Sitemap'inizi kontrol edin
curl -I https://yourdomain.com/sitemap.xml

# Veya tarayıcıda açın
https://yourdomain.com/sitemap.xml
```

### 2. Robots.txt Kontrolü
```bash
# Robots.txt dosyanızı kontrol edin
curl https://yourdomain.com/robots.txt
```

### 3. Google Search Console Kurulumu
1. [Google Search Console](https://search.google.com/search-console) adresine gidin
2. Sitenizi ekleyin ve doğrulayın
3. Sitemap'inizi gönderin

## 📋 Detaylı Adımlar

### Adım 1: Google Search Console Hesabı Oluşturma

1. **Google Search Console'a Giriş**
   - https://search.google.com/search-console adresine gidin
   - Google hesabınızla giriş yapın

2. **Property Ekleme**
   - "Add Property" butonuna tıklayın
   - "URL prefix" seçeneğini seçin
   - Sitenizin tam URL'ini girin (örn: https://yourdomain.com)

3. **Ownership Doğrulama**
   - HTML dosyası yükleme (önerilen)
   - HTML meta tag ekleme
   - Google Analytics hesabı kullanma
   - Google Tag Manager kullanma

### Adım 2: Sitemap Gönderimi

#### Otomatik Yöntem (Admin Panel)
1. Admin panele giriş yapın
2. SEO > Google Search Console menüsüne gidin
3. "Sitemap'i Google'a Gönder" butonuna tıklayın

#### Manuel Yöntem
1. Google Search Console'da sitenizi seçin
2. Sol menüden "Sitemaps" seçin
3. "Add a new sitemap" alanına `sitemap.xml` yazın
4. "Submit" butonuna tıklayın

#### Komut Satırı Yöntemi
```bash
cd /path/to/your/project
python scripts/google_indexing.py --site-url https://yourdomain.com --submit-sitemap
```

### Adım 3: URL Indexleme İsteği

#### Admin Panel Üzerinden
1. SEO > Google Search Console menüsüne gidin
2. "URL Indexleme İsteği" bölümüne URL'leri girin
3. "Indexleme İsteği Gönder" butonuna tıklayın

#### Komut Satırı Üzerinden
```bash
# Tek URL için
python scripts/google_indexing.py --site-url https://yourdomain.com --request-indexing https://yourdomain.com/new-page

# Birden fazla URL için
python scripts/google_indexing.py --site-url https://yourdomain.com --request-indexing \
  https://yourdomain.com/page1 \
  https://yourdomain.com/page2 \
  https://yourdomain.com/page3
```

### Adım 4: Google API Kurulumu (İsteğe Bağlı)

Otomatik indexleme için Google API kullanmak istiyorsanız:

1. **Google Cloud Console'da Proje Oluşturma**
   - https://console.cloud.google.com adresine gidin
   - Yeni proje oluşturun

2. **API'leri Etkinleştirme**
   - Google Search Console API
   - Web Search Indexing API

3. **Service Account Oluşturma**
   - IAM & Admin > Service Accounts
   - Yeni service account oluşturun
   - JSON key dosyasını indirin

4. **Konfigürasyon**
   ```bash
   # Service account dosyasını projeye kopyalayın
   cp /path/to/service-account.json config/google-service-account.json
   
   # Script'i service account ile çalıştırın
   python scripts/google_indexing.py \
     --site-url https://yourdomain.com \
     --service-account config/google-service-account.json \
     --submit-sitemap
   ```

## 🔧 Sorun Giderme

### Sitemap Erişilemiyor
```bash
# Sitemap'in erişilebilir olduğunu kontrol edin
curl -I https://yourdomain.com/sitemap.xml

# HTTP 200 dönmeli
HTTP/1.1 200 OK
Content-Type: application/xml
```

### Robots.txt Problemi
```bash
# Robots.txt'in doğru olduğunu kontrol edin
curl https://yourdomain.com/robots.txt

# Şu satırları içermeli:
# User-agent: *
# Allow: /
# Sitemap: https://yourdomain.com/sitemap.xml
```

### Google Search Console Hataları

**"Sitemap could not be read"**
- Sitemap URL'inin doğru olduğunu kontrol edin
- Sitemap'in geçerli XML formatında olduğunu kontrol edin
- Sunucu erişim izinlerini kontrol edin

**"Submitted URL not found (404)"**
- URL'in gerçekten erişilebilir olduğunu kontrol edin
- Redirect'lerin doğru çalıştığını kontrol edin

## 📊 İzleme ve Analiz

### Google Search Console Metrikleri
- **Coverage**: Indexlenen sayfa sayısı
- **Performance**: Arama performansı
- **Enhancements**: Sayfa deneyimi

### Komut Satırı Kontrolleri
```bash
# Indexlenme durumunu kontrol et
python scripts/google_indexing.py --site-url https://yourdomain.com --check-status \
  https://yourdomain.com/page1 \
  https://yourdomain.com/page2

# Sitemap doğrulama
python scripts/google_indexing.py --site-url https://yourdomain.com --validate-sitemap
```

## 🎯 En İyi Uygulamalar

### SEO Optimizasyonu
1. **Meta Veriler**: Her sayfa için benzersiz title ve description
2. **URL Yapısı**: Temiz ve anlamlı URL'ler
3. **İç Linkler**: Sayfalar arası bağlantılar
4. **Mobil Uyumluluk**: Responsive tasarım
5. **Sayfa Hızı**: Hızlı yüklenen sayfalar

### Sitemap Optimizasyonu
1. **Güncel Tutma**: Sitemap'i düzenli olarak güncelleyin
2. **Öncelik Belirleme**: Önemli sayfalar için yüksek priority
3. **Değişim Sıklığı**: Changefreq değerlerini doğru ayarlayın
4. **Görsel Eklemek**: Önemli görseller için image sitemap

### İzleme ve Bakım
1. **Düzenli Kontrol**: Haftalık Search Console kontrolü
2. **Hata Takibi**: 404 ve diğer hataları düzeltin
3. **Performans İzleme**: Arama performansını takip edin
4. **Güncelleme**: Yeni içerikler için indexleme isteği

## 📞 Destek

Sorunlarınız için:
1. Google Search Console Help Center
2. Google Webmaster Guidelines
3. Proje dokümantasyonu

## 🔗 Faydalı Linkler

- [Google Search Console](https://search.google.com/search-console)
- [Google Indexing API](https://developers.google.com/search/apis/indexing-api)
- [Sitemap Protocol](https://www.sitemaps.org/)
- [Robots.txt Specification](https://www.robotstxt.org/)
- [Google SEO Starter Guide](https://developers.google.com/search/docs/beginner/seo-starter-guide)
