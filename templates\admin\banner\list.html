{% extends "admin/base.html" %}

{% block title %}Banner Yönetimi{% endblock %}

{% block breadcrumb %}Banner Yönetimi{% endblock %}

{% block page_title %}Banner Yönetimi{% endblock %}
{% block page_subtitle %}Banner listesi ve yönetimi{% endblock %}

{% block admin_content %}
<div class="p-6">
    <!-- Başlık ve Ekleme Butonu -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">Banner Listesi</h2>
        <a href="{{ url_for('banner.banner_create') }}" 
           class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-300">
            <i class="fas fa-plus mr-2"></i>
            <span>Yeni Banner</span>
        </a>
    </div>

    <!-- <PERSON> -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Banner Listesi -->
    <div class="overflow-x-auto bg-white rounded-lg shadow">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Medya</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Başlık</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategori</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tür</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sıra</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for banner in banners %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if banner.file_type is defined and banner.file_type == 'video' %}
                            {% if banner.video %}
                            <div class="space-y-2">
                                <video src="{{ url_for('static', filename='uploads/banners/' + banner.video) }}" 
                                       class="h-16 w-24 object-cover rounded"
                                       controls></video>
                                <div class="text-xs text-gray-500 break-all">
                                    <i class="fas fa-video mr-1"></i> uploads/banners/{{ banner.video }}
                                </div>
                            </div>
                            {% elif banner.video_url %}
                            <div class="space-y-2">
                                <div class="h-16 w-24 bg-gray-800 rounded flex items-center justify-center">
                                    <i class="fas fa-video text-white"></i>
                                </div>
                                <div class="text-xs text-gray-500 break-all">
                                    <i class="fas fa-link mr-1"></i> URL
                                </div>
                            </div>
                            {% endif %}
                        {% elif banner.image %}
                        <div class="space-y-2">
                            <img src="{{ url_for('static', filename='uploads/banners/' + banner.image) }}" 
                                 alt="{{ banner.title or '' }}" 
                                 class="h-16 w-24 object-cover rounded">
                            <div class="text-xs text-gray-500 break-all">
                                <i class="fas fa-image mr-1"></i> uploads/banners/{{ banner.image }}
                            </div>
                        </div>
                        {% else %}
                        <div class="h-16 w-24 bg-gray-200 rounded flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">{{ banner.title or '' }}</div>
                        {% if banner.subtitle %}
                        <div class="text-sm text-gray-500">{{ banner.subtitle }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if banner.category == 'main' %}bg-blue-100 text-blue-800
                                   {% else %}bg-purple-100 text-purple-800{% endif %}">
                            {{ 'Ana Banner' if banner.category == 'main' else 'İkincil Banner' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if banner.file_type is defined and banner.file_type == 'video' %}bg-red-100 text-red-800
                                   {% else %}bg-green-100 text-green-800{% endif %}">
                            {{ 'Video' if banner.file_type is defined and banner.file_type == 'video' else 'Resim' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ banner.order }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if banner.active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {{ 'Aktif' if banner.active else 'Pasif' }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex space-x-2">
                            <a href="{{ url_for('banner.banner_edit', id=banner.id) }}" 
                               class="text-blue-600 hover:text-blue-900">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ url_for('banner.banner_delete', id=banner.id) }}" 
                                  method="POST" 
                                  class="inline"
                                  onsubmit="return confirm('Bu banneri silmek istediğinizden emin misiniz?');">
                                <button type="submit" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %} 