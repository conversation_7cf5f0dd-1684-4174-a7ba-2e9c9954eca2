from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.setting import db
import os
import psutil
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
import ipaddress
import json

security_bp = Blueprint('security', __name__)

# Güvenlik izleme için global değişkenler
request_log = deque(maxlen=1000)  # Son 1000 isteği sakla
failed_login_attempts = defaultdict(list)  # IP bazlı başarısız giriş denemeleri
blocked_ips = set()  # Engellenmiş IP'ler
suspicious_activities = deque(maxlen=500)  # Şüpheli aktiviteler

@security_bp.route('/admin/security')
@login_required
def security_dashboard():
    """Güvenlik yönetim dashboard"""
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))
    
    try:
        # Güvenlik bilgilerini topla
        security_info = get_security_info()
        return render_template('admin/security.html', security_info=security_info)
    except Exception as e:
        flash(f'Güvenlik bilgileri alınırken hata oluştu: {str(e)}', 'error')
        return render_template('admin/security.html', security_info={})

@security_bp.route('/admin/security/info')
@login_required
def security_info_api():
    """Güvenlik bilgilerini JSON olarak döndür"""
    if not current_user.is_admin:
        return jsonify({'error': 'Yetkiniz yok'})
    
    try:
        security_info = get_security_info()
        return jsonify(security_info)
    except Exception as e:
        return jsonify({'error': str(e)})

@security_bp.route('/admin/security/block-ip', methods=['POST'])
@login_required
def block_ip():
    """IP adresini engelle"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'})
    
    ip_address = request.json.get('ip')
    if not ip_address:
        return jsonify({'success': False, 'message': 'IP adresi gerekli'})
    
    try:
        blocked_ips.add(ip_address)
        log_security_event('IP_BLOCKED', f'IP {ip_address} engellendi', ip_address)
        return jsonify({'success': True, 'message': f'IP {ip_address} engellendi'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Hata: {str(e)}'})

@security_bp.route('/admin/security/unblock-ip', methods=['POST'])
@login_required
def unblock_ip():
    """IP adresinin engelini kaldır"""
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'})
    
    ip_address = request.json.get('ip')
    if not ip_address:
        return jsonify({'success': False, 'message': 'IP adresi gerekli'})
    
    try:
        blocked_ips.discard(ip_address)
        log_security_event('IP_UNBLOCKED', f'IP {ip_address} engeli kaldırıldı', ip_address)
        return jsonify({'success': True, 'message': f'IP {ip_address} engeli kaldırıldı'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Hata: {str(e)}'})

def get_security_info():
    """Güvenlik bilgilerini topla"""
    now = datetime.now()
    hour_ago = now - timedelta(hours=1)
    day_ago = now - timedelta(days=1)
    
    # Son 1 saatteki istekler
    recent_requests = [req for req in request_log if req['timestamp'] > hour_ago]
    
    # Son 24 saatteki şüpheli aktiviteler
    recent_suspicious = [act for act in suspicious_activities if act['timestamp'] > day_ago]
    
    # IP istatistikleri
    ip_stats = defaultdict(int)
    for req in recent_requests:
        ip_stats[req['ip']] += 1
    
    # En aktif IP'ler
    top_ips = sorted(ip_stats.items(), key=lambda x: x[1], reverse=True)[:10]
    
    # Başarısız giriş denemeleri
    failed_logins_count = sum(len(attempts) for attempts in failed_login_attempts.values())
    
    info = {
        'requests': {
            'total_hour': len(recent_requests),
            'total_day': len([req for req in request_log if req['timestamp'] > day_ago]),
            'unique_ips_hour': len(set(req['ip'] for req in recent_requests)),
            'top_ips': top_ips
        },
        'security': {
            'blocked_ips': list(blocked_ips),
            'failed_logins': failed_logins_count,
            'suspicious_activities': len(recent_suspicious),
            'recent_suspicious': list(recent_suspicious)[-10:]  # Son 10 şüpheli aktivite
        },
        'system': get_system_security_info()
    }
    return info

def get_system_security_info():
    """Sistem güvenlik bilgileri"""
    try:
        # CPU kullanımı
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Bellek kullanımı
        memory = psutil.virtual_memory()
        
        # Disk kullanımı
        disk = psutil.disk_usage('/')
        
        # Ağ bağlantıları
        connections = psutil.net_connections()
        active_connections = len([conn for conn in connections if conn.status == 'ESTABLISHED'])
        
        # Çalışan işlemler
        processes = len(psutil.pids())
        
        return {
            'cpu_percent': f"{cpu_percent:.1f}%",
            'memory_percent': f"{memory.percent:.1f}%",
            'memory_used': f"{memory.used / 1024 / 1024 / 1024:.2f} GB",
            'memory_total': f"{memory.total / 1024 / 1024 / 1024:.2f} GB",
            'disk_percent': f"{disk.percent:.1f}%",
            'disk_used': f"{disk.used / 1024 / 1024 / 1024:.2f} GB",
            'disk_total': f"{disk.total / 1024 / 1024 / 1024:.2f} GB",
            'active_connections': active_connections,
            'total_processes': processes
        }
    except Exception as e:
        return {
            'cpu_percent': 'Bilinmiyor',
            'memory_percent': 'Bilinmiyor',
            'memory_used': 'Bilinmiyor',
            'memory_total': 'Bilinmiyor',
            'disk_percent': 'Bilinmiyor',
            'disk_used': 'Bilinmiyor',
            'disk_total': 'Bilinmiyor',
            'active_connections': 'Bilinmiyor',
            'total_processes': 'Bilinmiyor'
        }

def log_request(ip, method, path, user_agent=None):
    """İstek logla"""
    request_log.append({
        'timestamp': datetime.now(),
        'ip': ip,
        'method': method,
        'path': path,
        'user_agent': user_agent
    })

def log_failed_login(ip):
    """Başarısız giriş denemesini logla"""
    now = datetime.now()
    failed_login_attempts[ip].append(now)
    
    # 5 dakika içinde 5'ten fazla başarısız deneme varsa şüpheli aktivite olarak işaretle
    recent_attempts = [attempt for attempt in failed_login_attempts[ip] 
                      if now - attempt < timedelta(minutes=5)]
    
    if len(recent_attempts) >= 5:
        log_security_event('BRUTE_FORCE_ATTEMPT', 
                          f'IP {ip} 5 dakika içinde {len(recent_attempts)} başarısız giriş denemesi yaptı', 
                          ip)

def log_security_event(event_type, description, ip=None):
    """Güvenlik olayını logla"""
    suspicious_activities.append({
        'timestamp': datetime.now(),
        'type': event_type,
        'description': description,
        'ip': ip
    })

def is_ip_blocked(ip):
    """IP adresinin engellenip engellenmediğini kontrol et"""
    return ip in blocked_ips

def detect_suspicious_activity(ip, path, method):
    """Şüpheli aktivite tespit et"""
    now = datetime.now()
    
    # Son 1 dakikadaki istekleri say
    recent_requests = [req for req in request_log 
                      if req['ip'] == ip and now - req['timestamp'] < timedelta(minutes=1)]
    
    # Dakikada 30'dan fazla istek şüpheli
    if len(recent_requests) > 30:
        log_security_event('HIGH_REQUEST_RATE', 
                          f'IP {ip} dakikada {len(recent_requests)} istek gönderdi', 
                          ip)
        return True
    
    # SQL injection denemeleri
    suspicious_patterns = ['union', 'select', 'drop', 'insert', 'delete', 'script', 'alert']
    if any(pattern in path.lower() for pattern in suspicious_patterns):
        log_security_event('SQL_INJECTION_ATTEMPT', 
                          f'IP {ip} şüpheli SQL injection denemesi: {path}', 
                          ip)
        return True
    
    return False
