{% extends "admin/base.html" %}

{% block breadcrumb %}Odalar / Doluluk Durumu{% endblock %}
{% block page_title %}Oda Müsaitlik Durumu{% endblock %}
{% block page_subtitle %}Odaların doluluk ve rezervasyon durumları{% endblock %}

{% block admin_content %}
<div class="container mx-auto p-4">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-wrap items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
                <div class="bg-blue-50 p-3 rounded-full">
                    <i class="fas fa-calendar-alt text-blue-500 text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-gray-800">Oda Doluluk Takvimi</h1>
            </div>
            
            <!-- Hızlı Tarih Seçimleri -->
            <div class="flex space-x-2">
                <button id="today" class="px-3 py-1 text-xs font-medium rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors">
                    Bugün
                </button>
                <button id="this-week" class="px-3 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors">
                    Bu Hafta
                </button>
                <button id="next-week" class="px-3 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors">
                    Gelecek Hafta
                </button>
                <button id="this-month" class="px-3 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors">
                    Bu Ay
                </button>
            </div>
        </div>
        
        <!-- Filtreler -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Tarih Aralığı</label>
                <div class="flex items-center space-x-3">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar text-gray-400"></i>
                        </div>
                        <input type="date" id="start_date" class="pl-10 form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" value="{{ start_date.strftime('%Y-%m-%d') }}">
                    </div>
                    <span class="text-gray-500">-</span>
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar text-gray-400"></i>
                        </div>
                        <input type="date" id="end_date" class="pl-10 form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" value="{{ end_date.strftime('%Y-%m-%d') }}">
                    </div>
                    <button id="filter-dates" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors">
                        <i class="fas fa-filter mr-2"></i>Filtrele
                    </button>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Oda Kategorisi</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-th-large text-gray-400"></i>
                    </div>
                    <select id="category-filter" class="pl-10 form-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <option value="">Tüm Kategoriler</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if request.args.get('category_id')|int == category.id %}selected{% endif %}>
                            {{ category.name_tr }}
                        </option>
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Durum Göstergeleri -->
        <div class="flex flex-wrap items-center gap-4 mb-6 bg-gray-50 p-4 rounded-lg">
            <div class="text-sm font-medium text-gray-700 mr-2">Durum:</div>
            <div class="flex items-center px-3 py-2 bg-white rounded-md shadow-sm border border-gray-200">
                <span class="inline-block w-5 h-5 bg-green-100 border border-green-400 rounded-md mr-2"></span>
                <span class="text-sm font-medium text-gray-700">Müsait</span>
            </div>
            <div class="flex items-center px-3 py-2 bg-white rounded-md shadow-sm border border-gray-200">
                <span class="inline-block w-5 h-5 bg-red-100 border border-red-400 rounded-md mr-2"></span>
                <span class="text-sm font-medium text-gray-700">Dolu</span>
            </div>
            <div class="flex items-center px-3 py-2 bg-white rounded-md shadow-sm border border-gray-200">
                <span class="inline-block w-5 h-5 bg-orange-100 border border-orange-400 rounded-md mr-2"></span>
                <span class="text-sm font-medium text-gray-700">Beklemede</span>
            </div>
            <div class="flex items-center px-3 py-2 bg-white rounded-md shadow-sm border border-gray-200">
                <span class="inline-block w-5 h-5 bg-yellow-100 border border-yellow-400 rounded-md mr-2"></span>
                <span class="text-sm font-medium text-gray-700">Ödeme Bekleniyor</span>
            </div>
        </div>
    </div>
    
    <!-- Oda Müsaitlik Tablosu -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white availability-table">
                <thead>
                    <tr class="bg-gray-100 text-gray-700 text-sm leading-normal">
                        <th class="py-3 px-4 text-left sticky left-0 bg-gray-100 z-10 border-b border-gray-200">Oda</th>
                        {% for date in date_range %}
                        {% set is_weekend = date.weekday() >= 5 %}
                        <th class="py-2 px-1 text-center border-b border-gray-200 {% if is_weekend %}bg-gray-50{% endif %}" data-date="{{ date.strftime('%Y-%m-%d') }}">
                            <div class="flex flex-col">
                                <span class="font-medium {% if is_weekend %}text-blue-600{% endif %}">{{ date.strftime('%d') }}</span>
                                <span class="text-xs">{{ date.strftime('%b') }}</span>
                                <span class="text-xs text-gray-500 {% if is_weekend %}font-medium{% endif %}">{{ ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'][date.weekday()] }}</span>
                            </div>
                        </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="text-gray-600 text-sm">
                    {% for room in rooms %}
                    <tr class="border-b border-gray-200 hover:bg-blue-50 transition-colors" data-room-id="{{ room.id }}" data-category-id="{{ room.category_id }}">
                        <td class="py-3 px-4 sticky left-0 bg-white z-10 min-w-[220px] hover:bg-blue-50">
                            <div class="flex items-center">
                                {% if room.gallery_images %}
                                <div class="w-14 h-14 rounded-lg overflow-hidden mr-3 shadow-sm border border-gray-200">
                                    <img src="/static/uploads/rooms/{{ room.gallery_images.split(',')[0] }}" 
                                         alt="{{ room.title_tr }}" 
                                         class="w-full h-full object-cover">
                                </div>
                                {% endif %}
                                <div>
                                    <p class="font-medium text-gray-800">{{ room.title_tr }}</p>
                                    <p class="text-xs text-gray-500">{{ room.category.name_tr if room.category else '' }}</p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">{{ room.capacity }} Kişilik</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        
                        {% for date in date_range %}
                        {% set date_key = date.strftime('%Y-%m-%d') %}
                        {% set status = availability.get(room.id, {}).get(date_key, 'available') %}
                        {% set is_weekend = date.weekday() >= 5 %}
                        <td class="p-0 text-center availability-cell border-l border-gray-100 {% if is_weekend %}bg-gray-50{% endif %}" 
                            data-date="{{ date_key }}" data-room-id="{{ room.id }}">
                            <div class="availability-status status-{{ status }}" 
                                 data-room-id="{{ room.id }}"
                                 data-date="{{ date_key }}"
                                 data-status="{{ status }}">
                                {% if status == 'occupied' %}
                                <i class="fas fa-bed text-red-500 opacity-50 text-xs"></i>
                                {% elif status == 'pending' %}
                                <i class="fas fa-clock text-orange-500 opacity-50 text-xs"></i>
                                {% elif status == 'payment_pending' %}
                                <i class="fas fa-credit-card text-yellow-500 opacity-50 text-xs"></i>
                                {% endif %}
                            </div>
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Rezervasyon Bilgi Modal -->
    <div id="reservation-modal" class="fixed z-50 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div class="bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full z-50 relative">
                <div class="absolute top-0 right-0 pt-4 pr-4">
                    <button type="button" id="close-modal" class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="sr-only">Kapat</span>
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bg-white p-6">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-info-circle text-blue-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="reservation-modal-title">
                                Rezervasyon Detayları
                            </h3>
                            <div id="reservation-details" class="mt-2">
                                <!-- Rezervasyon detayları buraya yüklenecek -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm" id="reservation-action">
                        İşlem Yap
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" id="close-modal-btn">
                        Kapat
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-red-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-red-700 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm hidden" id="cancel-reservation-btn">
                        <i class="fas fa-times mr-2"></i> Rezervasyonu İptal Et
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .availability-table {
        table-layout: fixed;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .availability-cell {
        width: 42px;
        height: 42px;
        padding: 0;
    }
    
    .availability-status {
        width: 100%;
        height: 42px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .availability-status:hover {
        transform: scale(1.1);
        box-shadow: 0 0 8px rgba(0,0,0,0.1);
        z-index: 5;
    }
    
    .status-available {
        background-color: #e6f7e6;
        border: 1px solid #c2e6c2;
    }
    
    .status-available:hover {
        background-color: #d1f0d1;
    }
    
    .status-occupied {
        background-color: #fae5e5;
        border: 1px solid #f1c0c0;
    }
    
    .status-occupied:hover {
        background-color: #f8d7d7;
    }
    
    .status-pending {
        background-color: #fff3e0;
        border: 1px solid #ffe0b2;
    }
    
    .status-pending:hover {
        background-color: #ffe8cc;
    }
    
    .status-payment_pending {
        background-color: #fff8e1;
        border: 1px solid #ffecb3;
    }
    
    .status-payment_pending:hover {
        background-color: #fff2cc;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tüm müsaitlik hücreleri için click eventi
    document.querySelectorAll('.availability-status').forEach(cell => {
        cell.addEventListener('click', function() {
            const roomId = this.getAttribute('data-room-id');
            const date = this.getAttribute('data-date');
            const status = this.getAttribute('data-status');
            
            // Modal içeriğini yükle
            loadReservationDetails(roomId, date, status);
            
            // Modalı göster
            document.getElementById('reservation-modal').classList.remove('hidden');
        });
    });
    
    // Modal kapatma butonları
    document.getElementById('close-modal').addEventListener('click', function() {
        document.getElementById('reservation-modal').classList.add('hidden');
    });
    
    document.getElementById('close-modal-btn').addEventListener('click', function() {
        document.getElementById('reservation-modal').classList.add('hidden');
    });
    
    // Filtreler için eventler
    document.getElementById('filter-dates').addEventListener('click', function() {
        applyFilters();
    });
    
    document.getElementById('category-filter').addEventListener('change', function() {
        applyFilters();
    });
    
    // Hızlı tarih seçimleri
    document.getElementById('today').addEventListener('click', function() {
        const today = new Date();
        const formattedDate = formatDate(today);
        document.getElementById('start_date').value = formattedDate;
        document.getElementById('end_date').value = formattedDate;
        applyFilters();
    });
    
    document.getElementById('this-week').addEventListener('click', function() {
        const today = new Date();
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Pazartesi
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Pazar
        
        document.getElementById('start_date').value = formatDate(startOfWeek);
        document.getElementById('end_date').value = formatDate(endOfWeek);
        applyFilters();
    });
    
    document.getElementById('next-week').addEventListener('click', function() {
        const today = new Date();
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1) + 7); // Gelecek Pazartesi
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Gelecek Pazar
        
        document.getElementById('start_date').value = formatDate(startOfWeek);
        document.getElementById('end_date').value = formatDate(endOfWeek);
        applyFilters();
    });
    
    document.getElementById('this-month').addEventListener('click', function() {
        const today = new Date();
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        document.getElementById('start_date').value = formatDate(startOfMonth);
        document.getElementById('end_date').value = formatDate(endOfMonth);
        applyFilters();
    });
    
    // Yardımcı fonksiyonlar
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    function applyFilters() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const categoryId = document.getElementById('category-filter').value;
        
        window.location.href = `/admin/rooms/availability?start_date=${startDate}&end_date=${endDate}&category_id=${categoryId}`;
    }
    
    // Rezervasyon detayları yükleme fonksiyonu
    function loadReservationDetails(roomId, date, status) {
        fetch(`/admin/api/reservation?room_id=${roomId}&date=${date}`)
            .then(response => response.json())
            .then(data => {
                const detailsContainer = document.getElementById('reservation-details');
                const actionButton = document.getElementById('reservation-action');
                
                if (data.reservation) {
                    // Rezervasyon varsa detayları göster
                    detailsContainer.innerHTML = `
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Müşteri:</span>
                                <span class="text-gray-700">${data.reservation.name}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">E-posta:</span>
                                <span class="text-gray-700">${data.reservation.email}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Telefon:</span>
                                <span class="text-gray-700">${data.reservation.phone}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Giriş Tarihi:</span>
                                <span class="text-gray-700">${data.reservation.check_in}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Çıkış Tarihi:</span>
                                <span class="text-gray-700">${data.reservation.check_out}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Kişi Sayısı:</span>
                                <span class="text-gray-700">${data.reservation.guests}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Durum:</span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium 
                                    ${data.reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' : 
                                    data.reservation.status === 'pending' ? 'bg-orange-100 text-orange-800' : 
                                    'bg-red-100 text-red-800'}">
                                    ${data.reservation.status === 'confirmed' ? 'Onaylandı' : 
                                    data.reservation.status === 'pending' ? 'Beklemede' : 'İptal Edildi'}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800">Ödeme Durumu:</span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium 
                                    ${data.reservation.payment_status === 'paid' ? 'bg-green-100 text-green-800' : 
                                    data.reservation.payment_status === 'processing' ? 'bg-blue-100 text-blue-800' : 
                                    data.reservation.payment_status === 'not_paid' ? 'bg-yellow-100 text-yellow-800' : 
                                    'bg-red-100 text-red-800'}">
                                    ${data.reservation.payment_status === 'paid' ? 'Ödendi' : 
                                    data.reservation.payment_status === 'processing' ? 'İşlemde' : 
                                    data.reservation.payment_status === 'not_paid' ? 'Ödeme Bekliyor' : 'Başarısız'}
                                </span>
                            </div>
                        </div>
                    `;
                    
                    // Durum butonlarını göster
                    actionButton.innerHTML = '<i class="fas fa-edit mr-2"></i>Durumu Güncelle';
                    actionButton.onclick = function() {
                        window.location.href = `/admin/reservations/${data.reservation.id}`;
                    };
                } else {
                    // Rezervasyon yoksa yeni rezervasyon oluşturma seçeneği
                    detailsContainer.innerHTML = `
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                                </div>
                                <div>
                                    <p class="text-blue-700">Bu tarih için rezervasyon bulunmuyor.</p>
                                    <p class="text-sm text-blue-600 mt-1">Yeni bir rezervasyon oluşturabilirsiniz.</p>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Yeni rezervasyon butonu
                    actionButton.innerHTML = '<i class="fas fa-plus mr-2"></i>Yeni Rezervasyon';
                    actionButton.onclick = function() {
                        window.location.href = `/admin/reservations/new?room_id=${roomId}&date=${date}`;
                    };
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const detailsContainer = document.getElementById('reservation-details');
                detailsContainer.innerHTML = `
                    <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                            </div>
                            <div>
                                <p class="text-red-700">Veri yüklenirken bir hata oluştu.</p>
                                <p class="text-sm text-red-600 mt-1">Lütfen tekrar deneyin.</p>
                            </div>
                        </div>
                    </div>
                `;
            });
    }

    // Rezervasyon iptal işlemi
    document.getElementById('cancel-reservation-btn').addEventListener('click', function() {
        const reservationCode = this.getAttribute('data-code');
        if (!reservationCode) return;
        
        if (confirm('Bu rezervasyonu iptal etmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
            // AJAX isteği ile iptal işlemini gerçekleştir
            fetch(`/reservation/cancel/${reservationCode}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Rezervasyon başarıyla iptal edildi.');
                    // Sayfayı yenile
                    window.location.reload();
                } else {
                    alert(`Hata: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('İptal işlemi sırasında bir hata oluştu.');
            });
        }
    });

    // Rezervasyon detaylarını getirme fonksiyonu - mevcut kodu güncelle
    function getReservationDetails(roomId, date) {
        // AJAX isteği ile rezervasyon detaylarını getir
        fetch(`/admin/reservations/details?room_id=${roomId}&date=${date}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const reservation = data.reservation;
                    
                    // Rezervasyon detaylarını göster
                    let html = `
                        <div class="space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Rezervasyon Kodu:</span>
                                    <span class="text-sm font-medium">#${reservation.reservation_code}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Durum:</span>
                                    <span class="text-sm font-medium ${getStatusClass(reservation.status)}">${getStatusText(reservation.status)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Ödeme Durumu:</span>
                                    <span class="text-sm font-medium ${getPaymentStatusClass(reservation.payment_status)}">${getPaymentStatusText(reservation.payment_status)}</span>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Ad Soyad:</span>
                                    <span class="text-sm">${reservation.name} ${reservation.surname}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">E-posta:</span>
                                    <span class="text-sm">${reservation.email}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Telefon:</span>
                                    <span class="text-sm">${reservation.phone}</span>
                                </div>
                            </div>
                            
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Giriş Tarihi:</span>
                                    <span class="text-sm">${formatDate(reservation.check_in)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Çıkış Tarihi:</span>
                                    <span class="text-sm">${formatDate(reservation.check_out)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">Misafir Sayısı:</span>
                                    <span class="text-sm">${reservation.guests} Kişi</span>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-500">Tutar:</span>
                                    <span class="text-sm font-bold">${formatPrice(reservation.amount, reservation.currency)}</span>
                                </div>
                            </div>
                            
                            ${reservation.special_requests ? `
                            <div class="border-t pt-3 mt-3">
                                <span class="text-sm font-medium text-gray-500 block mb-1">Özel İstekler:</span>
                                <p class="text-sm text-gray-600">${reservation.special_requests}</p>
                            </div>
                            ` : ''}
                        </div>
                    `;
                    
                    document.getElementById('reservation-details').innerHTML = html;
                    
                    // İptal butonu görünürlüğünü ayarla
                    const cancelBtn = document.getElementById('cancel-reservation-btn');
                    
                    if (reservation.status !== 'cancelled' && reservation.payment_status !== 'paid') {
                        cancelBtn.classList.remove('hidden');
                        cancelBtn.setAttribute('data-code', reservation.reservation_code);
                    } else {
                        cancelBtn.classList.add('hidden');
                        cancelBtn.removeAttribute('data-code');
                    }
                    
                    // Modalı aç
                    document.getElementById('reservation-modal').classList.remove('hidden');
                } else {
                    alert('Rezervasyon detayları yüklenemedi.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Rezervasyon detayları yüklenirken bir hata oluştu.');
            });
    }
});
</script>
{% endblock %} 