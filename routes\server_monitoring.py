from flask import Blueprint, render_template, jsonify, request, redirect, url_for
from flask_login import login_required, current_user
import psutil
import platform
import os
from datetime import datetime, timedelta, date
from sqlalchemy import func, desc
from models.ip_settings import VisitorStats, CountryVisitorStats
from models import db

server_bp = Blueprint('server_bp', __name__, url_prefix='/admin/server')

def start_monitoring():
    """Sunucu izleme sistemini başlat"""
    print("Sunucu izleme sistemi başlatıldı.")
    return True

@server_bp.route('/')
@login_required
def server_info():
    """Sunucu bilgilerini göster"""
    if not current_user.is_admin:
        return redirect(url_for('admin_bp.dashboard'))
        
    # Sistem bilgileri
    system_info = {
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'architecture': platform.machine(),
        'processor': platform.processor(),
        'hostname': platform.node(),
        'python_version': platform.python_version(),
        'platform': f"{platform.system()} {platform.release()}",
        'ip_address': request.remote_addr
    }
    
    # CPU kullanımı
    cpu_info = {
        'cpu_usage': psutil.cpu_percent(interval=1),
        'physical_cores': psutil.cpu_count(logical=False),
        'total_cores': psutil.cpu_count(logical=True),
        'max_frequency': "N/A",  # Bu bilgiler bazı sistemlerde mevcut olmayabilir
        'current_frequency': "N/A"
    }
    
    # Bellek kullanımı
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    memory_info = {
        'total': f"{memory.total / (1024 * 1024 * 1024):.2f} GB",
        'available': f"{memory.available / (1024 * 1024 * 1024):.2f} GB",
        'used': f"{memory.used / (1024 * 1024 * 1024):.2f} GB",
        'percentage': memory.percent,
        'swap_total': f"{swap.total / (1024 * 1024 * 1024):.2f} GB",
        'swap_used': f"{swap.used / (1024 * 1024 * 1024):.2f} GB"
    }
    
    # Disk kullanımı
    disk = psutil.disk_usage('/')
    disk_info = [{
        'device': '/',
        'total': f"{disk.total / (1024 * 1024 * 1024):.2f} GB",
        'used': f"{disk.used / (1024 * 1024 * 1024):.2f} GB",
        'free': f"{disk.free / (1024 * 1024 * 1024):.2f} GB",
        'percentage': disk.percent,
    }]
    
    # Sistem başlatma zamanı
    boot_time = datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
    
    # Çalışma süresi
    uptime_seconds = (datetime.now() - datetime.fromtimestamp(psutil.boot_time())).total_seconds()
    days, remainder = divmod(uptime_seconds, 86400)
    hours, remainder = divmod(remainder, 3600)
    minutes, seconds = divmod(remainder, 60)
    uptime = f"{int(days)} gün, {int(hours)} saat, {int(minutes)} dakika"
    
    # Ziyaretçi istatistikleri
    today = date.today()
    today_stats = VisitorStats.query.filter_by(date=today).first()
    today_visitors = today_stats.unique_visitors if today_stats else 0
    
    # Son 30 günlük ziyaretçi istatistikleri
    thirty_days_ago = today - timedelta(days=30)
    monthly_stats = VisitorStats.query.filter(
        VisitorStats.date >= thirty_days_ago,
        VisitorStats.date <= today
    ).all()
    
    daily_stats = [
        {
            'date': stats.date.strftime('%Y-%m-%d'),
            'visitors': stats.unique_visitors,
            'page_views': stats.page_views
        }
        for stats in monthly_stats
    ]
    
    # Tüm bilgileri tek bir objede topla
    server_info = {
        'system': system_info,
        'cpu': cpu_info,
        'memory': memory_info,
        'disk': disk_info,
        'boot_time': boot_time,
        'uptime': uptime
    }
    
    return render_template(
        'admin/server.html',
        server_info=server_info,
        today_visitors=today_visitors,
        daily_stats=daily_stats
    )

@server_bp.route('/visitor-stats')
@login_required
def visitor_stats_page():
    """Ülke bazlı ziyaretçi istatistiklerini göster"""
    if not current_user.is_admin:
        return redirect(url_for('admin_bp.dashboard'))
    
    try:
        # Son 30 gündeki ülke istatistikleri
        country_stats = CountryVisitorStats.get_country_stats(days=30)
        print(f"Country stats: {country_stats}")
        
        # SQLAlchemy Row nesnelerini JSON serileştirilebilir tuple listesine dönüştür
        country_stats_list = [(stat[0], stat[1], stat[2]) for stat in country_stats] if country_stats else []
        
        # Toplam ziyaretçi sayısı
        total_visitors = sum(stat[2] for stat in country_stats_list) if country_stats_list else 0
        print(f"Total visitors: {total_visitors}")
        
        # Ülke sayısı
        country_count = len(country_stats_list) if country_stats_list else 0
        print(f"Country count: {country_count}")
        
        # Günlük ziyaretçi istatistikleri
        today = date.today()
        thirty_days_ago = today - timedelta(days=30)
        
        daily_stats = db.session.query(
            VisitorStats.date,
            func.sum(VisitorStats.unique_visitors).label('visitors'),
            func.sum(VisitorStats.page_views).label('page_views')
        ).filter(
            VisitorStats.date >= thirty_days_ago,
            VisitorStats.date <= today
        ).group_by(
            VisitorStats.date
        ).order_by(
            VisitorStats.date.asc()
        ).all()
        
        print(f"Daily stats: {daily_stats}")
        
        # SQLAlchemy Row nesnelerini JSON serileştirilebilir dict listesine dönüştür
        daily_data = [
            {
                'date': stat[0].strftime('%Y-%m-%d'),
                'visitors': int(stat[1] or 0),
                'page_views': int(stat[2] or 0)
            }
            for stat in daily_stats
        ] if daily_stats else []
        
        print(f"Daily data: {daily_data}")
        
        return render_template(
            'admin/visitor_stats.html',
            country_stats=country_stats_list,
            total_visitors=total_visitors,
            country_count=country_count,
            daily_data=daily_data
        )
    except Exception as e:
        import traceback
        print(f"Error in visitor_stats_page: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'type': type(e).__name__
        }), 500

@server_bp.route('/api/visitor-stats')
@login_required
def visitor_stats_api():
    """Ziyaretçi istatistiklerini JSON olarak döndür"""
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized'}), 403
    
    try:
        from services.google_analytics import analytics_service

        # Google Analytics'ten gerçek veriler al
        daily_data = analytics_service.get_daily_visitors()

        # Başarılı ise Google Analytics verilerini kullan
        if daily_data.get('success', False):
            today_visitors = daily_data.get('users', 0)
            today_page_views = daily_data.get('pageviews', 0)
        else:
            # Fallback: Yerel veritabanından al
            today = date.today()
            today_stats = VisitorStats.query.filter_by(date=today).first()
            today_visitors = today_stats.unique_visitors if today_stats else 0
            today_page_views = today_stats.page_views if today_stats else 0
        
        # Aylık veriler için Google Analytics'i dene
        monthly_data = analytics_service.get_monthly_visitors()

        if monthly_data.get('success', False):
            month_visitors = monthly_data.get('users', 0)
            month_page_views = monthly_data.get('pageviews', 0)
        else:
            # Fallback: Yerel veritabanından al
            today = date.today()
            first_day_of_month = today.replace(day=1)
            month_stats = db.session.query(
                func.sum(VisitorStats.unique_visitors).label('visitors'),
                func.sum(VisitorStats.page_views).label('page_views')
            ).filter(
                VisitorStats.date >= first_day_of_month,
                VisitorStats.date <= today
            ).first()

            month_visitors = month_stats.visitors or 0 if month_stats else 0
            month_page_views = month_stats.page_views or 0 if month_stats else 0
        
        # Toplam ziyaretçi sayısını al
        total_stats = db.session.query(
            func.sum(VisitorStats.unique_visitors).label('visitors'),
            func.sum(VisitorStats.page_views).label('page_views')
        ).first()
        
        total_visitors = total_stats.visitors or 0 if total_stats else 0
        total_page_views = total_stats.page_views or 0 if total_stats else 0
        
        # Ülke bazlı istatistikler
        country_stats = CountryVisitorStats.get_country_stats(days=30)
        countries = [
            {
                'country_code': stat[0],
                'country_name': stat[1],
                'visitors': stat[2]
            }
            for stat in country_stats
        ]
        
        return jsonify({
            'today': {
                'visitors': int(today_visitors),
                'page_views': int(today_page_views)
            },
            'month': {
                'visitors': int(month_visitors),
                'page_views': int(month_page_views)
            },
            'total': {
                'visitors': int(total_visitors),
                'page_views': int(total_page_views)
            },
            'countries': countries,
            'source': 'google_analytics' if daily_data.get('success') else 'local_db'
        })
    except Exception as e:
        print(f"Ziyaretçi istatistikleri alınırken hata: {str(e)}")
        return jsonify({
            'error': str(e)
        }), 500
