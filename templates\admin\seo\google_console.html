{% extends "admin/base.html" %}

{% block title %}Google Search Console Entegrasyonu{% endblock %}

{% block admin_content %}
<div class="w-full">
    <!-- Başlık -->
    <div class="mb-6">
        <h1 class="text-2xl font-semibold text-zinc-800 flex items-center">
            <i class="fab fa-google text-blue-500 mr-3"></i>
            Google Search Console Entegrasyonu
        </h1>
        <p class="text-zinc-600 mt-2">Google arama sonuçlarında görünürlüğünüzü artırın</p>
    </div>

    <!-- Sitemap ve Robots.txt -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Sitemap Yönetimi -->
        <div class="glass-card rounded-xl p-6 border border-blue-200">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-sitemap text-white"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-zinc-800">Sitemap Yönetimi</h3>
                    <p class="text-sm text-zinc-600">XML sitemap'inizi Google'a gönderin</p>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-zinc-700 mb-2">Sitemap URL:</label>
                <div class="flex">
                    <input type="text"
                           class="flex-1 px-3 py-2 border border-zinc-300 rounded-l-lg bg-zinc-50 text-zinc-600"
                           value="{{ request.host_url }}sitemap.xml" readonly>
                    <button type="button"
                            class="px-3 py-2 bg-zinc-100 border border-l-0 border-zinc-300 rounded-r-lg hover:bg-zinc-200 transition-colors"
                            onclick="window.open('{{ request.host_url }}sitemap.xml', '_blank')">
                        <i class="fas fa-external-link-alt text-zinc-600"></i>
                    </button>
                </div>
            </div>

            <form method="POST" action="{{ url_for('seo.submit_sitemap') }}">
                <button type="submit"
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center">
                    <i class="fas fa-upload mr-2"></i>
                    Sitemap'i Google'a Gönder
                </button>
            </form>
        </div>

        <!-- Robots.txt -->
        <div class="glass-card rounded-xl p-6 border border-green-200">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-zinc-800">Robots.txt</h3>
                    <p class="text-sm text-zinc-600">Arama motoru botları için kurallar</p>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-zinc-700 mb-2">Robots.txt URL:</label>
                <div class="flex">
                    <input type="text"
                           class="flex-1 px-3 py-2 border border-zinc-300 rounded-l-lg bg-zinc-50 text-zinc-600"
                           value="{{ request.host_url }}robots.txt" readonly>
                    <button type="button"
                            class="px-3 py-2 bg-zinc-100 border border-l-0 border-zinc-300 rounded-r-lg hover:bg-zinc-200 transition-colors"
                            onclick="window.open('{{ request.host_url }}robots.txt', '_blank')">
                        <i class="fas fa-external-link-alt text-zinc-600"></i>
                    </button>
                </div>
            </div>

            <button type="button"
                    class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center"
                    onclick="validateRobots()">
                <i class="fas fa-check mr-2"></i>
                Robots.txt Doğrula
            </button>
        </div>
    </div>

    <!-- URL Indexleme -->
    <div class="glass-card rounded-xl p-6 border border-yellow-200 mb-6">
        <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-link text-white"></i>
            </div>
            <div>
                <h3 class="text-lg font-medium text-zinc-800">URL Indexleme İsteği</h3>
                <p class="text-sm text-zinc-600">Belirli sayfaları Google'a hızlıca indexletirin</p>
            </div>
        </div>

        <form method="POST" action="{{ url_for('seo.request_indexing') }}">
            <div class="mb-4">
                <label for="urls" class="block text-sm font-medium text-zinc-700 mb-2">URL'ler:</label>
                <textarea class="w-full px-3 py-2 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                          id="urls" name="urls" rows="5"
                          placeholder="/
/blog/yeni-makale
/rooms/deluxe-room
https://example.com/external-page"></textarea>
                <p class="text-sm text-zinc-500 mt-2">
                    Relatif URL'ler (/) veya tam URL'ler girebilirsiniz. Her satıra bir URL yazın.
                </p>
            </div>

            <button type="submit"
                    class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center">
                <i class="fas fa-paper-plane mr-2"></i>
                Indexleme İsteği Gönder
            </button>
        </form>
    </div>

    <!-- Site Verification -->
    <div class="glass-card rounded-xl p-6 border border-purple-200 mb-6">
        <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-shield-alt text-white"></i>
            </div>
            <div>
                <h3 class="text-lg font-medium text-zinc-800">Site Doğrulama</h3>
                <p class="text-sm text-zinc-600">Google Search Console için site doğrulama</p>
            </div>
        </div>

        <form method="POST" action="{{ url_for('integration.update_google_analytics') }}">
            <div class="mb-4">
                <label class="block text-sm font-medium text-zinc-700 mb-2">
                    Google Search Console Verification Meta Tag:
                </label>
                <input type="text"
                       name="google_search_console_verification"
                       value="{{ settings.google_search_console_verification or '' }}"
                       class="w-full px-3 py-2 border border-zinc-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                       placeholder="google-site-verification meta tag içeriği">
                <p class="text-xs text-zinc-500 mt-1">
                    Örnek: abc123def456ghi789 (sadece content kısmını girin)
                </p>
            </div>

            <button type="submit"
                    class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center">
                <i class="fas fa-save mr-2"></i>
                Verification Ayarını Kaydet
            </button>
        </form>
    </div>

    <!-- Google Search Console Araçları -->
    <div class="glass-card rounded-xl p-6 border border-blue-200 mb-6">
        <div class="flex items-center mb-6">
            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                <i class="fab fa-google text-white"></i>
            </div>
            <div>
                <h3 class="text-lg font-medium text-zinc-800">Google Search Console Araçları</h3>
                <p class="text-sm text-zinc-600">Doğrudan Google araçlarına erişim</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="https://search.google.com/search-console"
               target="_blank"
               class="flex flex-col items-center p-4 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors text-center">
                <i class="fas fa-tachometer-alt text-2xl text-blue-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">Search Console</span>
                <span class="text-xs text-zinc-600">Dashboard</span>
            </a>

            <a href="https://search.google.com/search-console/sitemaps"
               target="_blank"
               class="flex flex-col items-center p-4 border border-green-200 rounded-lg hover:bg-green-50 transition-colors text-center">
                <i class="fas fa-sitemap text-2xl text-green-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">Sitemap</span>
                <span class="text-xs text-zinc-600">Yönetimi</span>
            </a>

            <a href="https://search.google.com/search-console/index"
               target="_blank"
               class="flex flex-col items-center p-4 border border-yellow-200 rounded-lg hover:bg-yellow-50 transition-colors text-center">
                <i class="fas fa-search text-2xl text-yellow-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">URL Inspection</span>
                <span class="text-xs text-zinc-600">Araç</span>
            </a>
        </div>
    </div>

    <!-- SEO Kontrol Araçları -->
    <div class="glass-card rounded-xl p-6 border border-zinc-200">
        <div class="flex items-center mb-6">
            <div class="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-tools text-white"></i>
            </div>
            <div>
                <h3 class="text-lg font-medium text-zinc-800">SEO Kontrol Araçları</h3>
                <p class="text-sm text-zinc-600">Sitenizin performansını analiz edin</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="https://developers.google.com/speed/pagespeed/insights/"
               target="_blank"
               class="flex flex-col items-center p-4 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors text-center">
                <i class="fas fa-tachometer-alt text-2xl text-blue-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">PageSpeed</span>
                <span class="text-xs text-zinc-600">Insights</span>
            </a>

            <a href="https://search.google.com/test/mobile-friendly"
               target="_blank"
               class="flex flex-col items-center p-4 border border-green-200 rounded-lg hover:bg-green-50 transition-colors text-center">
                <i class="fas fa-mobile-alt text-2xl text-green-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">Mobile-Friendly</span>
                <span class="text-xs text-zinc-600">Test</span>
            </a>

            <a href="https://search.google.com/test/rich-results"
               target="_blank"
               class="flex flex-col items-center p-4 border border-yellow-200 rounded-lg hover:bg-yellow-50 transition-colors text-center">
                <i class="fas fa-code text-2xl text-yellow-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">Rich Results</span>
                <span class="text-xs text-zinc-600">Test</span>
            </a>

            <a href="https://www.google.com/webmasters/tools/robots-testing-tool"
               target="_blank"
               class="flex flex-col items-center p-4 border border-red-200 rounded-lg hover:bg-red-50 transition-colors text-center">
                <i class="fas fa-robot text-2xl text-red-500 mb-2"></i>
                <span class="text-sm font-medium text-zinc-800">Robots.txt</span>
                <span class="text-xs text-zinc-600">Tester</span>
            </a>
        </div>
    </div>
</div>


{% block extra_scripts %}
<script>
function validateRobots() {
    const robotsUrl = '{{ request.host_url }}robots.txt';

    // Loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Kontrol ediliyor...';
    button.disabled = true;

    fetch(robotsUrl)
        .then(response => {
            if (response.ok) {
                return response.text();
            }
            throw new Error('Robots.txt erişilemez');
        })
        .then(text => {
            // Basit doğrulama
            if (text.includes('Sitemap:')) {
                showNotification('✅ Robots.txt geçerli ve sitemap bildirimi içeriyor!', 'success');
            } else {
                showNotification('⚠️ Robots.txt bulundu ama sitemap bildirimi eksik!', 'warning');
            }
        })
        .catch(error => {
            showNotification('❌ Robots.txt kontrol edilemedi: ' + error.message, 'error');
        })
        .finally(() => {
            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
        });
}

function showNotification(message, type) {
    // Tailwind CSS ile uyumlu notification
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'warning' ? 'bg-yellow-500' : 'bg-red-500';

    notification.className = `fixed top-4 right-4 z-50 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Form submission loading states
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Gönderiliyor...';
                submitButton.disabled = true;

                // Re-enable after 3 seconds (in case of redirect)
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 3000);
            }
        });
    });
});
</script>
{% endblock %}
{% endblock %}
