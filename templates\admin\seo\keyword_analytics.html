{% extends "admin/base.html" %}

{% block breadcrumb %}SEO Yönetimi / Anahtar <PERSON><PERSON>{% endblock %}
{% block page_title %}Anahta<PERSON><PERSON>{% endblock %}
{% block page_subtitle %}Son 30 günün arama analizi{% endblock %}

{% block admin_content %}
<div class="p-6">
    <!-- Debug Bilgileri -->
    <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
        <strong>Debug:</strong>
        Top Keywords: {{ top_keywords|length }},
        Sources: {{ sources|length }},
        Pages: {{ pages|length }}
        {% if top_keywords %}
        <br>İlk keyword: {{ top_keywords[0].keyword }} ({{ top_keywords[0].total_visits }} ziyaret)
        {% endif %}
    </div>

    <!-- Üst İstatistik Kartları -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">Toplam Anahtar Kelime</p>
                    <p class="text-2xl font-bold">{{ top_keywords|length }}</p>
                </div>
                <i class="fas fa-key text-blue-200 text-2xl"></i>
            </div>
        </div>

        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm">Toplam Ziyaret</p>
                    <p class="text-2xl font-bold">{{ top_keywords|sum(attribute='total_visits') or 0 }}</p>
                </div>
                <i class="fas fa-eye text-green-200 text-2xl"></i>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm">Arama Motoru</p>
                    <p class="text-2xl font-bold">{{ sources|length }}</p>
                </div>
                <i class="fas fa-search text-purple-200 text-2xl"></i>
            </div>
        </div>

        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-4 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm">Ziyaret Edilen Sayfa</p>
                    <p class="text-2xl font-bold">{{ pages|length }}</p>
                </div>
                <i class="fas fa-file-alt text-orange-200 text-2xl"></i>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- En Çok Aranan Kelimeler -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">En Çok Aranan Kelimeler</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Kelime</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Ziyaret</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% if top_keywords %}
                            {% for keyword in top_keywords %}
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ keyword.keyword }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500 text-right">{{ keyword.total_visits }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="2" class="px-4 py-8 text-center text-gray-500">
                                    <i class="fas fa-search text-3xl mb-2 text-gray-300"></i>
                                    <p>Henüz anahtar kelime verisi bulunmuyor.</p>
                                    <p class="text-xs mt-1">Ziyaretçiler arama motorlarından gelmeye başladığında veriler burada görünecek.</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Kaynak Dağılımı -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Arama Motoru Dağılımı</h3>
            <div class="space-y-4">
                {% if sources %}
                    {% for source in sources %}
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">{{ source.source|title }}</span>
                            <span class="text-gray-900 font-medium">{{ source.count }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            {% set total_count = sources|sum(attribute='count') %}
                            {% set percentage = (source.count / total_count * 100)|round if total_count > 0 else 0 %}
                            <div class="bg-emerald-500 h-2 rounded-full" style="width: {{ percentage }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-chart-pie text-3xl mb-2 text-gray-300"></i>
                        <p>Henüz arama motoru verisi bulunmuyor.</p>
                        <p class="text-xs mt-1">Ziyaretçiler arama motorlarından gelmeye başladığında dağılım burada görünecek.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- En Çok Ziyaret Edilen Sayfalar -->
        <div class="bg-white rounded-lg shadow-sm p-6 lg:col-span-2">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">En Çok Ziyaret Edilen Sayfalar</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Sayfa</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Ziyaret</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% if pages %}
                            {% for page in pages %}
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ page.page_url }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500 text-right">{{ page.count }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="2" class="px-4 py-8 text-center text-gray-500">
                                    <i class="fas fa-file-alt text-3xl mb-2 text-gray-300"></i>
                                    <p>Henüz sayfa ziyaret verisi bulunmuyor.</p>
                                    <p class="text-xs mt-1">Ziyaretçiler sayfalarınızı ziyaret etmeye başladığında veriler burada görünecek.</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Yenileme ve Dışa Aktarma Butonları -->
    <div class="mt-6 flex justify-between items-center">
        <div class="flex space-x-3">
            <button onclick="location.reload()"
                    class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Yenile
            </button>

            <button onclick="exportData()"
                    class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-download mr-2"></i>
                CSV İndir
            </button>
        </div>

        <div class="text-sm text-gray-500">
            <i class="fas fa-clock mr-1"></i>
            Son güncelleme: <span id="current-time"></span>
        </div>
    </div>

    <!-- Bilgi Notu -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
            <div class="text-sm text-blue-700">
                <p class="font-medium mb-1">Anahtar Kelime Analizi Hakkında</p>
                <p>Bu veriler, ziyaretçilerin arama motorlarından sitenize gelmek için kullandıkları anahtar kelimeleri gösterir.
                Veriler son 30 günlük dönemi kapsar ve otomatik olarak güncellenir.</p>
            </div>
        </div>
    </div>
</div>

<script>
// Sayfa yüklendiğinde zamanı göster
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const timeString = now.toLocaleDateString('tr-TR') + ' ' + now.toLocaleTimeString('tr-TR');
    document.getElementById('current-time').textContent = timeString;
});

function exportData() {
    // CSV export fonksiyonu
    const data = [
        ['Anahtar Kelime', 'Ziyaret Sayısı'],
        {% for keyword in top_keywords %}
        ['{{ keyword.keyword }}', '{{ keyword.total_visits }}'],
        {% endfor %}
    ];

    let csvContent = "data:text/csv;charset=utf-8,";
    data.forEach(function(rowArray) {
        let row = rowArray.join(",");
        csvContent += row + "\r\n";
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "anahtar_kelime_analizi.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Otomatik yenileme (5 dakikada bir)
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}