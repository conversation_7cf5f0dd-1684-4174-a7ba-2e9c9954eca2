{% extends "base.html" %}

{% block title %}{% if current_language == 'tr' %}Ana Sayfa{% else %}Home Page{% endif %} | {{ settings.site_title or 'Zeppelin Hotel' }}{% endblock %}

{% block head %}
<!-- Tailwind CSS CDN -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Tailwind Config dosyasını ekleyin -->
<script src="{{ url_for('static', filename='js/tailwind.config.js') }}"></script>

<!-- Font Awesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Great+Vibes&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">

<!-- Flatpickr CSS ve JS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/tr.js"></script>

<!-- Tailwind Config -->
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          'gold': '#C6A87D',
        },
        fontFamily: {
          'great-vibes': ['"Great Vibes"', 'cursive'],
          'poppins': ['Poppins', 'sans-serif'],
        },
      }
    }
  }
</script>

<!-- CSP Meta Tag -->
<meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-eval' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net;">

<!-- Dil değiştirme script'i -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mevcut dili al
    const currentLang = document.documentElement.lang || 'tr';
    
    // Çevirileri yükle
    fetch(`/api/translations/${currentLang}`)
        .then(response => response.json())
        .then(translations => {
            // data-translate attribute'u olan tüm elementleri bul
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[key]) {
                    // Eğer element bir input ise value'sunu, değilse içeriğini güncelle
                    if (element.tagName === 'INPUT') {
                        element.value = translations[key];
                    } else {
                        element.textContent = translations[key];
                    }
                }
            });
        })
        .catch(error => console.error('Translation error:', error));
});
</script>

<!-- Sayfa özel Schema.org işaretlemesi -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{{ settings.site_title or 'Zeppelin Hotel' }} - Ana Sayfa",
  "description": "{{ settings.site_description or 'Kapadokya bölgesinde lüks ve konforun buluştugu nokta. Eşsiz deneyimler için sizi bekliyoruz.' }}",
  "url": "{{ request.url }}",
  "speakable": {
    "@type": "SpeakableSpecification",
    "cssSelector": ["h1", "h2", "p"]
  }
}
</script>
{% endblock %}

{% block content %}
<!-- Banner Bölümü -->
<section id="banner" class="relative w-full overflow-hidden">
    <!-- SEO için H1 başlık (görsel olarak gizlenmiş) -->
    <h1 class="sr-only">{{ settings.site_title or 'Zeppelin Hotel' }} - {% if current_language == 'tr' %}Lüks ve Konforun Adresi{% else %}Luxury and Comfort{% endif %}</h1>
    
    <!-- Banner Slider -->
    <div class="relative h-[calc(100vw*9/16)] max-h-[1080px] min-h-[400px] overflow-hidden">
        {% if main_sliders %}
            {% for slider in main_sliders %}
            <div class="slider-item absolute inset-0 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %} transition-opacity duration-1000">
                {% if slider.file_type == 'image' %}
                    <img src="{{ url_for('static', filename='uploads/' + slider.file_path) }}" 
                         alt="<span data-translate='true'>{{ slider.title }}</span>"
                         width="1920" height="1080" loading="{% if loop.first %}eager{% else %}lazy{% endif %}"
                         class="w-full h-full object-cover">
                {% else %}
                    <video src="{{ url_for('static', filename='uploads/' + slider.file_path) }}" 
                           class="w-full h-full object-cover"
                           width="1920" height="1080" preload="metadata"
                           autoplay muted loop playsinline>
                        <span data-translate="true">Tarayıcınız video elementini desteklemiyor.</span>
                    </video>
                {% endif %}
                
                <!-- Slider İçeriği -->
                <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                    <div class="container mx-auto px-4">
                        <div class="max-w-2xl text-white {% if loop.index is odd %}ml-0{% else %}ml-auto text-right{% endif %}">
                            {% if slider.description %}
                            <span class="text-gold font-great-vibes text-4xl sm:text-5xl md:text-6xl" data-translate="true">{{ slider.description }}</span>
                            {% endif %}
                            
                            {% if slider.title %}
                            <h2 class="text-3xl sm:text-4xl md:text-6xl font-light mt-4 md:mt-6 leading-tight {% if loop.first %}hidden{% endif %}" data-translate="true">{{ slider.title }}</h2>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Slider Kontrolleri -->
            <div class="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
                {% for slider in main_sliders %}
                <button class="slider-dot w-3 h-3 rounded-full bg-white/30 hover:bg-white/50 transition-all duration-300 {% if loop.first %}!bg-white{% endif %}"
                        data-index="{{ loop.index0 }}"></button>
                {% endfor %}
            </div>
        {% endif %}
        
        <!-- Oda Arama Formu (Slider üzerinde) -->
        <div class="absolute bottom-40 md:bottom-40 bottom-6 left-0 right-0 z-10">
            <div class="container mx-auto px-4">
                <div class="bg-white/90 backdrop-blur-lg rounded-2xl md:rounded-full shadow-xl max-w-5xl mx-auto overflow-hidden">
                    <form action="{{ url_for('rooms.search_rooms') }}" method="GET" class="flex flex-row items-center">
                        <div class="w-1/4 md:w-auto flex-1 p-2 md:p-3 md:pl-6 border-r border-gray-200">
                            <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="check_in_date">Giriş Tarihi</label>
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-gold mx-1 md:mx-2"></i>
                                <input type="text" id="check_in" name="check_in" placeholder="Giriş" class="w-full bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700" required>
                            </div>
                        </div>
                        
                        <div class="w-1/4 md:w-auto flex-1 p-2 md:p-3 border-r border-gray-200">
                            <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="check_out_date">Çıkış Tarihi</label>
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-gold mx-1 md:mx-2"></i>
                                <input type="text" id="check_out" name="check_out" placeholder="Çıkış" class="w-full bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700" required>
                            </div>
                        </div>
                        
                        <div class="w-1/4 md:w-auto p-2 md:p-3 border-r border-gray-200">
                            <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="guests">Misafir Sayısı</label>
                            <div class="flex items-center">
                                <i class="fas fa-user-friends text-gold mx-1 md:mx-2"></i>
                                <select id="guests" name="guests" class="bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700 appearance-none pr-6 md:pr-8 w-full">
                                    {% for i in range(1, 11) %}
                                    <option value="{{ i }}">{{ i }} {% if current_language == 'tr' %}K{% else %}P{% endif %}</option>
                                    {% endfor %}
                                </select>
                                <div class="text-gold pointer-events-none absolute right-1/4 md:static transform -translate-x-4 md:transform-none">
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="w-1/4 md:w-auto p-2 md:p-2 text-center">
                            <button type="submit" class="w-full md:w-auto bg-gold hover:bg-gold/90 text-white font-medium py-2 md:py-3 px-2 md:px-8 rounded-full transition-all duration-300 flex items-center justify-center">
                                <i class="fas fa-search md:mr-2"></i>
                                <span class="hidden md:inline" data-translate="search_rooms">Oda Ara</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Odalar Bölümü -->
<section id="rooms" class="bg-[#E3DCD3] py-10 rounded-3xl mx-4 my-8">
    <div class="max-w-[1920px] mx-auto px-4">
        <!-- Başlık Alanı -->
        <div class="text-center mb-16">
            <span class="text-gold font-great-vibes text-5xl" data-translate="comfort_luxury">Konfor ve Lüks</span>
            <div class="flex items-center justify-center mt-6">
                <div class="h-[2px] w-12 bg-gold"></div>
                <div class="mx-4 text-gold text-2xl">⚜</div>
                <div class="h-[2px] w-12 bg-gold"></div>
            </div>
            <p class="mt-4 text-gray-600 max-w-xl mx-auto" data-translate="rooms_description">
                Eşsiz manzara ve konforun buluştuğu özel odalarımız
            </p>
        </div>

        <!-- Odalar Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6 px-2 lg:px-4">
            <!-- King Suite -->
            <div id="king-suite" data-category="king-suite" class="room-category bg-white rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                <a href="{{ url_for('rooms.rooms_by_category', slug='king-suite') }}" class="block relative w-full h-full">
                    <div class="relative overflow-hidden rounded-t-3xl">
                        <img src="{{ url_for('static', filename='images/king-suite.png') }}"
                             alt="King Suite"
                             class="w-full h-[320px] lg:h-[380px] object-cover">



                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="flex items-center justify-center space-x-4 text-white/80">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-bed text-gold"></i>
                                        <span class="text-sm" data-translate="king_bed">King Bed</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-users text-gold"></i>
                                        <span class="text-sm" data-translate="kisiler">4 Kişi</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-expand-arrows-alt text-gold"></i>
                                        <span class="text-sm" data-translate="area">45m²</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Oda Bilgileri -->
                    <div class="p-6 bg-white">
                        <div class="text-center">
                            <h3 class="text-2xl font-great-vibes text-gray-800 mb-3" data-translate="king_suite">King Suite</h3>
                            <p class="text-gray-600 text-sm mb-5" data-translate="king_suite_desc">Lüks ve konforun mükemmel birleşimi</p>

                            <!-- Özellikler -->
                            <div class="flex justify-center space-x-6 mb-5 text-gray-500">
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-wifi text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="wifi">Wi-Fi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-bath text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="jacuzzi">Jacuzzi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-couch text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="sofa">Sofa</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-glass-martini-alt text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="minibar">Minibar</span>
                                </div>
                            </div>

                            <!-- Detayları Gör Butonu -->
                            <div class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                                <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Queen Suite -->
            <div id="queen-suite" data-category="queen-suite" class="room-category bg-white rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                <a href="{{ url_for('rooms.rooms_by_category', slug='queen-suite') }}" class="block relative w-full h-full">
                    <div class="relative overflow-hidden rounded-t-3xl">
                        <img src="{{ url_for('static', filename='images/queen-suite.png') }}"
                             alt="Queen Suite"
                             class="w-full h-[320px] lg:h-[380px] object-cover">



                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="flex items-center justify-center space-x-4 text-white/80">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-bed text-gold"></i>
                                        <span class="text-sm" data-translate="queen_bed">Queen Bed</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-users text-gold"></i>
                                        <span class="text-sm" data-translate="kisiler3">4 Kişi</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-expand-arrows-alt text-gold"></i>
                                        <span class="text-sm" data-translate="area">45m²</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Oda Bilgileri -->
                    <div class="p-6 bg-white">
                        <div class="text-center">
                            <h3 class="text-2xl font-great-vibes text-gray-800 mb-3" data-translate="queen_suite">Queen Suite</h3>
                            <p class="text-gray-600 text-sm mb-5" data-translate="queen_suite_desc">Zarif tasarım ve modern konfor</p>

                            <!-- Özellikler -->
                            <div class="flex justify-center space-x-6 mb-5 text-gray-500">
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-wifi text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="wifi">Wi-Fi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-bath text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="jacuzzi">Jacuzzi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-couch text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="sofa">Sofa</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-glass-martini-alt text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="minibar">Minibar</span>
                                </div>
                            </div>

                            <!-- Detayları Gör Butonu -->
                            <div class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                                <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Junior Suite -->
            <div id="junior-suite" data-category="junior-suite" class="room-category bg-white rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                <a href="{{ url_for('rooms.rooms_by_category', slug='junior-suite') }}" class="block relative w-full h-full">
                    <div class="relative overflow-hidden rounded-t-3xl">
                        <img src="{{ url_for('static', filename='images/junior-suite.png') }}"
                             alt="Junior Suite"
                             class="w-full h-[320px] lg:h-[380px] object-cover">



                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="flex items-center justify-center space-x-4 text-white/80">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-bed text-gold"></i>
                                        <span class="text-sm" data-translate="double_bed">Double Bed</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-users text-gold"></i>
                                        <span class="text-sm" data-translate="kisiler3">2 Kişi</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-expand-arrows-alt text-gold"></i>
                                        <span class="text-sm" data-translate="area">35m²</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Oda Bilgileri -->
                    <div class="p-6 bg-white">
                        <div class="text-center">
                            <h3 class="text-2xl font-great-vibes text-gray-800 mb-3" data-translate="junior_suite">Junior Suite</h3>
                            <p class="text-gray-600 text-sm mb-5" data-translate="junior_suite_desc">Konforlu ve ferah yaşam alanı</p>

                            <!-- Özellikler -->
                            <div class="flex justify-center space-x-6 mb-5 text-gray-500">
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-wifi text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="wifi">Wi-Fi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-couch text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="sofa">Sofa</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-glass-martini-alt text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="minibar">Minibar</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-coffee text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="refreshments">Refreshments</span>
                                </div>
                            </div>

                            <!-- Detayları Gör Butonu -->
                            <div class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                                <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Standard Room -->
            <div id="standard-room" data-category="standart-rooms" class="room-category bg-white rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                <a href="{{ url_for('rooms.rooms_by_category', slug='standart-rooms') }}" class="block relative w-full h-full">
                    <div class="relative overflow-hidden rounded-t-3xl">
                        <img src="{{ url_for('static', filename='images/standart-rooms.png') }}"
                             alt="Standard Room"
                             class="w-full h-[320px] lg:h-[380px] object-cover">



                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="flex items-center justify-center space-x-4 text-white/80">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-bed text-gold"></i>
                                        <span class="text-sm" data-translate="twin_bed">Twin Bed</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-users text-gold"></i>
                                        <span class="text-sm" data-translate="kisiler2">2 Kişi</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-expand-arrows-alt text-gold"></i>
                                        <span class="text-sm" data-translate="area">25m²</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Oda Bilgileri -->
                    <div class="p-6 bg-white">
                        <div class="text-center">
                            <h3 class="text-2xl font-great-vibes text-gray-800 mb-3" data-translate="standart_room">Standart Oda</h3>
                            <p class="text-gray-600 text-sm mb-5" data-translate="standard_room_desc">Ekonomik ve konforlu konaklama</p>

                            <!-- Özellikler -->
                            <div class="flex justify-center space-x-6 mb-5 text-gray-500">
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-wifi text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="wifi">Wi-Fi</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-glass-martini-alt text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="minibar">Minibar</span>
                                </div>
                               
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-coffee text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="refreshments">Refreshments</span>
                                </div>
                                <div class="flex flex-col items-center group/icon">
                                    <i class="fas fa-pump-soap text-gold mb-1 text-base group-hover/icon:scale-110 transition-transform duration-300"></i>
                                    <span class="text-xs font-medium" data-translate="toiletries">Toiletries</span>
                                </div>
                            </div>

                            <!-- Detayları Gör Butonu -->
                            <div class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                                <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Ana Banner -->
{% set main_banners = banners|selectattr('category', 'equalto', 'main')|selectattr('active', 'equalto', true)|list %}
{% if main_banners %}
<!-- Başlık Alanı -->
<section class="bg-[#E3DCD3] py-5 rounded-3xl mx-4 my-8">
    <div class="container mx-auto px-4">
        <div class="text-center mb-5">
            <span class="text-gold font-great-vibes text-5xl" data-translate="atv_safari">Sizi Bekliyoruz</span>
            <div class="flex items-center justify-center mt-6">
                <div class="h-[2px] w-12 bg-gold"></div>
                <div class="mx-4 text-gold text-2xl">⚜</div>
                <div class="h-[2px] w-12 bg-gold"></div>
            </div>
        </div>
    </div>
</section>

<section class="relative w-full h-[60vh] min-h-[400px] overflow-hidden bg-black border-t-4 border-b-4 border-gold rounded-t-3xl rounded-b-3xl">
    {% for banner in main_banners %}
    <div class="banner-slide absolute inset-0 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %} transition-opacity duration-1000">
        {% if hasattr(banner, 'file_type') and banner.file_type == 'video' %}
            {% if hasattr(banner, 'video') and banner.video %}
            <video src="{{ url_for('static', filename='uploads/banners/' + banner.video) }}"
                   class="absolute inset-0 w-full h-full object-cover opacity-60"
                   autoplay muted loop playsinline>
                Tarayıcınız video elementini desteklemiyor.
            </video>
            {% elif hasattr(banner, 'video_url') and banner.video_url %}
            <video src="{{ banner.video_url }}"
                   class="absolute inset-0 w-full h-full object-cover opacity-60"
                   autoplay muted loop playsinline>
                Tarayıcınız video elementini desteklemiyor.
            </video>
            {% endif %}
        {% elif banner.image %}
        <img src="{{ url_for('static', filename='uploads/banners/' + banner.image) }}"
             alt="{{ banner.title }}"
             class="absolute inset-0 w-full h-full object-cover opacity-60">
        {% endif %}
        
        <div class="absolute inset-0 flex items-center">
            <div class="container mx-auto px-4">
                <div class="max-w-2xl {% if loop.index is odd %}ml-0{% else %}ml-auto text-right{% endif %}">
                    {% if banner.subtitle %}
                    <span class="text-gold font-great-vibes text-4xl md:text-5xl" data-translate="true">{{ banner.subtitle }}</span>
                    {% endif %}
                    
                    {% if banner.description %}
                    <p class="text-white text-lg md:text-xl mt-4" data-translate="true">{{ banner.description }}</p>
                    {% endif %}
                    
                    {% if banner.button_text and banner.button_link %}
                    <a href="{{ banner.button_link }}" 
                       class="inline-block mt-8 px-8 py-3 bg-gold text-white rounded-full hover:bg-gold/90 transition-all duration-300">
                        <span data-translate="true">{{ banner.button_text }}</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Banner Navigasyon Noktaları -->
    {% if main_banners|length > 1 %}
    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
        {% for banner in main_banners %}
        <button class="banner-dot w-3 h-3 rounded-full bg-white/50 hover:bg-white transition-colors duration-300 {% if loop.first %}active{% endif %}"
                data-index="{{ loop.index0 }}"></button>
        {% endfor %}
    </div>
    {% endif %}
</section>
{% endif %}

<!-- İkinci Banner -->
{% set secondary_banners = banners|selectattr('category', 'equalto', 'secondary')|selectattr('active', 'equalto', true)|list %}
{% if secondary_banners %}
<section class="relative w-full h-[60vh] min-h-[400px] overflow-hidden bg-black border-t-4 border-b-4 border-gold rounded-t-3xl rounded-b-3xl">
    {% for banner in secondary_banners %}
    <div class="banner-slide-secondary absolute inset-0 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %} transition-opacity duration-1000">
        {% if hasattr(banner, 'file_type') and banner.file_type == 'video' %}
            {% if hasattr(banner, 'video') and banner.video %}
            <video src="{{ url_for('static', filename='uploads/banners/' + banner.video) }}"
                   class="absolute inset-0 w-full h-full object-cover opacity-60"
                   autoplay muted loop playsinline>
                Tarayıcınız video elementini desteklemiyor.
            </video>
            {% elif hasattr(banner, 'video_url') and banner.video_url %}
            <video src="{{ banner.video_url }}"
                   class="absolute inset-0 w-full h-full object-cover opacity-60"
                   autoplay muted loop playsinline>
                Tarayıcınız video elementini desteklemiyor.
            </video>
            {% endif %}
        {% elif banner.image %}
        <img src="{{ url_for('static', filename='uploads/banners/' + banner.image) }}"
             alt="{{ banner.title }}"
             class="absolute inset-0 w-full h-full object-cover opacity-60">
        {% endif %}
        
        <div class="absolute inset-0 flex items-center">
            <div class="container mx-auto px-4">
                <div class="max-w-2xl {% if loop.index is odd %}ml-0{% else %}ml-auto text-right{% endif %}">
                    {% if banner.subtitle %}
                    <span class="text-gold font-great-vibes text-4xl md:text-5xl" data-translate="true">{{ banner.subtitle }}</span>
                    {% endif %}
                    
                    {% if banner.description %}
                    <p class="text-white text-lg md:text-xl mt-4" data-translate="true">{{ banner.description }}</p>
                    {% endif %}
                    
                    {% if banner.button_text and banner.button_link %}
                    <a href="{{ banner.button_link }}" 
                       class="inline-block mt-8 px-8 py-3 bg-gold text-white rounded-full">
                        <span data-translate="true">{{ banner.button_text }}</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Banner Navigasyon Noktaları -->
    {% if secondary_banners|length > 1 %}
    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
        {% for banner in secondary_banners %}
        <button class="banner-dot-secondary w-3 h-3 rounded-full bg-white/50"
                data-index="{{ loop.index0 }}"></button>
        {% endfor %}
    </div>
    {% endif %}
</section>
{% endif %}

<!-- Ana Sayfa İçeriği -->
{% if homepage_content %}
<section class="bg-[#E3DCD3] pb-10">
    <!-- İçerik - 15px kenar boşlukları -->
    <div class="w-full px-[15px]">
        {% if g.language == 'tr' %}
            {{ homepage_content.content_tr|safe }}
        {% else %}
            {{ homepage_content.content_en|safe }}
        {% endif %}
    </div>
</section>
{% endif %}

<!-- Aktiviteler Bölümü - Tam Ekran -->
<section id="activities" class="bg-[#E3DCD3] py-16">
    <div class="w-full px-6 lg:px-8">
        <!-- Başlık Alanı -->
        <div class="text-center mb-10">
            <span class="text-gold font-great-vibes text-5xl" data-translate="adventure_awaits">Macera Seni Bekliyor</span>
            <h2 class="text-4xl font-bold text-gray-900 mt-4" data-translate="activities_title">AKTİVİTELER</h2>
            <div class="flex items-center justify-center mt-6">
                <div class="h-[2px] w-12 bg-gold"></div>
                <div class="mx-4 text-gold text-2xl">⚜</div>
                <div class="h-[2px] w-12 bg-gold"></div>
            </div>
            <p class="mt-4 text-gray-600 max-w-xl mx-auto" data-translate="activities_description">
                Kapadokya'nın eşsiz güzelliklerini keşfetmek için unutulmaz aktiviteler
            </p>
        </div>

        <!-- Aktivite Kartları Swiper - Tam Ekran -->
        <div class="relative w-full">
            <!-- Swiper Container -->
            <div class="swiper activitiesSwiper w-full">
                <div class="swiper-wrapper">
                    {% for activity in activities %}
                    <div class="swiper-slide">
                        <div class="activity-card group bg-white rounded-3xl overflow-hidden hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                            <div class="relative overflow-hidden">
                                {% if activity.featured_image %}
                                <img src="{{ url_for('static', filename='uploads/activities/' + activity.featured_image) }}"
                                     alt="{{ activity.get_title(current_language) }}"
                                     class="w-full h-[240px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                                {% else %}
                                <div class="w-full h-[240px] bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
                                    <i class="fas fa-mountain text-gold text-3xl"></i>
                                </div>
                                {% endif %}

                                <!-- Overlay Gradient -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                                    <div class="absolute bottom-4 left-4 right-4">
                                        <div class="flex items-center justify-center space-x-4 text-white/80">
                                            {% if activity.duration %}
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-clock text-gold"></i>
                                                <span class="text-sm">{{ activity.duration }}</span>
                                            </div>
                                            {% endif %}
                                            {% if activity.difficulty %}
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-signal text-gold"></i>
                                                <span class="text-sm">{{ activity.get_difficulty_display() }}</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Aktivite Bilgileri -->
                            <div class="p-5 bg-white">
                                <div class="text-center">
                                    <h3 class="text-2xl font-great-vibes text-gray-800 mb-2">{{ activity.get_title(current_language) }}</h3>

                                    {% if activity.get_description(current_language) %}
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                        {{ activity.get_description(current_language)|truncate(80) }}
                                    </p>
                                    {% endif %}

                                    <!-- Detayları Gör Butonu -->
                                    <a href="{{ url_for('activities.activity_detail', slug=activity.slug) }}"
                                       class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2.5 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                                        <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                                        <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Navigation buttons -->
                <div class="swiper-button-next !text-gold !w-12 !h-12 !bg-white/90 !rounded-full !shadow-lg hover:!bg-white !transition-all !duration-300"></div>
                <div class="swiper-button-prev !text-gold !w-12 !h-12 !bg-white/90 !rounded-full !shadow-lg hover:!bg-white !transition-all !duration-300"></div>

                <!-- Pagination -->
                <div class="swiper-pagination !bottom-[-50px]"></div>
            </div>
        </div>

        <!-- Tüm Aktiviteler Butonu -->
        <div class="text-center mt-12">
            <a href="{{ url_for('activities.activity_index') }}"
               class="inline-flex items-center bg-gold hover:bg-gold/90 text-white px-8 py-4 rounded-full text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                <span data-translate="all_activities">Tüm Aktiviteler</span>
                <i class="fas fa-mountain ml-3 transform group-hover:scale-110 transition-transform duration-300"></i>
            </a>
        </div>
    </div>
</section>

<!-- Yemek Menüsü Bölümü -->
<section id="food-menu" class="bg-[#E3DCD3] py-10 rounded-3xl mx-4 my-8">
    <div class="max-w-[1920px] mx-auto px-4">
        <!-- Başlık Alanı -->
        <div class="text-center mb-16">
            <span class="text-gold font-great-vibes text-5xl" data-translate="culinary_journey">Lezzet Yolculuğu</span>
            <div class="flex items-center justify-center mt-6">
                <div class="h-[2px] w-12 bg-gold"></div>
                <div class="mx-4 text-gold text-2xl">⚜</div>
                <div class="h-[2px] w-12 bg-gold"></div>
            </div>
            <p class="mt-4 text-gray-600 max-w-xl mx-auto" data-translate="food_description">
                Şeflerimizin özenle hazırladığı eşsiz lezzetleri keşfedin
            </p>
        </div>

        <!-- Yemek Kartları Grid - 8 Yemek için 2 Satır -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 px-2 lg:px-4">
            {% set featured_foods = foods[:4] if foods else [] %}
            {% for food in featured_foods %}
            {% if food.active %}
            <div class="food-card group bg-white rounded-3xl overflow-hidden hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                <div class="relative overflow-hidden">
                    {% if food.image %}
                    <img src="{{ url_for('static', filename=food.image.replace('\\', '/')) }}"
                         alt="{{ food.name }}"
                         class="w-full h-[280px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                    {% else %}
                    <div class="w-full h-[280px] bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
                        <i class="fas fa-utensils text-gold text-4xl"></i>
                    </div>
                    {% endif %}

                    <!-- Overlay Gradient -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="flex items-center justify-center space-x-4 text-white/80">
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-clock text-gold"></i>
                                    <span class="text-sm">15-20 dk</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-star text-gold"></i>
                                    <span class="text-sm">Özel</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Yemek Bilgileri -->
                <div class="p-6 bg-white">
                    <div class="text-center">
                        <h3 class="text-xl font-great-vibes text-gray-800 mb-3">{{ food.name }}</h3>
                        {% if food.description %}
                        <p class="text-gray-600 text-sm mb-6 line-clamp-2">
                            {{ food.description|truncate(80) }}
                        </p>
                        {% endif %}

                        <!-- Detayları Gör Butonu -->
                        <div class="inline-flex items-center bg-gold/10 hover:bg-gold/20 px-4 py-2 rounded-full text-gold hover:text-gold/80 transition-all duration-300 group-hover:translate-x-1 border border-gold/20 hover:border-gold/40">
                            <span class="text-sm font-medium" data-translate="view_details">Detayları Gör</span>
                            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>

        <!-- Tüm Yemekler Butonu -->
        <div class="text-center mt-12">
            <a href="{{ url_for('food.menu') }}"
               class="inline-flex items-center bg-gold hover:bg-gold/90 text-white px-8 py-4 rounded-full text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl">
                <span data-translate="all_foods">Tüm Yemekler</span>
                <i class="fas fa-utensils ml-3 transform group-hover:scale-110 transition-transform duration-300"></i>
            </a>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="blog-section py-20 bg-gradient-to-b from-[#E3DCD3] to-[#F0F0F0] rounded-3xl mx-4 my-8">
    <div class="max-w-[1920px] mx-auto px-4">
        <!-- Başlık Bölümü -->
        <div class="text-center mb-20">
            <div class="inline-block">
                <span class="text-gold font-great-vibes text-6xl block mb-2">Blog</span>
                <div class="flex items-center justify-center">
                    <div class="h-[1px] w-16 bg-gradient-to-r from-transparent to-gold"></div>
                    <div class="mx-6 text-gold text-xl">✦</div>
                    <div class="h-[1px] w-16 bg-gradient-to-l from-transparent to-gold"></div>
                </div>
            </div>
            <p class="mt-8 text-gray-700 text-lg max-w-2xl mx-auto leading-relaxed" data-translate="blog_description">
                Kapadokya'nın büyülü dünyasından hikayeler, seyahat ipuçları ve unutulmaz deneyimler
            </p>
        </div>

        <!-- Blog Grid - 5 Blog Layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-6 px-2 lg:px-4">
            {% for post in latest_posts %}
            <article class="blog-card group cursor-pointer">
                <a href="{{ url_for('blog.blog_detail', slug=post.slug) }}" class="block">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-gold/20">
                        <!-- Blog Görseli -->
                        {% if post.featured_image %}
                        <div class="relative overflow-hidden">
                            <div class="aspect-[4/3] overflow-hidden">
                                <img src="{{ url_for('static', filename='uploads/blog/' + post.featured_image) }}"
                                     alt="{{ get_lang_text(post, 'title') }}"
                                     class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">
                            </div>

                            <!-- Gradient Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                            <!-- Tarih Badge -->
                            <div class="absolute bottom-4 left-4">
                                <div class="bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg shadow-sm">
                                    <div class="flex items-center space-x-2">
                                        <i class="far fa-calendar-alt text-gold text-sm"></i>
                                        <span class="text-gray-700 text-sm font-medium">
                                            {{ post.created_at.strftime('%d %b %Y') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Blog İçeriği -->
                        <div class="p-5 xl:p-6">
                            <!-- Kategori -->
                            <div class="mb-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gold/10 text-gold border border-gold/20">
                                    <i class="fas fa-tag mr-1"></i>
                                    Blog
                                </span>
                            </div>

                            <!-- Başlık -->
                            <h3 class="text-base xl:text-lg font-bold text-gray-900 mb-3 line-clamp-2 leading-tight group-hover:text-gold transition-colors duration-300">
                                {{ get_lang_text(post, 'title')|replace('Dunyasi', 'Dünyası') }}
                            </h3>

                            <!-- Özet -->
                            <p class="text-gray-600 text-sm xl:text-base leading-relaxed mb-4 line-clamp-2 xl:line-clamp-3">
                                {% if post.excerpt %}
                                    {{ get_lang_text(post, 'excerpt') }}
                                {% else %}
                                    {{ get_lang_text(post, 'content')|striptags|truncate(100) }}
                                {% endif %}
                            </p>

                            <!-- Alt Bilgiler -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                <!-- Meta Bilgiler -->
                                <div class="flex items-center space-x-2 xl:space-x-3">
                                    <div class="flex items-center text-gray-500">
                                        <i class="fas fa-clock text-gold mr-1"></i>
                                        <span class="text-xs xl:text-sm">5dk</span>
                                    </div>
                                    <div class="flex items-center text-gray-500">
                                        <i class="fas fa-eye text-gold mr-1"></i>
                                        <span class="text-xs xl:text-sm">Pop</span>
                                    </div>
                                </div>

                                <!-- Okuma Butonu -->
                                <div class="flex items-center text-gold font-medium group-hover:text-gold/80 transition-colors duration-300">
                                    <span class="text-xs xl:text-sm mr-1">
                                        {% if current_language == 'tr' %}
                                            Oku
                                        {% else %}
                                            Read
                                        {% endif %}
                                    </span>
                                    <i class="fas fa-arrow-right text-xs transform group-hover:translate-x-1 transition-transform duration-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </article>
            {% endfor %}
        </div>

        <!-- Tüm Blog Yazıları Butonu -->
        <div class="text-center mt-20">
            <div class="inline-block">
                <a href="{{ url_for('blog.blog_index') }}"
                   class="btn-blog-all group relative inline-flex items-center px-8 py-4 bg-white border-2 border-gold text-gold rounded-xl
                          hover:bg-gold hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl
                          font-medium text-base tracking-wide overflow-hidden">
                    <!-- Arka plan animasyonu -->
                    <div class="absolute inset-0 bg-gold transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>

                    <!-- İçerik -->
                    <div class="relative flex items-center">
                        <i class="fas fa-newspaper mr-3 transform group-hover:rotate-12 transition-transform duration-300"></i>
                        <span>
                            {% if current_language == 'tr' %}
                                Tüm Blog Yazıları
                            {% else %}
                                All Blog Posts
                            {% endif %}
                        </span>
                        <i class="fas fa-arrow-right ml-3 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>
            </div>

            <!-- Alt açıklama -->
            <p class="mt-4 text-gray-600 text-sm">
                {% if current_language == 'tr' %}
                    Daha fazla hikaye ve deneyim için
                {% else %}
                    For more stories and experiences
                {% endif %}
            </p>
        </div>
    </div>
</section>

<style>
/* Oda kartları için özel animasyonlar */
.room-category {
    position: relative;
    overflow: hidden;
}

.room-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(198, 168, 125, 0.1), transparent);
    transition: left 0.8s ease-in-out;
    z-index: 1;
}

.room-category:hover::before {
    left: 100%;
}

/* Resim hover animasyonu kaldırıldı - temiz görünüm */
.room-category img {
    transition: none !important;
    transform: none !important;
}

.room-category:hover img {
    transform: none !important;
    scale: 1 !important;
}

/* Base template'deki galeri efektini override et */
.room-category .gallery-item img,
.room-category .gallery-item:hover img {
    transform: none !important;
    transition: none !important;
}

/* Badge animasyonları */
.room-category .absolute.top-4.left-4 {
    transform: translateY(-10px);
    opacity: 0;
    animation: slideInDown 0.6s ease-out 0.3s forwards;
}

@keyframes slideInDown {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Özellik ikonları hover efekti */
.room-category .fas {
    transition: all 0.3s ease;
}

.room-category:hover .fas {
    transform: scale(1.1);
    color: #C6A87D;
}

/* Responsive iyileştirmeler */
@media (max-width: 1024px) {
    .room-category {
        margin-bottom: 1.5rem;
    }

    .room-category img {
        height: 320px !important;
    }

    .room-category .p-6 {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .room-category img {
        height: 280px !important;
    }

    .room-category .p-6 {
        padding: 1.25rem;
    }

    .room-category h3 {
        font-size: 1.75rem;
    }

    .room-category .space-x-6 {
        gap: 1rem;
    }
}

@media (max-width: 640px) {
    .room-category img {
        height: 250px !important;
    }

    .room-category .p-6 {
        padding: 1rem;
    }

    .room-category .space-x-6 {
        gap: 0.75rem;
    }

    .room-category .space-x-6 span {
        font-size: 0.625rem;
    }

    .room-category .space-x-6 i {
        font-size: 0.875rem;
    }
}

/* Smooth scroll için */
html {
    scroll-behavior: smooth;
}

/* Loading animasyonu */
.room-category img {
    transition: opacity 0.3s ease;
}

.room-category img[loading] {
    opacity: 0.7;
}

.room-category img:not([loading]) {
    opacity: 1;
}

/* Blog Section Professional Styles */
.blog-section {
    background: linear-gradient(135deg, #E3DCD3 0%, #F0F0F0 100%);
}

/* 15px yan boşluk için özel kurallar */
.blog-section .max-w-\[1920px\] {
    padding-left: 15px;
    padding-right: 15px;
}

/* Blog Card Hover Effects */
.blog-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center bottom;
}

.blog-card:hover {
    transform: translateY(-8px) scale(1.02);
}

/* Image Aspect Ratio */
.aspect-4\/3 {
    aspect-ratio: 4 / 3;
}

/* Line Clamp Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 3;
}

/* Food Section Styles */
.food-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center bottom;
}

.food-card:hover {
    transform: translateY(-8px) scale(1.02);
}

.food-card .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
}

/* Blog Responsive Design - 5 Blog Layout */
@media (min-width: 1536px) {
    /* 2XL screens: 5 columns */
    .blog-section .grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1280px) and (max-width: 1535px) {
    /* XL screens: 4 columns */
    .blog-section .grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) and (max-width: 1279px) {
    /* Large screens: 3 columns */
    .blog-section .grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .blog-section .p-8 {
        padding: 1.5rem;
    }

    .blog-section h3 {
        font-size: 1rem;
        line-height: 1.4;
    }

    .blog-section .text-base {
        font-size: 0.875rem;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    /* Medium screens: 2 columns */
    .blog-section .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .blog-section .p-8 {
        padding: 1.5rem;
    }
}

@media (max-width: 767px) {
    /* Small screens: 1 column */
    .blog-section .grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .blog-section .p-8 {
        padding: 1.25rem;
    }

    .blog-section .py-20 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .blog-section .mb-20 {
        margin-bottom: 2rem;
    }

    .blog-section .text-6xl {
        font-size: 2.5rem;
    }

    .blog-section .text-lg {
        font-size: 1rem;
    }
}

@media (max-width: 640px) {
    .blog-section .px-4 {
        padding-left: 15px;
        padding-right: 15px;
    }

    .blog-section .px-2 {
        padding-left: 8px;
        padding-right: 8px;
    }

    .blog-section .space-x-4 > * + * {
        margin-left: 0.75rem;
    }

    .blog-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

/* Button Animation */
.btn-blog-all {
    position: relative;
    overflow: hidden;
}

.btn-blog-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-blog-all:hover::before {
    left: 100%;
}

/* Swiper Activities Carousel Styles - Tam Ekran */
.activitiesSwiper {
    padding: 20px 0 60px 0;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin-left: 0;
}

.activitiesSwiper .swiper-wrapper {
    padding: 0 20px;
}

.activitiesSwiper .swiper-slide {
    height: auto; /* Let height be automatic */
    /* The explicit width property was removed to allow Swiper to control slide dimensions for proper looping. */
}

.activitiesSwiper .activity-card {
    width: 100%;
    min-width: 280px;
    max-width: 350px;
}

/* Hover efektleri */
.activitiesSwiper .swiper-slide:hover {
    transform: scale(1.05) translateY(-5px) !important;
}

/* Otomatik kayma sırasında smooth geçiş */
.activitiesSwiper.swiper-autoplay .swiper-slide {
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Mobil responsive ayarlar */
@media (max-width: 768px) {
    .activitiesSwiper {
        margin-left: calc(-50vw + 50%);
        width: 100vw;
        padding: 20px 0 60px 0;
    }

    .activitiesSwiper .swiper-wrapper {
        padding: 0 10px;
    }

    .activitiesSwiper .activity-card {
        width: 280px;
        min-width: 280px;
        max-width: 280px;
    }

    .activitiesSwiper .swiper-button-next,
    .activitiesSwiper .swiper-button-prev {
        width: 40px !important;
        height: 40px !important;
    }
}

/* Tablet responsive ayarlar */
@media (min-width: 769px) and (max-width: 1024px) {
    .activitiesSwiper .activity-card {
        width: 300px;
        min-width: 300px;
        max-width: 300px;
    }
}
</style>

<script>
// Swiper Activities Carousel - Basit ve Çalışan Versiyon
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Aktiviteler Swiper başlatılıyor...');

    // 1 saniye bekle (diğer scriptlerin yüklenmesi için)
    setTimeout(function() {
        try {
            // Swiper var mı kontrol et
            if (typeof Swiper === 'undefined') {
                console.error('❌ Swiper kütüphanesi bulunamadı!');
                return;
            }

            // Container var mı kontrol et
            const container = document.querySelector('.activitiesSwiper');
            if (!container) {
                console.error('❌ .activitiesSwiper container bulunamadı!');
                return;
            }

            // Slide sayısını kontrol et
            const slides = container.querySelectorAll('.swiper-slide');
            console.log(`📊 Toplam slide sayısı: ${slides.length}`);

            if (slides.length === 0) {
                console.warn('⚠️ Hiç slide bulunamadı!');
                return;
            }

            // Swiper'ı başlat - Manuel Sonsuz Döngü
            console.log(`🔧 Slide sayısı: ${slides.length} - Manuel sonsuz döngü kullanılacak`);

            const swiper = new Swiper('.activitiesSwiper', {
                loop: true,
                slidesPerView: 4, // Default slides per view
                spaceBetween: 30,
                autoplay: {
                    delay: 2500,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true, // Pause on hover
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    320: {
                        slidesPerView: 1,
                        spaceBetween: 20
                    },
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 25
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 30
                    },
                    1280: {
                        slidesPerView: 4,
                        spaceBetween: 30
                    },
                    1536: {
                        slidesPerView: 5,
                        spaceBetween: 30
                    },
                }
            });

            // Global erişim için
            window.activitiesSwiper = swiper;

            console.log('🎉 Aktiviteler carousel hazır!');

        } catch (error) {
            console.error('❌ Hata:', error);
        }
    }, 1000);
});
</script>

{% block scripts %}
{{ super() }}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Bugünün ve yarının tarihlerini al
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // Tarih formatını YYYY-MM-DD olarak ayarla
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        // Check-in ve check-out tarihlerini başlat
        const checkInPicker = flatpickr('#check_in', {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            locale: 'tr',
            defaultDate: formatDate(today),
            disableMobile: true,
            onChange: function(selectedDates, dateStr) {
                // Check-out tarihi en az check-in tarihinden bir gün sonra olmalı
                const nextDay = new Date(selectedDates[0]);
                nextDay.setDate(nextDay.getDate() + 1);
                checkOutPicker.set('minDate', nextDay);
                
                // Eğer check-out tarihi check-in tarihinden önceyse, check-out tarihini güncelle
                if (checkOutPicker.selectedDates[0] <= selectedDates[0]) {
                    checkOutPicker.setDate(nextDay);
                }
            }
        });
        
        const checkOutPicker = flatpickr('#check_out', {
            dateFormat: 'Y-m-d',
            minDate: formatDate(tomorrow),
            locale: 'tr',
            defaultDate: formatDate(tomorrow),
            disableMobile: true
        });
    });
</script>
{% endblock %}
{% endblock %}