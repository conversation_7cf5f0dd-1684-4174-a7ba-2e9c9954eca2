from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, abort, current_app, session
from models import db
from models.reservation import Reservation
from models.room import Room, RoomCategory
from models.setting import Setting
from models.payment import PaymentMethod, PaymentTransaction, PaymentStatus, PaymentProvider
from services.currency_service import CurrencyService, CurrencyRate
from services.pdf_service import PDFService
from datetime import datetime, date, timedelta
from flask_login import login_required, current_user
from sqlalchemy import and_, or_
import uuid
import json
import os

reservation_bp = Blueprint('reservation', __name__)

@reservation_bp.route('/admin/reservations/update-link', methods=['POST'])
@login_required
def update_link():
    try:
        reservation_link = request.form.get('reservation_link')
        setting = Setting.query.filter_by(key='reservation_link').first()
        
        if setting:
            setting.value = reservation_link
            if setting.click_count is None:
                setting.click_count = 0
        else:
            setting = Setting(
                key='reservation_link',
                value=reservation_link,
                type_='text',
                group='general',
                click_count=0
            )
            db.session.add(setting)
            
        db.session.commit()
        flash('Rezervasyon linki başarıyla güncellendi.', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('Rezervasyon linki güncellenirken bir hata oluştu.', 'error')
        print(f"Hata: {str(e)}")
    
    return redirect(url_for('reservation.reservation_list'))

@reservation_bp.route('/admin/reservations')
@login_required
def reservation_list():
    reservations = Reservation.query.order_by(Reservation.created_at.desc()).all()
    settings = {
        setting.key: setting for setting in Setting.query.all()
    }
    return render_template('admin/reservations/list.html', 
                         reservations=reservations,
                         settings=settings)

@reservation_bp.route('/admin/reservations/new', methods=['GET'])
@login_required
def new_reservation():
    room_id = request.args.get('room_id')
    date_str = request.args.get('date')
    
    room = None
    check_in = date.today().strftime('%Y-%m-%d')
    check_out = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    if room_id:
        room = Room.query.get(room_id)
    
    if date_str:
        try:
            check_in = datetime.strptime(date_str, '%Y-%m-%d').date().strftime('%Y-%m-%d')
            check_out = (datetime.strptime(date_str, '%Y-%m-%d').date() + timedelta(days=1)).strftime('%Y-%m-%d')
        except ValueError:
            pass
    
    # Tüm aktif odaları al
    rooms = Room.query.filter_by(status='active').all()
    
    # Ödeme yöntemlerini al
    payment_methods = PaymentMethod.query.filter_by(is_active=True).all()
    
    today = date.today().strftime('%Y-%m-%d')
    tomorrow = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    return render_template('admin/reservations/create.html', 
                          room=room,
                          rooms=rooms,
                          check_in=check_in,
                          check_out=check_out,
                          today=today,
                          tomorrow=tomorrow,
                          payment_methods=payment_methods)

@reservation_bp.route('/admin/reservations/create', methods=['POST'])
@login_required
def create_reservation():
    try:
        # Rezervasyon bilgilerini al
        name = request.form.get('name')
        surname = request.form.get('surname', '')
        email = request.form.get('email')
        phone = request.form.get('phone')
        check_in = datetime.strptime(request.form.get('check_in'), '%Y-%m-%d').date()
        check_out = datetime.strptime(request.form.get('check_out'), '%Y-%m-%d').date()
        guests = int(request.form.get('guests', 1))
        room_id = int(request.form.get('room_id'))
        status = request.form.get('status', 'pending')
        message = request.form.get('message', '')
        price = float(request.form.get('price', 0))
        currency = request.form.get('currency', 'EUR')
        
        # Oda bilgisini al
        room = Room.query.get_or_404(room_id)
        
        # Oda müsaitlik kontrolü
        if not is_room_available(room_id, check_in, check_out):
            flash('Seçilen tarih aralığında oda müsait değil!', 'error')
            return redirect(url_for('reservation.new_reservation'))
        
        # Yeni rezervasyon oluştur
        reservation = Reservation(
            name=name,
            surname=surname,
            email=email,
            phone=phone,
            check_in=check_in,
            check_out=check_out,
            guests=guests,
            room_type=room.category.name_tr if room.category else '',
            room_id=room_id,
            message=message,
            status=status,
            payment_status='not_paid',
            amount=price,
            currency=currency
        )
        
        # Ödeme işlemi yapılacak mı?
        collect_payment = request.form.get('collect_payment') == 'on'
        
        if collect_payment:
            payment_method_id = int(request.form.get('payment_method_id'))
            payment_method = PaymentMethod.query.get_or_404(payment_method_id)
            
            # Kart bilgilerini al (gerçek uygulamada bu bilgiler saklanmamalı!)
            card_holder = request.form.get('card_holder')
            card_number = request.form.get('card_number')
            expiry_date = request.form.get('expiry_date')
            cvv = request.form.get('cvv')
            
            # Ödeme işlemini başlat
            transaction = process_payment(
                payment_method=payment_method,
                amount=price,
                currency=currency,
                card_holder=card_holder,
                card_number=card_number,
                expiry_date=expiry_date,
                cvv=cvv
            )
            
            if transaction:
                reservation.payment_transaction_id = transaction.id
                
                if transaction.status == PaymentStatus.SUCCESS:
                    reservation.payment_status = 'paid'
                    reservation.status = 'confirmed'
                else:
                    reservation.payment_status = 'failed'
            else:
                flash('Ödeme işlemi başlatılamadı!', 'error')
                return redirect(url_for('reservation.new_reservation'))
        
        db.session.add(reservation)
        db.session.commit()
        
        flash('Rezervasyon başarıyla oluşturuldu!', 'success')
        return redirect(url_for('reservation.reservation_detail', id=reservation.id))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Rezervasyon oluşturulurken bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('reservation.new_reservation'))

@reservation_bp.route('/admin/reservations/<int:id>')
@login_required
def reservation_detail(id):
    reservation = Reservation.query.get_or_404(id)
    return render_template('admin/reservations/detail.html', reservation=reservation)

@reservation_bp.route('/admin/reservations/<int:id>/status', methods=['POST'])
@login_required
def update_status(id):
    reservation = Reservation.query.get_or_404(id)
    status = request.form.get('status')
    if status in ['pending', 'confirmed', 'cancelled', 'completed']:
        reservation.status = status
        db.session.commit()
        flash('Rezervasyon durumu güncellendi', 'success')
    return redirect(url_for('reservation.reservation_list'))

@reservation_bp.route('/make-reservation/<int:room_id>', methods=['GET', 'POST'])
def make_reservation(room_id):
    room = Room.query.filter_by(id=room_id, status='active').first_or_404()
    
    # Mevcut para birimlerini getir
    currencies = CurrencyRate.query.all()
    currency_dict = {
        'TRY': '₺',
        'USD': '$',
        'EUR': '€'
    }
    
    if request.method == 'POST':
        try:
            # Form verilerini al
            check_in = datetime.strptime(request.form.get('check_in'), '%Y-%m-%d').date()
            check_out = datetime.strptime(request.form.get('check_out'), '%Y-%m-%d').date()
            payment_method_id = request.form.get('payment_method_id', '1')  # Varsayılan olarak 1
            selected_currency = request.form.get('selected_currency', room.currency)  # Seçilen para birimi
            
            # Müsaitlik kontrolü
            if not room.is_available(check_in, check_out):
                flash('Seçilen tarih aralığında oda müsait değil!', 'error')
                return redirect(url_for('reservation.make_reservation', room_id=room_id))
                
            # Veriyi session'a kaydet ve ödeme sayfasına yönlendir
            session_data = {
                'room_id': room_id,
                'check_in': check_in.strftime('%Y-%m-%d'),
                'check_out': check_out.strftime('%Y-%m-%d'),
                'guests': int(request.form.get('guests', 1)),
                'payment_method_id': payment_method_id,  # Ödeme yöntemini ekle
                'selected_currency': selected_currency  # Seçilen para birimini ekle
            }
            
            # Flask session'ına kaydet
            session['reservation_data'] = session_data
            
            return redirect(url_for('reservation.reservation_checkout'))
            
        except Exception as e:
            flash(f'Rezervasyon oluşturulurken bir hata oluştu: {str(e)}', 'error')
    
    # GET isteği için
    today = date.today().strftime('%Y-%m-%d')
    tomorrow = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    # URL parametrelerinden tarih ve misafir bilgilerini al (varsa)
    check_in = request.args.get('check_in', today)
    check_out = request.args.get('check_out', tomorrow)
    guests = request.args.get('guests', 1, type=int)
    
    # Tarih formatını kontrol et
    try:
        check_in_date = datetime.strptime(check_in, '%Y-%m-%d').date()
        check_out_date = datetime.strptime(check_out, '%Y-%m-%d').date()
        
        # Geçmiş tarihler için bugünü kullan
        if check_in_date < date.today():
            check_in = today
            check_in_date = date.today()
            
        # Check-out tarihi check-in tarihinden önceyse düzelt
        if check_out_date <= check_in_date:
            check_out = (check_in_date + timedelta(days=1)).strftime('%Y-%m-%d')
    except ValueError:
        # Geçersiz tarih formatı durumunda varsayılan değerleri kullan
        check_in = today
        check_out = tomorrow
    
    # Ödeme yöntemlerini al
    payment_methods = PaymentMethod.query.filter_by(is_active=True).all()
    
    # Müsait olmayan tarihleri getir
    unavailable_dates = room.get_unavailable_dates()
    
    return render_template('reservation/make_reservation.html', 
                          room=room,
                          check_in=check_in,
                          check_out=check_out,
                          guests=guests,
                          payment_methods=payment_methods,
                          currencies=currencies,
                          currency_dict=currency_dict,
                          unavailable_dates=unavailable_dates,
                          today=today)

@reservation_bp.route('/reservation/checkout', methods=['GET', 'POST'], endpoint='reservation_checkout')
def reservation_checkout():
    # Session'dan verileri al
    reservation_data = session.get('reservation_data')
    
    if not reservation_data:
        flash('Rezervasyon bilgileri bulunamadı!', 'error')
        return redirect(url_for('rooms.room_index'))
    
    # Oda bilgilerini getir
    room = Room.query.get_or_404(reservation_data['room_id'])
    
    # Tarih bilgilerini çıkar
    check_in = datetime.strptime(reservation_data['check_in'], '%Y-%m-%d').date()
    check_out = datetime.strptime(reservation_data['check_out'], '%Y-%m-%d').date()
    
    # Gece sayısını hesapla
    nights = (check_out - check_in).days
    
    # Misafir sayısı
    guests = int(reservation_data['guests'])
    
    # Seçilen para birimi (form gönderildiğinde veya GET isteğinde)
    selected_currency = reservation_data.get('selected_currency', room.currency)
    
    # Fiyat hesapla (kişi sayısına göre)
    base_price = room.price
    if guests > 1:
        base_price += (room.price * 0.5 * (guests - 1))
    
    total_price = base_price * nights
    
    # Para birimi dönüşümü
    if selected_currency != room.currency:
        converted_price = CurrencyService.convert_currency(total_price, room.currency, selected_currency)
        if converted_price is not None:
            total_price = converted_price
        else:
            selected_currency = room.currency  # Dönüşüm başarısız olursa orijinal para birimini kullan
    
    # Ödeme yöntemlerini getir
    payment_methods = PaymentMethod.query.filter_by(is_active=True).all()
    
    # Önceden seçilen ödeme yöntemini al
    selected_payment_method_id = reservation_data.get('payment_method_id', None)
    
    # Para birimlerini getir
    currencies = CurrencyRate.query.all()
    rates = CurrencyService.get_latest_rates()
    
    if request.method == 'POST':
        try:
            # Kişisel bilgileri al
            name = request.form.get('first_name')  # first_name olarak değiştirildi
            surname = request.form.get('last_name')  # last_name olarak değiştirildi
            email = request.form.get('email')
            phone = request.form.get('phone')
            guests = int(reservation_data['guests'])
            country = request.form.get('country', '')  # Varsayılan boş string
            id_type = request.form.get('id_type', '')  # Varsayılan boş string
            id_number = request.form.get('id_number', '')  # Varsayılan boş string
            address = request.form.get('address', '')  # Varsayılan boş string
            special_requests = request.form.get('special_requests', '')
            currency = request.form.get('currency', selected_currency)  # Seçilen para birimini kullan
            
            # Seçilen para birimine göre fiyat dönüşümü yap
            original_amount = base_price * nights  # Orijinal para biriminde (genelde EUR)
            amount = original_amount
            
            if currency != room.currency:
                amount = CurrencyService.convert_currency(
                    original_amount, 
                    room.currency, 
                    currency
                )
                
                # Dönüşüm başarısız olursa orijinal fiyatı kullan
                if amount is None:
                    amount = original_amount
                    currency = room.currency
            
            # Ödeme yöntemi bilgilerini al
            payment_method_id = request.form.get('payment_method_id', selected_payment_method_id)
            if not payment_method_id:
                flash('Ödeme yöntemi seçilmedi!', 'error')
                return redirect(url_for('reservation.reservation_checkout'))
            
            # Geçerli bir ödeme yöntemi olduğundan emin ol (artık sadece 2 ve 3 var)
            if payment_method_id not in ['2', '3']:
                flash('Geçersiz ödeme yöntemi seçildi!', 'error')
                return redirect(url_for('reservation.reservation_checkout'))
                
            payment_method = PaymentMethod.query.get_or_404(payment_method_id)
            
            # Kart bilgilerini al
            card_holder = request.form.get('card_holder')
            card_number = request.form.get('card_number')
            expiry_date = request.form.get('expiry_date')
            cvv = request.form.get('cvv')
            
            # Kredi kartı seçilmişse kart bilgilerinin kontrolü
            if payment_method_id == '2':  # Kredi Kartı
                if not all([card_holder, card_number, expiry_date, cvv]):
                    flash('Lütfen kredi kartı bilgilerini eksiksiz doldurunuz!', 'error')
                    return redirect(url_for('reservation.reservation_checkout'))
            
            # Rezervasyon oluştur
            reservation = Reservation(
                name=name,
                surname=surname,
                email=email,
                phone=phone,
                check_in=check_in,
                check_out=check_out,
                guests=guests,
                room_type=room.category.name_tr if room.category else room.get_title(),
                room_id=room.id,
                special_requests=special_requests,
                country=country,
                id_type=id_type,
                id_number=id_number,
                address=address,
                status='pending',
                payment_status='processing',
                amount=amount,
                currency=currency,
                original_amount=original_amount,
                exchange_rate=rates.get(currency, {}).get('selling_rate') if currency != room.currency else 1.0
            )
            
            db.session.add(reservation)
            db.session.flush()  # ID oluşması için flush et
            
            # Ödeme işlemini başlat
            transaction = process_payment(
                payment_method=payment_method,
                amount=amount,
                currency=currency,
                card_holder=card_holder,
                card_number=card_number,
                expiry_date=expiry_date,
                cvv=cvv,
                reservation_id=reservation.id
            )
            
            if transaction:
                reservation.payment_transaction_id = transaction.id
                
                if transaction.status == PaymentStatus.SUCCESS:
                    reservation.payment_status = 'paid'
                    reservation.status = 'confirmed'
                    
                    # PDF oluştur
                    pdf_path = PDFService.get_reservation_pdf_path(reservation)
                    PDFService.create_reservation_pdf(reservation, pdf_path)
                    
                    db.session.commit()
                    
                    # Session'ı temizle
                    session.pop('reservation_data', None)
                    
                    # Başarı sayfasına yönlendir
                    return redirect(url_for('reservation.reservation_success', code=reservation.reservation_code))
                else:
                    reservation.payment_status = 'failed'
                    db.session.commit()
                    flash('Ödeme işlemi başarısız oldu!', 'error')
            else:
                reservation.payment_status = 'failed'
                db.session.commit()
                flash('Ödeme işlemi başlatılamadı!', 'error')
                
            # Hata durumunda checkout sayfasına yönlendir
            return redirect(url_for('reservation.reservation_checkout'))
            
        except Exception as e:
            import traceback
            db.session.rollback()
            error_traceback = traceback.format_exc()
            print(f"Hata detayları: {error_traceback}")
            flash(f'Ödeme işlemi sırasında bir hata oluştu: {str(e)}', 'error')
    
    # Geçici rezervasyon objesi oluştur (şablon için)
    reservation = Reservation(
        name="Geçici",
        surname="Rezervasyon",
        email="<EMAIL>",
        phone="0000000000",
        check_in=check_in,
        check_out=check_out,
        guests=guests,
        room_id=room.id,
        room_type=room.category.name_tr if room.category else "",
        currency=selected_currency,  # Seçilen para birimini kullan
        amount=total_price  # Dönüştürülmüş fiyatı kullan
    )
            
    # GET isteği için
    return render_template('reservation/checkout.html',
                         room=room,
                         check_in=check_in,
                         check_out=check_out,
                         nights=nights,
                         total_price=total_price,
                         guests=reservation_data['guests'],
                         payment_methods=payment_methods,
                         currencies=currencies,
                         selected_payment_method_id=selected_payment_method_id,
                         selected_currency=selected_currency,  # Seçilen para birimini şablona gönder
                         rates=rates,
                         reservation=reservation)

@reservation_bp.route('/reservation/success/<code>')
def reservation_success(code):
    # Rezervasyon bilgilerini getir
    reservation = Reservation.query.filter_by(reservation_code=code).first_or_404()
    
    # Sadece tamamlanmış rezervasyonlar için
    if reservation.status != 'confirmed' or reservation.payment_status != 'paid':
        abort(404)
    
    return render_template('reservation/success.html', reservation=reservation)

@reservation_bp.route('/reservation/download-pdf/<code>')
def download_reservation_pdf(code):
    # Rezervasyon bilgilerini getir
    reservation = Reservation.query.filter_by(reservation_code=code).first_or_404()
    
    # Sadece tamamlanmış rezervasyonlar için
    if reservation.status != 'confirmed' or reservation.payment_status != 'paid':
        abort(404)
    
    pdf_path = PDFService.get_reservation_pdf_path(reservation)
    
    # Check if PDF was successfully generated
    if not PDFService.is_pdf_generated(code) or not os.path.exists(pdf_path):
        # If PDF doesn't exist, try to create it
        result = PDFService.create_reservation_pdf(reservation, pdf_path)
        if not result or not os.path.exists(pdf_path):
            # If PDF generation fails, return an error
            return jsonify({
                'error': 'PDF oluşturulamadı. Lütfen daha sonra tekrar deneyin.',
                'type': 'PDFGenerationError'
            }), 500
    
    try:
        # PDF'i indir
        return send_file(pdf_path, 
                       mimetype='application/pdf',
                       as_attachment=True,
                       download_name=f"Reservation-{reservation.reservation_code}.pdf")
    except FileNotFoundError:
        return jsonify({
            'error': f'PDF dosyası bulunamadı: {pdf_path}',
            'type': 'FileNotFoundError'
        }), 404
    except Exception as e:
        return jsonify({
            'error': str(e),
            'type': type(e).__name__
        }), 500

@reservation_bp.route('/reservation/click', methods=['POST'])
def count_click():
    try:
        setting = Setting.query.filter_by(key='reservation_link').first()
        if setting:
            if setting.click_count is None:
                setting.click_count = 0
            setting.click_count += 1
            db.session.commit()
            return jsonify({
                'success': True, 
                'count': setting.click_count
            })
    except Exception as e:
        print(f"Click count error: {str(e)}")
        db.session.rollback()
    return jsonify({'success': False})

@reservation_bp.route('/reservation/check-availability', methods=['POST'])
def check_availability():
    """API endpoint to check room availability for specific dates"""
    room_id = request.form.get('room_id')
    check_in = request.form.get('check_in')
    check_out = request.form.get('check_out')
    
    if not all([room_id, check_in, check_out]):
        return jsonify({
            'available': False,
            'error': 'Missing parameters'
        })
    
    try:
        room = Room.query.get_or_404(int(room_id))
        check_in_date = datetime.strptime(check_in, '%Y-%m-%d').date()
        check_out_date = datetime.strptime(check_out, '%Y-%m-%d').date()
        
        is_available = room.is_available(check_in_date, check_out_date)
        
        return jsonify({
            'available': is_available,
            'room_id': room_id,
            'check_in': check_in,
            'check_out': check_out
        })
    except Exception as e:
        return jsonify({
            'available': False,
            'error': str(e)
        })

@reservation_bp.route('/reservation/get-price', methods=['GET'])
def get_price():
    """Para birimine göre fiyat bilgisini döndürür"""
    try:
        room_id = request.args.get('room_id', type=int)
        check_in = request.args.get('check_in')
        check_out = request.args.get('check_out')
        guests = request.args.get('guests', 1, type=int)
        currency = request.args.get('currency', 'EUR')
        
        if not room_id:
            return jsonify({'success': False, 'error': 'Oda ID gereklidir'})
            
        # Odayı bul
        room = Room.query.get_or_404(room_id)
        
        # Sadece para birimi dönüşümü yapılacaksa
        if not check_in or not check_out:
            # Temel fiyat (oda fiyatı)
            price = room.price
            
            # Para birimi dönüşümü
            if currency != room.currency:
                converted_price = CurrencyService.convert_currency(price, room.currency, currency)
                if converted_price is not None:
                    price = converted_price
                else:
                    currency = room.currency  # Dönüşüm başarısız olursa orijinal para birimini kullan
            
            # Formatlanmış fiyat
            if currency == 'TRY':
                formatted_price = f"{int(round(price))} ₺"
            elif currency == 'USD':
                formatted_price = f"${int(round(price))}"
            elif currency == 'EUR':
                formatted_price = f"€{int(round(price))}"
            else:
                formatted_price = f"{int(round(price))} {currency}"
                
            return jsonify({
                'success': True,
                'price': price,
                'formatted_price': formatted_price,
                'currency': currency
            })
        
        # Tarih kontrolü
        if check_in and check_out:
            try:
                check_in_date = datetime.strptime(check_in, '%Y-%m-%d').date()
                check_out_date = datetime.strptime(check_out, '%Y-%m-%d').date()
                
                # Gece sayısını hesapla
                nights = (check_out_date - check_in_date).days
                if nights <= 0:
                    nights = 1
            except ValueError:
                nights = 1
        else:
            nights = 1
        
        # Temel fiyat (oda fiyatı)
        base_price = room.price
        
        # Misafir sayısına göre fiyat ayarlaması
        # İlk kişi için tam fiyat, diğer kişiler için %50 ek ücret
        price = base_price
        if guests > 1:
            price += (base_price * 0.5 * (guests - 1))
        
        # Para birimi dönüşümü
        original_price = price
        original_currency = room.currency
        
        if currency != original_currency:
            converted_price = CurrencyService.convert_currency(price, original_currency, currency)
            if converted_price is not None:
                price = converted_price
            else:
                currency = original_currency  # Dönüşüm başarısız olursa orijinal para birimini kullan
        
        # Formatlanmış fiyat
        if currency == 'TRY':
            formatted_price = f"{int(round(price))} ₺"
        elif currency == 'USD':
            formatted_price = f"${int(round(price))}"
        elif currency == 'EUR':
            formatted_price = f"€{int(round(price))}"
        else:
            formatted_price = f"{int(round(price))} {currency}"
        
        # Diğer para birimlerindeki karşılıkları
        other_currencies = []
        currencies = CurrencyRate.query.all()
        for curr in currencies:
            if curr.currency_code != currency:
                other_price = CurrencyService.convert_currency(price, currency, curr.currency_code)
                if other_price is not None:
                    if curr.currency_code == 'TRY':
                        other_formatted = f"{int(round(other_price))} ₺"
                    elif curr.currency_code == 'USD':
                        other_formatted = f"${int(round(other_price))}"
                    elif curr.currency_code == 'EUR':
                        other_formatted = f"€{int(round(other_price))}"
                    else:
                        other_formatted = f"{int(round(other_price))} {curr.currency_code}"
                    
                    other_currencies.append(other_formatted)
        
        return jsonify({
            'success': True,
            'price': price,
            'formatted_price': formatted_price,
            'currency': currency,
            'nights': nights,
            'guests': guests,
            'other_currencies': ', '.join(other_currencies)
        })
        
    except Exception as e:
        import traceback
        print(f"Fiyat hesaplama hatası: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'success': False, 'error': str(e)})

@reservation_bp.route('/reservation/cancel/<code>', methods=['POST'])
def cancel_reservation(code):
    """Rezervasyon iptali için API endpoint'i"""
    try:
        # Rezervasyonu bul
        reservation = Reservation.query.filter_by(reservation_code=code).first_or_404()
        
        # İptal edilip edilemeyeceğini kontrol et
        if reservation.status == 'cancelled':
            return jsonify({
                'success': False,
                'message': 'Bu rezervasyon zaten iptal edilmiş.'
            }), 400
            
        # Eğer ödeme yapıldıysa, iptal etme (sadece ödenmemiş veya bekleyen rezervasyonlar iptal edilebilir)
        if reservation.payment_status == 'paid':
            return jsonify({
                'success': False,
                'message': 'Ödenmiş rezervasyonları iptal etmek için lütfen otel ile iletişime geçiniz.'
            }), 400
        
        # Rezervasyonu iptal et
        reservation.status = 'cancelled'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Rezervasyon başarıyla iptal edildi.',
            'code': reservation.reservation_code
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Rezervasyon iptal edilirken bir hata oluştu: {str(e)}'
        }), 500

@reservation_bp.route('/reservations', methods=['GET'])
def user_reservations():
    """Kullanıcı rezervasyonlarını listeler"""
    from datetime import timedelta
    
    page = request.args.get('page', 1, type=int)
    per_page = 6  # Her sayfada gösterilecek rezervasyon sayısı
    
    # Arama filtresi
    code = request.args.get('code', '')
    
    # Sorgu oluştur
    query = Reservation.query
    
    # Filtreleri uygula
    if code:
        query = query.filter(Reservation.reservation_code.like(f'%{code}%'))
    
    # Son eklenenden ilk eklenene sırala
    query = query.order_by(Reservation.created_at.desc())
    
    # Sayfalama
    pagination = query.paginate(page=page, per_page=per_page)
    reservations = pagination.items
    
    # Admin panelinde mi yoksa frontend'de mi gösterileceğini belirle
    if request.path.startswith('/admin') or current_user.is_authenticated:
        return render_template('admin/reservations/user_list.html', 
                             reservations=reservations,
                             pagination=pagination,
                             code=code,
                             timedelta=timedelta)
    else:
        return render_template('reservation/list.html', 
                             reservations=reservations,
                             pagination=pagination,
                             code=code,
                             timedelta=timedelta)


@reservation_bp.route('/payments', methods=['GET'])
def payment_list():
    """Ödeme işlemlerini listeler"""
    from datetime import timedelta
    
    page = request.args.get('page', 1, type=int)
    per_page = 10  # Her sayfada gösterilecek ödeme sayısı
    
    # Filtreler
    status = request.args.get('status', '')
    code = request.args.get('code', '')
    
    # Sorgu oluştur
    query = PaymentTransaction.query
    
    # Filtreleri uygula
    if status:
        if status == 'paid':
            query = query.filter(PaymentTransaction.status == PaymentStatus.SUCCESS)
        elif status == 'processing':
            query = query.filter(PaymentTransaction.status == PaymentStatus.PENDING)
        elif status == 'failed':
            query = query.filter(PaymentTransaction.status == PaymentStatus.FAILED)
        elif status == 'refunded':
            query = query.filter(PaymentTransaction.status == PaymentStatus.REFUNDED)
    
    # Kod ile filtrele (rezervasyon kodu veya işlem numarası)
    if code:
        # Alt sorgu ile rezervasyon kodunu bul
        reservations_with_code = db.session.query(Reservation.id).filter(
            Reservation.reservation_code.like(f'%{code}%')
        ).subquery()
        
        # İşlem numarası veya rezervasyon koduna göre filtrele
        query = query.filter(
            db.or_(
                PaymentTransaction.transaction_id.like(f'%{code}%'),
                PaymentTransaction.order_id.like(f'%{code}%'),
                PaymentTransaction.reservation_id.in_(reservations_with_code)
            )
        )
    
    # Son eklenenden ilk eklenene sırala
    query = query.order_by(PaymentTransaction.created_at.desc())
    
    # Sayfalama
    pagination = query.paginate(page=page, per_page=per_page)
    payments = pagination.items
    
    # Admin panelinde mi yoksa frontend'de mi gösterileceğini belirle
    if request.path.startswith('/admin') or current_user.is_authenticated:
        return render_template('admin/reservations/payments.html',
                             payments=payments,
                             pagination=pagination,
                             status=status,
                             code=code,
                             timedelta=timedelta)
    else:
        return render_template('reservation/payments.html',
                             payments=payments,
                             pagination=pagination,
                             status=status,
                             code=code,
                             timedelta=timedelta)

@reservation_bp.route('/admin/reservations/user-list')
@login_required
def admin_user_reservations():
    """Admin paneli için kullanıcı rezervasyonları listesi"""
    return user_reservations()

@reservation_bp.route('/admin/reservations/payments')
@login_required
def admin_payment_list():
    """Admin paneli için ödeme işlemleri listesi"""
    return payment_list()

# Yardımcı fonksiyonlar
def is_room_available(room_id, check_in, check_out):
    """
    Belirtilen tarih aralığında oda müsait mi kontrol eder
    """
    room = Room.query.get(room_id)
    if room:
        return room.is_available(check_in, check_out)
    return False

def process_payment(payment_method, amount, currency='TRY', card_holder=None, card_number=None, expiry_date=None, cvv=None, reservation_id=None):
    """
    Ödeme işlemi simülasyonu. Gerçek bir uygulamada, burada bir ödeme sağlayıcısına bağlanılarak işlem yapılır.
    """
    # Ödeme işlemi ID'si
    order_id = f"ORD-{uuid.uuid4().hex[:8].upper()}"
    
    # İşlem ID'si simülasyonu
    transaction_id = f"TR-{uuid.uuid4().hex[:10].upper()}"
    
    # Gerçek uygulamada burada bir ödeme entegrasyonu olacak
    # Bu örnekte başarılı bir ödeme simüle ediyoruz
    
    # Yeni ödeme işlemi kaydı
    transaction = PaymentTransaction(
        payment_method_id=payment_method.id,
        order_id=order_id,
        transaction_id=transaction_id,
        amount=amount,
        currency=currency or payment_method.currency or 'TRY',
        status=PaymentStatus.SUCCESS,
        installment=1,
        response_data={
            'message': 'Ödeme simülasyonu başarılı',
            'card_info': {
                'holder': card_holder,
                'number': card_number[-4:] if card_number else None,
                'expiry': expiry_date
            }
        }
    )
    
    db.session.add(transaction)
    return transaction 