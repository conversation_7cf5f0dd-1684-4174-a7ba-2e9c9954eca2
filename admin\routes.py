from flask import Blueprint, render_template, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from models import db
from models.room import Room
from models.blog import BlogPost
from models.food import Food, Order
from models.setting import get_settings
from models.reservation import Reservation
from models.activity import Activity
from models.user import User
from models.gallery import Gallery
from models.slider import Slider
from models.banner import Banner
from models.sponsor import Sponsor
from models.promotion import Promotion
from models.menu import Menu
from models.page import Page
from models.language import Language
from models.seo import SEO
from datetime import datetime, date, timedelta
from utils.helpers import get_turkey_time

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard')
@login_required
def dashboard():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('main.index'))

    try:
        # Son 7 günün rezervasyon verilerini çek
        reservations_last_7_days = db.session.query(
            db.func.date(Reservation.created_at),
            db.func.count(Reservation.id)
        ).filter(
            Reservation.created_at >= get_turkey_time() - timedelta(days=7)
        ).group_by(
            db.func.date(Reservation.created_at)
        ).order_by(
            db.func.date(Reservation.created_at)
        ).all()

        # Veriyi Chart.js için hazırla
        last_7_days_data = []
        date_map = {}
        
        # Tarih nesnesi veya string olabilir, kontrol edelim
        for date_val, count in reservations_last_7_days:
            if hasattr(date_val, 'strftime'):
                date_str = date_val.strftime('%Y-%m-%d')
            else:
                # Eğer zaten string ise doğrudan kullan
                date_str = str(date_val)
            date_map[date_str] = count
            
        for i in range(6, -1, -1): # Son 7 günü bugünden geriye doğru sırala
            day = get_turkey_time() - timedelta(days=i)
            date_str = day.strftime('%Y-%m-%d')
            last_7_days_data.append({
                'date': day.strftime('%b %d'),
                'count': date_map.get(date_str, 0)
            })

        # Tüm istatistikleri tek bir yerde topla
        stats = {
            'rooms': {
                'total': Room.query.count(),
                'active': Room.query.filter_by(status='active').count(),
            },
            'reservations': {
                'total': Reservation.query.count(),
                'pending': Reservation.query.filter_by(status='pending').count(),
                'confirmed': Reservation.query.filter_by(status='confirmed').count(),
                'last_7_days': last_7_days_data,
            },
            'posts': {
                'total': BlogPost.query.count(),
                'published': BlogPost.query.filter_by(status='published').count(),
            },
            'users': {
                'total': User.query.count(),
                'admins': User.query.filter_by(is_admin=True).count(),
            },
            'activities': {
                'total': Activity.query.count(),
                'active': Activity.query.filter_by(status='active').count(),
            },
            'media': {
                'galleries': Gallery.query.count(),
                'sliders': Slider.query.count(),
                'banners': Banner.query.count(),
            },
            'sponsors': {
                'total': Sponsor.query.count(),
                'active': Sponsor.query.filter_by(active=True).count(),
            },
            'promotions': {
                'total': Promotion.query.count(),
                'active': Promotion.query.filter_by(is_active=True).count(),
            },
            'food': {
                'total': Food.query.count(),
                'active': Food.query.filter_by(active=True).count(),
            },
            'menu': {
                'total': Menu.query.count(),
            },
            'pages': {
                'total': Page.query.count(),
                'published': Page.query.filter_by(status='published').count(),
            },
            'languages': {
                'total': Language.query.count(),
            },
            'seo': {
                'total': SEO.query.count(),
            }
        }

        # Son aktiviteleri hazırla (örnek veriler, burayı log sisteminize bağlayabilirsiniz)
        recent_activities = [
            {'message': '<strong>Yeni Rezervasyon:</strong> Ahmet Yılmaz - Standart Oda', 'timestamp': get_turkey_time() - timedelta(minutes=15), 'icon': 'fa-calendar-plus', 'color': 'teal'},
            {'message': '<strong>Yeni Blog Yazısı:</strong> "Yaz Tatili İçin 5 Öneri" yayınlandı', 'timestamp': get_turkey_time() - timedelta(hours=2), 'icon': 'fa-feather-alt', 'color': 'green'},
            {'message': '<strong>Kullanıcı Kaydı:</strong> Zeynep Kaya sisteme katıldı.', 'timestamp': get_turkey_time() - timedelta(hours=5), 'icon': 'fa-user-plus', 'color': 'purple'},
            {'message': '<strong>Oda Durumu Güncellendi:</strong> Kral Dairesi temizlendi.', 'timestamp': get_turkey_time() - timedelta(days=1), 'icon': 'fa-bed', 'color': 'blue'},
        ]

        return render_template('admin/dashboard.html',
                               stats=stats, 
                               recent_activities=recent_activities,
                               settings=get_settings())
                            
    except Exception as e:
        current_app.logger.error(f"Dashboard Error: {str(e)}", exc_info=True)
        flash('Kontrol paneli yüklenirken kritik bir hata oluştu. Lütfen kayıtları kontrol edin.', 'danger')
        
        # Boş bir sözlük olarak stats gönder (None olarak değil)
        empty_stats = {
            'rooms': {'total': 0, 'active': 0},
            'reservations': {'total': 0, 'pending': 0, 'confirmed': 0, 'last_7_days': []},
            'posts': {'total': 0, 'published': 0},
            'users': {'total': 0, 'admins': 0},
            'activities': {'total': 0, 'active': 0},
            'media': {'galleries': 0, 'sliders': 0, 'banners': 0},
            'sponsors': {'total': 0, 'active': 0},
            'promotions': {'total': 0, 'active': 0},
            'food': {'total': 0, 'active': 0},
            'menu': {'total': 0},
            'pages': {'total': 0, 'published': 0},
            'languages': {'total': 0},
            'seo': {'total': 0}
        }
        
        return render_template('admin/dashboard.html', error=True, stats=empty_stats, settings=get_settings())

# Diğer admin route'ları buraya eklenebilir... 