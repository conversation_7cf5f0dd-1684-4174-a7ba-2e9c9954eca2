{% extends "admin/base.html" %}

{% block breadcrumb %}Entegrasyon / Sanal POS Sistemleri{% endblock %}
{% block page_title %}Sanal POS Sistemleri{% endblock %}
{% block page_subtitle %}Sanal POS entegrasyonlarını yapılandırın{% endblock %}

<!-- Macro tanımlamaları -->
{% macro form_input(label, name, value, type="text", placeholder="") %}
<div>
    <label class="form-label">{{ label }}</label>
    <input type="{{ type }}" 
           name="{{ name }}" 
           value="{{ value }}"
           placeholder="{{ placeholder }}"
           class="form-input">
</div>
{% endmacro %}

{% macro action_button(text, icon, type="submit", style="primary", name="", value="") %}
<button type="{{ type }}" 
        {% if name %}name="{{ name }}"{% endif %}
        {% if value %}value="{{ value }}"{% endif %}
        class="btn btn-{{ style }}">
    <i class="{{ icon }} mr-2"></i>
    {{ text }}
</button>
{% endmacro %}

{% macro payment_card(title, status=false, test_mode=false) %}
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
    <div class="p-6">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 flex items-center justify-center bg-gray-50 rounded-lg">
                    <i class="fas fa-credit-card text-2xl text-gray-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
                    {% if caller %}
                        {{ caller() }}
                    {% endif %}
                </div>
            </div>
            {% if status %}
            <div class="flex items-center space-x-2">
                <span class="status-badge {{ 'status-badge-active' if status else 'status-badge-inactive' }}">
                    {{ 'Aktif' if status else 'Pasif' }}
                </span>
                {% if test_mode is not none %}
                <span class="status-badge {{ 'status-badge-test' if test_mode else 'status-badge-prod' }}">
                    {{ 'Test' if test_mode else 'Prod' }}
                </span>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {{ caller() if caller }}
    </div>
</div>
{% endmacro %}

{% block admin_content %}
<div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto">
        <!-- Başlık -->
        <div class="mb-8">
            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 flex items-center justify-center bg-blue-50 rounded-lg">
                        <i class="fas fa-credit-card text-2xl text-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Sanal POS Sistemleri</h2>
                        <p class="text-gray-600">VakıfBank ve iyzico sanal POS entegrasyonları</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sanal POS Kartları -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- VakıfBank -->
                {% call payment_card('VakıfBank', vakifbank.is_active, vakifbank.is_test_mode) %}
                    <form method="POST" action="{{ url_for('integration.update_vakifbank') }}" class="space-y-6">
                        <!-- Temel Bilgiler -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="form-label">Müşteri Numarası</label>
                                <input type="text" name="merchant_id" value="{{ vakifbank.merchant_id }}"
                                       class="form-input" placeholder="Müşteri numaranız">
                            </div>
                            <div>
                                <label class="form-label">Terminal ID</label>
                                <input type="text" name="terminal_id" value="{{ vakifbank.terminal_id }}"
                                       class="form-input" placeholder="Terminal ID">
                            </div>
                        </div>

                        <!-- Güvenlik Bilgileri -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="form-label">Terminal Kullanıcı</label>
                                <input type="text" name="terminal_user" value="{{ vakifbank.terminal_user }}"
                                       class="form-input" placeholder="Terminal kullanıcı adı">
                            </div>
                            <div>
                                <label class="form-label">Terminal Şifre</label>
                                <input type="password" name="terminal_pass" value="{{ vakifbank.terminal_pass }}"
                                       class="form-input" placeholder="Terminal şifresi">
                            </div>
                        </div>

                        <!-- 3D Secure Ayarları -->
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">3D Secure Ayarları</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="form-label">3D Secure URL</label>
                                    <input type="text" name="secure3d_url" value="{{ vakifbank.secure3d_url }}"
                                           class="form-input" placeholder="3D Secure URL">
                                </div>
                                <div>
                                    <label class="form-label">Başarılı İşlem URL</label>
                                    <input type="text" name="success_url" value="{{ vakifbank.success_url }}"
                                           class="form-input" placeholder="Başarılı işlem yönlendirme URL">
                                </div>
                            </div>
                        </div>

                        <!-- Test Modu ve Durum -->
                        <div class="flex items-center space-x-6">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="is_test_mode" 
                                       {% if vakifbank.is_test_mode %}checked{% endif %}
                                       class="rounded border-gray-300 text-blue-600">
                                <span class="text-sm text-gray-700">Test Modu</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="is_active"
                                       {% if vakifbank.is_active %}checked{% endif %}
                                       class="rounded border-gray-300 text-blue-600">
                                <span class="text-sm text-gray-700">Aktif</span>
                            </label>
                        </div>

                        <!-- Butonlar -->
                        <div class="flex justify-end space-x-3">
                            <button type="submit" name="test_connection" value="1"
                                    class="btn btn-secondary">
                                <i class="fas fa-plug mr-2"></i>
                                Bağlantıyı Test Et
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Kaydet
                            </button>
                        </div>
                    </form>
                {% endcall %}

                <!-- iyzico -->
                {% call payment_card('iyzico', iyzico.is_active, iyzico.is_test_mode) %}
                    <form method="POST" action="{{ url_for('integration.update_iyzico') }}" class="space-y-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="form-label">API Key</label>
                                <input type="text" name="api_key" value="{{ iyzico.iyzico_api_key }}"
                                       class="form-input">
                            </div>
                            <div>
                                <label class="form-label">Secret Key</label>
                                <input type="password" name="secret_key" value="{{ iyzico.iyzico_secret_key }}"
                                       class="form-input">
                            </div>
                        </div>

                        <!-- Taksit Seçenekleri -->
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">Taksit Seçenekleri</h4>
                            <div class="grid grid-cols-3 gap-4">
                                {% for i in [2,3,6,9,12] %}
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" name="installments" value="{{ i }}"
                                           {% if iyzico and iyzico.installment_options and i in iyzico.installment_options %}checked{% endif %}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="text-sm text-gray-700">{{ i }} Taksit</span>
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="submit" name="test_connection" value="1"
                                    class="btn btn-primary">
                                Bağlantıyı Test Et
                            </button>
                            <button type="submit"
                                    class="btn btn-secondary">
                                Kaydet
                            </button>
                        </div>
                    </form>
                {% endcall %}
        </div>
    </div>
</div>

<!-- Önce macro tanımlaması -->
{% macro payment_logo(name) %}
{% set logos = {
    'vakifbank': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#004B93" d="M40.2 10h119.6c16.6 0 30 13.4 30 30v0c0 16.6-13.4 30-30 30H40.2c-16.6 0-30-13.4-30-30v0c0-16.6 13.4-30 30-30z"/><path fill="#FFF" d="M67.7 45.8V25.4h4.8v20.4h-4.8zm24.1 0V25.4h4.8v20.4h-4.8zm-12 0V25.4h4.8v20.4h-4.8z"/></svg>',
    'iyzico': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#FF6B6B" d="M39.7 15.1h120.6c13.8 0 25 11.2 25 25s-11.2 25-25 25H39.7c-13.8 0-25-11.2-25-25s11.2-25 25-25z"/><path fill="#FFF" d="M76.4 43.8V27.4h5v16.4h-5zm20 0V27.4h5v16.4h-5zm-10 0V27.4h5v16.4h-5z"/></svg>',
    'paypal': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#003087" d="M46.2 12h107.6c18.9 0 34.2 15.3 34.2 34.2v0c0 18.9-15.3 34.2-34.2 34.2H46.2C27.3 80.4 12 65.1 12 46.2v0C12 27.3 27.3 12 46.2 12z"/><path fill="#FFF" d="M46.2 12h107.6c18.9 0 34.2 15.3 34.2 34.2v0c0 18.9-15.3 34.2-34.2 34.2H46.2C27.3 80.4 12 65.1 12 46.2v0C12 27.3 27.3 12 46.2 12z"/></svg>',
    'stripe': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#635BFF" d="M40 10h120c16.6 0 30 13.4 30 30s-13.4 30-30 30H40c-16.6 0-30-13.4-30-30s13.4-30 30-30z"/><path fill="#FFF" d="M89.1 34.5c0-3.8-1.8-6.8-5.2-6.8s-5.2 3-5.2 6.8c0 4.5 2.3 6.8 5.6 6.8 1.6 0 2.8-.4 3.7-.9v-2.8c-.9.4-1.9.7-3.2.7-1.3 0-2.4-.5-2.6-2.2h6.8c0-.2.1-1.1.1-1.6zm-6.9-1.4c0-1.7.9-2.4 1.8-2.4s1.7.7 1.7 2.4h-3.5z"/></svg>',
    'binance': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#F3BA2F" d="M35.4 11.8L50 26.4l5.4-5.4-20-20-20 20 5.4 5.4 14.6-14.6zM10.8 36.4l5.4-5.4-5.4-5.4-5.4 5.4 5.4 5.4zm24.6 0l14.6-14.6-5.4-5.4L29.2 31.8l-14.6-14.6-5.4 5.4 20 20 5.4-5.4z"/></svg>',
    'bitcoin': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50"><path fill="#F7931A" d="M98.2 39.1c1.2-7.9-4.8-12.2-13.1-15l2.7-10.8-6.6-1.6-2.6 10.5-5.3-1.3 2.6-10.5-6.6-1.6-2.7 10.8c-1.4-.3-2.7-.7-4.1-1l-9.1-2.3-1.8 7.1s4.9 1.1 4.8 1.2c2.7.7 3.2 2.4 3.1 3.8l-3.1 12.5c.2 0 .*******-.2-.1-.4-.1-.7-.2l-4.3 17.4c-.3 .8-1.2 2-3.1 1.5.1.1-4.8-1.2-4.8-1.2l-3.3 7.6 8.6 2.1 4.7 1.2-2.7 10.9 6.6 1.6 2.7-10.8 5.3 1.3-2.7 10.8 6.6 1.6 2.7-10.9c11.1 2.1 19.5.8 23-8.8 2.8-7.8-.1-12.3-6-15.3 4.3-1 7.5-3.8 8.4-9.6zm-15 20.9c-2 7.9-15.4 3.6-19.7 2.6l3.5-14.1c4.3 1.1 18.3 3.2 16.2 11.5zm2-20.6c-1.8 7.3-13.1 3.6-16.8 2.7l3.2-12.8c3.7.9 15.6 2.6 13.6 10.1z"/></svg>'
} %}
<div class="payment-logo-container">
    {% if name in logos %}
        {{ logos[name] | safe }}
    {% else %}
        <svg class="default-logo" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 50">
            <rect width="200" height="50" fill="#f0f0f0"/>
            <text x="100" y="30" text-anchor="middle" fill="#666">{{ name }}</text>
        </svg>
    {% endif %}
</div>
{% endmacro %}

<!-- Sonra style tag'i -->
<style>
/* Sanal POS Kartları */

/* Form elementleri için ortak stiller */
.form-input {
    margin-top: 0.25rem;
    display: block;
    width: 100%;
    border-radius: 0.5rem;
    border: 1px solid rgb(209, 213, 219);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    color: rgb(17, 24, 39);
}

.form-input:focus {
    outline: none;
    border-color: rgb(59, 130, 246);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(55, 65, 81);
    margin-bottom: 0.25rem;
}

.form-select {
    margin-top: 0.25rem;
    display: block;
    width: 100%;
    border-radius: 0.5rem;
    border: 1px solid rgb(209, 213, 219);
    padding: 0.5rem 0.75rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Buton stilleri */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 150ms ease-in-out;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
    background-color: rgb(37, 99, 235);
    color: white;
}

.btn-primary:hover {
    background-color: rgb(29, 78, 216);
}

.btn-secondary {
    background-color: white;
    color: rgb(55, 65, 81);
    border: 1px solid rgb(209, 213, 219);
}

.btn-secondary:hover {
    background-color: rgb(249, 250, 251);
}

/* Checkbox stilleri */
.form-checkbox {
    border-radius: 0.25rem;
    border: 1px solid rgb(209, 213, 219);
    width: 1rem;
    height: 1rem;
}

.form-checkbox:checked {
    background-color: rgb(37, 99, 235);
    border-color: rgb(37, 99, 235);
}

/* Form grup stilleri */
.form-group {
    margin-bottom: 1rem;
}

.form-section {
    background-color: white;
    border-radius: 0.75rem;
    border: 1px solid rgb(229, 231, 235);
    padding: 1.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.form-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: rgb(17, 24, 39);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
}

@media (min-width: 768px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}



/* Status badge stilleri */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge-active {
    background-color: rgb(220, 252, 231);
    color: rgb(22, 101, 52);
}

.status-badge-inactive {
    background-color: rgb(254, 226, 226);
    color: rgb(153, 27, 27);
}

.status-badge-test {
    background-color: rgb(254, 249, 195);
    color: rgb(133, 77, 14);
}

.status-badge-prod {
    background-color: rgb(219, 234, 254);
    color: rgb(30, 64, 175);
}

/* Kart stilleri */
.payment-card {
    background-color: white;
    border-radius: 0.75rem;
    border: 1px solid rgb(229, 231, 235);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: box-shadow 0.3s;
}

.payment-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Logo container */
.logo-container {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(249, 250, 251);
    border-radius: 0.5rem;
}

.logo-container img {
    height: 2rem;
    width: auto;
    object-fit: contain;
}

/* Logo stilleri */
.payment-logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgb(229, 231, 235);
    width: 120px;
    height: 40px;
}

.payment-logo-container svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Default logo stili */
.default-logo {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    border-radius: 4px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validasyonları ve diğer JavaScript işlemleri burada yapılabilir
    console.log('Sanal POS ayarları sayfası yüklendi');
});
</script>
{% endblock %} 