{% extends "base.html" %}

{% block title %}{{ _('Rezervasyon Başarılı') }}{% endblock title %}

{% block head %}
<style>
    .checkmark-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #C6A87D;
        stroke-miterlimit: 10;
        margin: 10% auto;
        box-shadow: inset 0px 0px 0px #C6A87D;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        stroke-width: 2;
        stroke: #fff;
        stroke-miterlimit: 10;
        margin: 10% auto;
        box-shadow: inset 0px 0px 0px #C6A87D;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark-check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke .3s cubic-bezier(0.65, 0, 0.45, 1) .8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {
        0%, 100% {
            transform: none;
        }
        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 50px #C6A87D;
        }
    }
    
    .success-panel {
        max-width: 600px;
        border-radius: 8px;
        box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.1);
    }
    
    .success-panel .panel-header {
        background-color: #C6A87D;
        border-radius: 8px 8px 0 0;
        color: white;
        padding: 20px;
        text-align: center;
    }
    
    .success-panel .panel-body {
        padding: 30px;
        background-color: white;
        border-radius: 0 0 8px 8px;
    }
    
    .detail-row {
        display: flex;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px dashed #eaeaea;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        width: 150px;
        font-weight: 500;
        color: #888;
    }
    
    .detail-value {
        flex-grow: 1;
    }
    
    .download-btn {
        background-color: #C6A87D;
        color: white;
        padding: 12px 24px;
        border-radius: 4px;
        text-align: center;
        font-weight: 500;
        transition: all 0.3s;
        display: block;
        width: 100%;
        text-decoration: none;
    }
    
    .download-btn:hover {
        background-color: #b59665;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Ana içerik -->
    <div class="container mx-auto px-4 py-24 flex flex-col items-center">
        <!-- Başarı animasyonu -->
        <div class="checkmark">
            <svg class="checkmark-circle" viewBox="0 0 52 52">
                <circle class="checkmark-circle__circle" cx="26" cy="26" r="25" fill="none"/>
                <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
            </svg>
        </div>
        
        <!-- Başarı paneli -->
        <div class="success-panel mt-6 w-full">
            <div class="panel-header">
                <h2 class="text-2xl font-semibold" data-translate="reservation_successful">
                    Rezervasyon Başarılı!
                </h2>
                <p class="mt-2 opacity-90" data-translate="reservation_confirmed">
                    Rezervasyonunuz onaylandı ve ödemeniz alındı
                </p>
            </div>
            
            <div class="panel-body">
                <div class="text-center mb-6">
                    <p class="text-lg font-semibold text-gray-700" data-translate="reservation_code">Rezervasyon Kodu</p>
                    <p class="text-2xl font-bold text-gold">{{ reservation.reservation_code }}</p>
                </div>
                
                <div class="border-t border-b border-gray-200 py-4 my-4">
                    <div class="detail-row">
                        <div class="detail-label" data-translate="room">Oda:</div>
                        <div class="detail-value">{{ get_lang_text(reservation.room, 'title') if reservation.room else reservation.room_type }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="guest">Misafir:</div>
                        <div class="detail-value">{{ reservation.name }} {{ reservation.surname }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="check_in_date">Giriş Tarihi:</div>
                        <div class="detail-value">{{ reservation.check_in.strftime('%d.%m.%Y') }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="check_out_date">Çıkış Tarihi:</div>
                        <div class="detail-value">{{ reservation.check_out.strftime('%d.%m.%Y') }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="nights">Toplam Gece:</div>
                        <div class="detail-value">{{ reservation.get_total_nights() }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="guests">Misafir Sayısı:</div>
                        <div class="detail-value">{{ reservation.guests }}</div>
                    </div>
                    
                    <div class="detail-row">
                        <div class="detail-label" data-translate="payment_amount">Ödeme Tutarı:</div>
                        <div class="detail-value font-bold">
                            {% if reservation.currency == 'TRY' %}
                                {{ reservation.amount|round|int }} ₺
                            {% elif reservation.currency == 'USD' %}
                                ${{ reservation.amount|round|int }}
                            {% elif reservation.currency == 'EUR' %}
                                €{{ reservation.amount|round|int }}
                            {% else %}
                                {{ reservation.amount|round|int }} {{ reservation.currency }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 mb-4">
                    <p class="text-center text-gray-600 mb-6" data-translate="reservation_details_note">
                        Rezervasyon detaylarınız e-posta adresinize de gönderilmiştir.
                    </p>
                    
                    <a href="{{ url_for('reservation.download_reservation_pdf', code=reservation.reservation_code) }}" 
                       class="download-btn">
                        <i class="fas fa-file-pdf mr-2"></i>
                        <span data-translate="download_confirmation">Rezervasyon Belgesini İndir</span>
                    </a>
                </div>
                
                <div class="text-center mt-8">
                    <a href="{{ url_for('main.main') }}" class="text-gold hover:text-gold/80">
                        <i class="fas fa-home mr-1"></i> 
                        <span data-translate="back_to_homepage">Ana Sayfaya Dön</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 