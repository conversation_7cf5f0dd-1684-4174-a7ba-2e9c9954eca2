{% extends 'base.html' %}

{% block title %}<PERSON><PERSON>{% endblock %}

{% block content %}
<!-- Header -->

<head>
    <!-- Google Fonts Great Vibes -->
    <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap" rel="stylesheet">
</head>

<!-- Main Content -->
<main class="pt-12"> <!-- pt-32'den pt-12'ye düşürüldü -->
    <section class="pt-20 pb-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Galeri Başlığı -->
                <div class="gallery-title-container">
                    <h2 class="gallery-title" data-translate="gallery_title"><PERSON>ri</h2>
                </div>
                <p class="text-gray-600 max-w-2xl mx-auto" data-translate="gallery_description"></p>
            </div>

             <!-- <PERSON><PERSON> -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="galleryGrid">
                {% for gallery in galleries %}
                <div class="gallery-item" data-category="{{ gallery.category }}">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden group cursor-pointer transform transition duration-300 hover:shadow-xl hover:-translate-y-1"
                         onclick="openModal(`{{ gallery.image|escape }}`, `{{ gallery.title|escape }}`, `{{ gallery.description|default('') }}`)">
                        <div class="relative overflow-hidden aspect-w-16 aspect-h-9">
                            <img src="{{ url_for('static', filename='uploads/gallery/' + gallery.image) }}"
                                 alt="{{ gallery.title|escape }}"
                                 class="w-full h-64 object-cover transform transition duration-500 group-hover:scale-110"
                                 loading="lazy"
                                 onerror="this.src='data:image/svg+xml;charset=UTF-8,<svg width=\'200\' height=\'200\' xmlns=\'http://www.w3.org/2000/svg\'><rect width=\'200\' height=\'200\' fill=\'%23f3f4f6\'/><text x=\'50%\' y=\'50%\' font-size=\'16\' text-anchor=\'middle\' fill=\'%236b7280\'>Resim Bulunamadı</text></svg>'">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                <span class="text-white opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
                                    <i class="fas fa-search-plus text-3xl"></i>
                                </span>
                            </div>
                        </div>
                        <div class="p-4 hidden">
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ gallery.title }}</h3>
                            {% if gallery.description %}
                            <p class="mt-2 text-gray-600">{{ gallery.description }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Modal -->
    <div id="imageModal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true">
        <div class="absolute inset-0 bg-black bg-opacity-75 transition-opacity" onclick="closeModal()"></div>
        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all max-w-4xl w-full">
                    <!-- Modal Close Button -->
                    <button onclick="closeModal()" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10" aria-label="Kapat">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <!-- Modal Content -->
                    <div class="relative">
                        <img id="modalImage" src="" alt="" class="w-full max-h-[80vh] object-contain">
                        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-4">
                            <h3 id="modalTitle" class="text-xl font-semibold"></h3>
                            <p id="modalDescription" class="mt-1 text-sm"></p>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="absolute inset-y-0 left-0 flex items-center">
                        <button onclick="navigate(-1)" class="bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-r p-2 m-2 transition-all" aria-label="Önceki">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="absolute inset-y-0 right-0 flex items-center">
                        <button onclick="navigate(1)" class="bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-l p-2 m-2 transition-all" aria-label="Sonraki">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->

<!-- Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Menü işlevselliği
    const menuButton = document.getElementById('menuButton');
    const closeMenu = document.getElementById('closeMenu');
    const sideMenu = document.getElementById('sideMenu');

    menuButton.addEventListener('click', function() {
        sideMenu.classList.remove('-translate-x-full');
        document.body.style.overflow = 'hidden';
    });

    closeMenu.addEventListener('click', function() {
        sideMenu.classList.add('-translate-x-full');
        document.body.style.overflow = '';
    });

    // Menü dışına tıklandığında kapanma
    document.addEventListener('click', function(e) {
        if (!sideMenu.contains(e.target) && !menuButton.contains(e.target) && !sideMenu.classList.contains('-translate-x-full')) {
            sideMenu.classList.add('-translate-x-full');
            document.body.style.overflow = '';
        }
    });

    let currentIndex = 0;
    const galleries = JSON.parse('{{ gallery_data|tojson|safe }}');

    window.openModal = function(image, title, description) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalDescription = document.getElementById('modalDescription');

        currentIndex = galleries.findIndex(g => g.image.includes(image));

        modalImage.src = galleries[currentIndex].image;
        modalImage.alt = galleries[currentIndex].title;
        modalTitle.textContent = galleries[currentIndex].title;
        modalDescription.textContent = galleries[currentIndex].description;

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Preload adjacent images
        if (currentIndex > 0) {
            const prevImage = new Image();
            prevImage.src = galleries[currentIndex - 1].image;
        }
        if (currentIndex < galleries.length - 1) {
            const nextImage = new Image();
            nextImage.src = galleries[currentIndex + 1].image;
        }
    };

    window.closeModal = function() {
        const modal = document.getElementById('imageModal');
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    };

    window.navigate = function(direction) {
        currentIndex = (currentIndex + direction + galleries.length) % galleries.length;
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalDescription = document.getElementById('modalDescription');

        modalImage.src = galleries[currentIndex].image;
        modalImage.alt = galleries[currentIndex].title;
        modalTitle.textContent = galleries[currentIndex].title;
        modalDescription.textContent = galleries[currentIndex].description;
    };

    // Klavye kontrolü
    document.addEventListener('keydown', function(e) {
        if (document.getElementById('imageModal').classList.contains('hidden')) return;

        if (e.key === 'Escape') closeModal();
        if (e.key === 'ArrowLeft') navigate(-1);
        if (e.key === 'ArrowRight') navigate(1);
    });

    // Kategori filtreleme
    const filterButtons = document.querySelectorAll('[data-category]');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const category = button.dataset.category;

            filterButtons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-200');
            });
            button.classList.remove('bg-gray-200');
            button.classList.add('bg-blue-600', 'text-white');

            galleryItems.forEach(item => {
                const shouldShow = category === 'all' || item.dataset.category === category;
                item.style.display = shouldShow ? 'block' : 'none';
                if (shouldShow) {
                    item.classList.add('animate-fade-in');
                    setTimeout(() => item.classList.remove('animate-fade-in'), 500);
                }
            });
        });
    });
});
</script>

<style>
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Menü animasyonu için ek stiller */
#sideMenu {
    transition: transform 0.3s ease-in-out;
}

body {
    font-family: 'Great Vibes', cursive;
}

.gallery-title-container {
    margin-bottom: 2rem;
    padding-top: 15px; /* Yukarıdan 15px boşluk */
}

.gallery-title {
    font-family: 'Great Vibes', cursive;
    font-size: 3.5rem !important; /* Daha makul bir font boyutu */
    font-weight: 400;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    color: #333 !important; /* Koyu renk */
    display: inline-block;
    padding: 0.5rem 2rem;
    position: relative;
}
</style>
{% endblock %}