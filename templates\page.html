{% extends "base.html" %}

{% block title %}
    {% if page %}
        {% if g.language == 'tr' %}
            {{ page.title_tr }}
        {% else %}
            {{ page.title_en or page.title_tr }}
        {% endif %}
    {% else %}
        Sayfa Bulunamadı
    {% endif %}
{% endblock %}

{% block head %}
<style>
@import url('https://fonts.googleapis.com/css2?family=Libre+Caslon+Display&display=swap');

/* <PERSON><PERSON><PERSON><PERSON> stilleri */
.prose {
    max-width: none;
    width: 100%;
    margin: 0;
    font-family: 'Libre Caslon Display', serif;
    padding: 0;
    text-align: center;
}

.prose p {
    margin-bottom: 2rem;
    line-height: 1.9;
    color: #374151;
    font-size: 1.125rem;
    font-weight: 400;
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
    text-align: center;
    padding: 0 1rem;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
    color: #111827;
    font-weight: 600;
    line-height: 1.3;
    font-family: 'Great Vibes', cursive !important;
    position: relative;
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
    text-align: center;
    padding: 0 1rem;
}

.prose h1 {
    font-size: 2.75rem;
    margin: 3rem 0 2rem;
    letter-spacing: -0.02em;
}

.prose h2 {
    font-size: 2.25rem;
    margin: 2.5rem 0 1.5rem;
    letter-spacing: -0.015em;
}

.prose h2::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 3rem;
    height: 2px;
    background: linear-gradient(to right, #C6A87D, transparent);
}

.prose h3 {
    font-size: 1.75rem;
    margin: 2rem 0 1.25rem;
}

.prose img {
    border-radius: 1rem;
    margin: 3rem auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: transform 0.3s ease;
    max-width: 100%;
    width: auto;
}

.prose img:hover {
    transform: translateY(-4px);
}

.prose a {
    color: #C6A87D;
    text-decoration: none;
    border-bottom: 1px solid rgba(198, 168, 125, 0.2);
    transition: all 0.2s ease;
    font-weight: 500;
}

.prose a:hover {
    color: #9F815A;
    border-color: #9F815A;
}

.prose ul, .prose ol {
    margin: 2rem 0;
    padding-left: 1.75rem;
    color: #374151;
    max-width: 75ch;
    margin-left: auto;
    margin-right: auto;
}

.prose li {
    margin: 0.75rem 0;
    padding-left: 0.5rem;
    line-height: 1.7;
}

.prose blockquote {
    border-left: 3px solid #C6A87D;
    padding: 1.5rem 2rem;
    margin: 2.5rem 0;
    background: rgba(198, 168, 125, 0.05);
    border-radius: 0.5rem;
    max-width: 75ch;
    margin-left: auto;
    margin-right: auto;
}

.prose blockquote p {
    color: #4B5563;
    font-style: italic;
    font-size: 1.2rem;
    line-height: 1.8;
    margin: 0;
}

.prose hr {
    margin: 3rem 0;
    border: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, #E5E7EB, transparent);
}

/* CKEditor tabloları için genişlik ayarları */
.ck-content table {
    width: 100% !important;
    max-width: 100% !important;
    border-collapse: collapse;
    margin: 0;
    table-layout: fixed;
}

.ck-content table th, .ck-content table td {
    padding: 0.5rem;
    border: 1px solid #ddd;
    text-align: left;
    word-wrap: break-word;
}

.ck-content table th {
    background-color: #f9fafb;
    font-weight: bold;
}

/* CKEditor tabloları için tam genişlik ayarları */
.full-width-table {
    width: 100% !important;
    max-width: 100% !important;
    border-collapse: collapse;
    margin: 0;
    table-layout: fixed;
}

.full-width-table th, .full-width-table td {
    padding: 0.5rem;
    border: 1px solid #ddd;
    text-align: left;
    word-wrap: break-word;
}

/* Özel Elementler */
.content-meta {
    font-size: 0.875rem;
    color: #6B7280;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.content-meta i {
    color: #C6A87D;
}

.share-buttons {
    display: flex;
    gap: 1rem;
}

.share-button {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    background: #F3F4F6;
    color: #4B5563;
    transition: all 0.2s ease;
}

.share-button:hover {
    background: #C6A87D;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Tasarım */
@media (max-width: 1600px) {
    .content-card {
        width: 95vw;
    }
}

@media (max-width: 768px) {
    .prose {
        padding: 0 1rem;
    }

    .prose h1 { font-size: 2rem; }
    .prose h2 { font-size: 1.75rem; }
    .prose h3 { font-size: 1.5rem; }
    .prose p { font-size: 1rem; }
    .prose blockquote p { font-size: 1.1rem; }
    .content-card {
        width: 100%;
        border-radius: 0;
    }
}

/* Container genişliğini artır */
.page-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.page-content {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Üst menü */
.top-menu {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;
}

/* Ana içerik */
.main-content {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Alt bilgi */
.footer {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 1rem;
    background-color: #f9fafb;
}

/* Hero bölümü için özel stil */
.hero-content {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
}

.hero-content img {
    width: 100%; /* Tam ekran genişlik */
    height: auto; /* Oranları koru */
    object-fit: cover; /* Görseli düzgün şekilde yerleştir */
}

/* İçerik kartı için özel stil */
.content-card {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
}

/* Hero bölümü başlığı için özel stil */
.hero-content h1 {
    font-family: 'Great Vibes', cursive !important;
    font-size: 3.5rem;
    font-weight: 400;
    line-height: 1.2;
    margin: 0;
    padding: 0;
    color: #111827;
}

/* Paylaşım butonları için yeni stiller */
.share-buttons-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 0;
    padding: 1rem;
    background: linear-gradient(to right, rgba(198, 168, 125, 0.05), rgba(198, 168, 125, 0.1), rgba(198, 168, 125, 0.05));
    border-radius: 1rem;
}

.share-button {
    width: 3rem !important;
    height: 3rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    background: white;
    color: #4B5563;
    transition: all 0.3s ease;
    border: 1px solid rgba(198, 168, 125, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.share-button:hover {
    background: #C6A87D;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(198, 168, 125, 0.2);
}

.share-button i {
    font-size: 1.2rem;
}

#mainShareBtn {
    background: #C6A87D;
    color: white;
}

#mainShareBtn:hover {
    background: #9F815A;
}
</style>

<!-- Font Import -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Playfair+Display:wght@600;700&display=swap" rel="stylesheet">

<!-- Schema.org structured data for WebPage -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{% if g.language == 'tr' %}{{ page.title_tr }}{% else %}{{ page.title_en or page.title_tr }}{% endif %}",
  "description": "{{ page.meta_description }}",
  "url": "{{ request.url }}",
  "mainEntity": {
    "@type": "Article",
    "headline": "{% if g.language == 'tr' %}{{ page.title_tr }}{% else %}{{ page.title_en or page.title_tr }}{% endif %}",
    "description": "{{ page.meta_description }}",
    "image": "{% if page.featured_image %}{{ url_for('static', filename='uploads/pages/' + page.featured_image, _external=True) }}{% endif %}",
    "datePublished": "{{ page.created_at.isoformat() if page.created_at }}",
    "dateModified": "{{ page.updated_at.isoformat() if page.updated_at }}",
    "author": {
      "@type": "Organization",
      "name": "Zeppelin Cappadocia",
      "url": "https://zeppelincappadocia.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Zeppelin Cappadocia",
      "logo": {
        "@type": "ImageObject",
        "url": "{{ url_for('static', filename='assets/images/logo.webp', _external=True) }}"
      }
    }
  }
}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Görsel tıklama için lightbox efekti
    const images = document.querySelectorAll('.prose img');
    images.forEach(img => {
        img.addEventListener('click', () => {
            const lightbox = document.createElement('div');
            lightbox.className = 'fixed inset-0 bg-black/90 z-50 flex items-center justify-center cursor-pointer transition-opacity duration-300';

            const image = document.createElement('img');
            image.src = img.src;
            image.className = 'max-w-[90vw] max-h-[90vh] object-contain';

            lightbox.appendChild(image);
            document.body.appendChild(lightbox);

            lightbox.addEventListener('click', () => {
                lightbox.classList.add('opacity-0');
                setTimeout(() => lightbox.remove(), 300);
            });
        });
        img.classList.add('cursor-zoom-in');
    });

    // Paylaşım butonları için
    const shareButtons = document.querySelectorAll('.share-button');
    shareButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.classList.add('scale-110');
        });
        button.addEventListener('mouseleave', function() {
            this.classList.remove('scale-110');
        });
    });

    // Main share button functionality
    const mainShareBtn = document.getElementById('mainShareBtn');
    if (mainShareBtn) {
        mainShareBtn.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                })
                .catch(error => console.log('Paylaşım hatası:', error));
            } else {
                alert('Tarayıcınız paylaşım özelliğini desteklemiyor. Lütfen diğer paylaşım seçeneklerini kullanın.');
            }
        });
    }
});

// Social media sharing functions
function shareOnFacebook() {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

function shareOnTwitter() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(document.title);
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
}

function shareOnWhatsApp() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(document.title);
    window.open(`https://wa.me/?text=${text} ${url}`, '_blank');
}

function shareOnInstagram() {
    alert('Instagram\'da doğrudan paylaşım yapılamıyor. URL\'yi kopyalayarak manuel olarak paylaşabilirsiniz.');
}
</script>
{% endblock %}

{% block content %}
{% if error %}
    <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-lg w-full text-center p-8">
            <h1 class="text-6xl font-light text-gray-800 mb-8">404</h1>
            <p class="text-xl text-gray-600 mb-8">{{ error }}</p>
            <a href="{{ url_for('main.index') }}"
               class="inline-block px-6 py-3 bg-gold text-white rounded-full hover:bg-gold/90 transition-colors">
                Ana Sayfaya Dön
            </a>
        </div>
    </div>
{% else %}
    <main class="flex-grow bg-white">
        <!-- Hero Bölümü -->
        <div class="relative min-h-[320px] py-20 mb-0 overflow-hidden bg-zinc-900">
            <!-- Arkaplan Görseli ve Overlay -->
            {% if page.featured_image %}
            <div class="absolute inset-0 w-full h-full">
                <img src="{{ url_for('static', filename='uploads/pages/' + page.featured_image) }}"
                     alt="{{ page.title }}"
                     class="absolute inset-0 w-full h-full object-cover object-center"
                     onerror="this.onerror=null; console.log('Görsel yüklenemedi:', this.src);">
                <div class="absolute inset-0 bg-gradient-to-r from-black/80 to-black/60"></div>
            </div>
            {% endif %}

            <!-- Dekoratif Elementler -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 right-0 w-96 h-96 bg-gold/20 rounded-full blur-3xl translate-x-1/2 -translate-y-1/2"></div>
                <div class="absolute bottom-0 left-0 w-96 h-96 bg-gold/20 rounded-full blur-3xl -translate-x-1/2 translate-y-1/2"></div>
            </div>

            <!-- Başlık İçeriği -->
            <div class="container mx-auto px-4 relative z-10">
                <div class="text-center">
                    <h1 class="text-4xl md:text-6xl font-great-vibes text-white mb-6 mt-16">
                        {% if g.language == 'tr' %}
                            {{ page.title_tr }}
                        {% else %}
                            {{ page.title_en or page.title_tr }}
                        {% endif %}
                    </h1>
                    {% if page.meta_description %}
                    <p class="mt-6 text-xl text-white/80 max-w-3xl mx-auto font-light leading-relaxed">
                        {{ page.meta_description }}
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- İçerik Alanı -->
        <div class="w-full mx-0 px-0 pb-0 mb-0">
            <div class="w-full mx-0 bg-white">
                <!-- Dekoratif Kenar -->
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-gold/40 via-gold to-gold/40"></div>

                <!-- İçerik -->
                <article class="pt-0">
                    <div class="w-full">
                        <!-- Makale İçeriği -->
                        <div class="prose-lg lg:prose-xl max-w-none">
                            <!-- Ana İçerik -->
                            <div class="article-content space-y-6">
                                {% if g.language == 'tr' %}
                                    {{ page.content_tr|safe }}
                                {% else %}
                                    {{ page.content_en|safe or page.content_tr|safe }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Alt Bilgi -->
                    {% if page.updated_at %}
                    <div class="mt-8 pt-4 border-t border-zinc-100">
                        <div class="share-buttons-container">
                            <button class="share-button" title="Paylaş" id="mainShareBtn">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            <a href="javascript:void(0)" onclick="shareOnFacebook()" class="share-button" title="Facebook'ta Paylaş">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="javascript:void(0)" onclick="shareOnTwitter()" class="share-button" title="Twitter'da Paylaş">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="javascript:void(0)" onclick="shareOnInstagram()" class="share-button" title="Instagram'da Paylaş">
    <i class="fab fa-instagram"></i>
</a>
                            <a href="javascript:void(0)" onclick="shareOnWhatsApp()" class="share-button" title="WhatsApp'ta Paylaş">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </article>
            </div>
        </div>
    </main>

    <style>
    /* Makale içeriği için özel stiller */
    .article-content {
        text-align: center;
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .article-content img {
        max-width: 100%;
        width: auto;
        margin: 0 auto;
        border-radius: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
        display: block;
    }

    .article-content p {
        color: #3f3f46;
        line-height: 1.75;
        text-align: center;
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .article-content h2 {
        font-size: 1.875rem;
        font-family: 'Playfair Display', serif;
        color: #18181b;
        margin: 2rem 0;
        position: relative;
        text-align: center;
        padding: 0;
    }

    .article-content h2::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 3rem;
        height: 0.125rem;
        background: linear-gradient(to right, transparent, #C6A87D, transparent);
    }

    .article-content blockquote {
        padding: 1rem;
        border-left: 4px solid rgba(198, 168, 125, 0.3);
        font-style: italic;
        color: #52525b;
        background: rgba(244, 244, 245, 0.5);
        margin: 2rem 0;
        text-align: center;
        max-width: 100%;
    }

    /* Summernote tarafından oluşturulan tablolar için genişlik ayarları */
    .article-content table {
        width: 100% !important;
        max-width: 100% !important;
        border-collapse: collapse;
        margin: 2rem auto !important;
        border: 1px solid #ddd;
        table-layout: fixed;
        padding: 0 !important;
        box-sizing: border-box;
    }

    .article-content table th,
    .article-content table td {
        padding: 0.5rem;
        border: 1px solid #ddd;
        text-align: left;
        word-wrap: break-word;
        margin: 0 !important;
    }

    /* Tablo içindeki paragrafları düzelt */
    .article-content table p {
        margin: 0 !important;
        padding: 0 !important;
        text-align: left;
    }

    .article-content table th {
        background-color: #f9fafb;
        font-weight: bold;
    }

    /* @screen direktiflerini media query'lere dönüştür */
    @media (min-width: 640px) {
        .article-content > *:not(table) {
            max-width: 85ch;
            margin-left: auto;
            margin-right: auto;
        }

        .article-content table {
            width: 100%;
            max-width: 100%;
        }
    }

    @media (min-width: 1024px) {
    .article-content img {
        width: 100% !important;
        max-width: none !important;
    }
}

    /* Mobil cihazlar için tablo görünümü */
    @media (max-width: 640px) {
        .article-content table,
        .ck-content table,
        .full-width-table {
            display: block;
            width: 100% !important;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
    }
    </style>
{% endif %}
{% endblock %}