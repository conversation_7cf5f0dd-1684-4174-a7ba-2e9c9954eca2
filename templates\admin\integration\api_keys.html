{% extends "admin/base.html" %}

{% block breadcrumb %}Entegrasyon / API Anahtarları{% endblock %}
{% block page_title %}API Anahtarları{% endblock %}
{% block page_subtitle %}API erişim anahtarlarını yönetin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <!-- API Anahtarı Oluştur -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">API Anahtarları</h3>
                    <button type="button" 
                            class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600">
                        <i class="fas fa-plus mr-2"></i>
                        Yeni API Anahtarı
                    </button>
                </div>
                
                <!-- API Anahtarları Listesi -->
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-medium text-gray-900">Rezervasyon API</h4>
                                <p class="text-sm text-gray-500 mt-1">Rezervasyon sistemi için API anahtarı</p>
                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Aktif
                                    </span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="text-gray-400 hover:text-gray-500">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="text-red-400 hover:text-red-500">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-3">
                            <input type="text" value="sk_test_123456789" readonly
                                   class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-mono">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Analytics Hızlı Erişim -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fab fa-google text-blue-600 mr-2"></i>
                    Google Analytics
                </h3>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Analytics & Tag Manager</h4>
                            <p class="text-sm text-gray-600">Web sitenizin analitik verilerini yönetin</p>
                            <div class="flex items-center mt-3 space-x-4">
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600">Analytics:</span>
                                    {% if settings.google_analytics_active == 'True' and settings.google_analytics_id %}
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Aktif
                                    </span>
                                    {% else %}
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times mr-1"></i>
                                        Pasif
                                    </span>
                                    {% endif %}
                                </div>

                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600">Tag Manager:</span>
                                    {% if settings.google_tag_manager_active == 'True' and settings.google_tag_manager_id %}
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Aktif
                                    </span>
                                    {% else %}
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-times mr-1"></i>
                                        Pasif
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-col space-y-2">
                            <a href="{{ url_for('integration.google_analytics') }}"
                               class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                <i class="fas fa-cog mr-2"></i>
                                Yönet
                            </a>

                            <a href="https://analytics.google.com/analytics/web/#/p10451427301/reports/intelligenthome"
                               target="_blank"
                               class="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Panel
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Webhook Ayarları -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Webhook URL'leri</h3>
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900">Rezervasyon Webhook</h4>
                        <p class="text-sm text-gray-500 mt-1">Rezervasyon bildirimleri için webhook URL'i</p>
                        <div class="mt-3 flex items-center space-x-2">
                            <input type="text" value="https://example.com/webhook/reservation" readonly
                                   class="flex-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-mono">
                            <button class="text-gray-400 hover:text-gray-500">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAnalytics() {
    // Google Analytics test eventi gönder
    if (typeof gtag !== 'undefined') {
        gtag('event', 'test_event', {
            'event_category': 'admin_test',
            'event_label': 'analytics_integration_test',
            'value': 1
        });

        // Başarı mesajı göster
        showNotification('Test eventi Google Analytics\'e gönderildi!', 'success');
    } else {
        showNotification('Google Analytics yüklenmemiş. Lütfen ayarları kontrol edin.', 'error');
    }
}

function showNotification(message, type) {
    // Basit bildirim sistemi
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // 3 saniye sonra kaldır
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Kopyalama fonksiyonları
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Panoya kopyalandı!', 'success');
    });
}
</script>
{% endblock %}