from models import db
from datetime import datetime

class Setting(db.Model):
    __tablename__ = 'settings'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    type_ = db.Column(db.String(20), default='text')
    description = db.Column(db.Text)
    group = db.Column(db.String(50), default='general')
    click_count = db.Column(db.Integer, default=0)  # Tıklanma sayısı
    last_click = db.Column(db.DateTime)  # <PERSON> tıklanma zamanı
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    site_logo = db.Column(db.String(255))  # Ana logo
    site_logo_small = db.Column(db.String(255))  # <PERSON><PERSON><PERSON>ük logo
    
    def __repr__(self):
        return f'<Setting {self.key}>'

    @staticmethod
    def get_setting(key, default=None):
        setting = Setting.query.filter_by(key=key).first()
        return setting.value if setting else default

    @staticmethod
    def set_setting(key, value, type_='text', group='general'):
        """Ayar değerini günceller veya yeni ayar oluşturur"""
        setting = Setting.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            setting.type_ = type_
            setting.group = group
        else:
            setting = Setting(
                key=key, 
                value=value,
                type_=type_,
                group=group
            )
            db.session.add(setting)
        db.session.commit()
        return setting 

def get_settings():
    """Site ayarlarını getiren yardımcı fonksiyon"""
    # Temel ayarları kontrol et ve yoksa oluştur
    default_settings = {
        'site_title': 'Site Başlığı',
        'site_description': 'Site Açıklaması',
        'site_keywords': 'anahtar, kelimeler',
        'contact_email': '<EMAIL>',
        'contact_phone': '+90 555 555 55 55',
        'contact_address': 'Adres bilgisi',
        'footer_text': '© 2024 Tüm hakları saklıdır.'
    }

    settings_dict = {}

    # Önce default ayarları yükle
    for key, default_value in default_settings.items():
        setting = Setting.query.filter_by(key=key).first()
        if not setting:
            setting = Setting(
                key=key,
                value=default_value,
                type_='text',
                group='general'
            )
            db.session.add(setting)
            db.session.commit()
        settings_dict[key] = setting.value

    # Sonra tüm diğer ayarları da yükle (Google Analytics, vb.)
    all_settings = Setting.query.all()
    for setting in all_settings:
        # Boolean değerleri düzgün dönüştür
        if setting.type_ == 'boolean':
            settings_dict[setting.key] = setting.value in ['True', 'true', '1', True]
        else:
            settings_dict[setting.key] = setting.value

    # Nesne gibi erişim için SimpleNamespace kullan
    from types import SimpleNamespace
    return SimpleNamespace(**settings_dict)