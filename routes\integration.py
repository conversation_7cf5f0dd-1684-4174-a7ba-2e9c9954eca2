from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.slider import db
from models.payment import PaymentMethod, PaymentProvider
from models.setting import get_settings, Setting
from services.google_analytics import analytics_service

integration_bp = Blueprint('integration', __name__)

@integration_bp.route('/admin/integration/payment', methods=['GET'])
@login_required
def payment_settings():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    # Mevcut ödeme yöntemlerini al (sadece sanal POS)
    vakifbank = PaymentMethod.query.filter_by(provider=PaymentProvider.VAKIFBANK).first()
    iyzico = PaymentMethod.query.filter_by(provider=PaymentProvider.IYZICO).first()

    # Eğer kayıt yoksa varsayılan değerlerle oluştur (sadece sanal POS)
    if not vakifbank:
        vakifbank = PaymentMethod(
            name='VakıfBank Sanal POS',
            provider=PaymentProvider.VAKIFBANK,
            description='VakıfBank Sanal POS Sistemi',
            is_active=False,
            is_test_mode=True,
            currency='TRY'
        )
        db.session.add(vakifbank)
        db.session.commit()

    if not iyzico:
        iyzico = PaymentMethod(
            name='iyzico Sanal POS',
            provider=PaymentProvider.IYZICO,
            description='iyzico Sanal POS Sistemi',
            is_active=False,
            is_test_mode=True,
            currency='TRY'
        )
        db.session.add(iyzico)
        db.session.commit()

    return render_template('admin/integration/payment.html',
                         vakifbank=vakifbank,
                         iyzico=iyzico,
                         settings=get_settings())

@integration_bp.route('/admin/integration/payment/vakifbank', methods=['POST'])
@login_required
def update_vakifbank():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    vakifbank = PaymentMethod.query.filter_by(
        provider=PaymentProvider.VAKIFBANK
    ).first()

    if not vakifbank:
        vakifbank = PaymentMethod(
            name='VakıfBank',
            provider=PaymentProvider.VAKIFBANK
        )

    # Form verilerini al
    vakifbank.merchant_id = request.form.get('merchant_id')
    vakifbank.terminal_id = request.form.get('terminal_id')
    vakifbank.terminal_user = request.form.get('terminal_user')
    vakifbank.terminal_pass = request.form.get('terminal_pass')
    vakifbank.secure3d_url = request.form.get('secure3d_url')
    vakifbank.success_url = request.form.get('success_url')
    vakifbank.is_test_mode = bool(request.form.get('test_mode'))
    vakifbank.is_active = bool(request.form.get('is_active'))
    
    # Taksit seçeneklerini güncelle
    installments = request.form.getlist('installments')
    vakifbank.installment_options = [int(x) for x in installments]

    try:
        db.session.add(vakifbank)
        db.session.commit()
        flash('VakıfBank ayarları başarıyla güncellendi.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ayarlar kaydedilirken bir hata oluştu: {str(e)}', 'error')

    return redirect(url_for('integration.payment_settings'))



@integration_bp.route('/admin/integration/payment/iyzico', methods=['POST'])
@login_required
def update_iyzico():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    iyzico = PaymentMethod.query.filter_by(
        provider=PaymentProvider.IYZICO
    ).first()

    if not iyzico:
        iyzico = PaymentMethod(
            name='iyzico',
            provider=PaymentProvider.IYZICO,
            description='iyzico Payment Gateway',
            is_active=False,
            is_test_mode=True,
            currency='TRY'
        )

    # Form verilerini al
    iyzico.iyzico_api_key = request.form.get('api_key')
    iyzico.iyzico_secret_key = request.form.get('secret_key')
    iyzico.iyzico_base_url = request.form.get('base_url')
    iyzico.is_test_mode = bool(request.form.get('test_mode'))
    iyzico.is_active = bool(request.form.get('is_active'))
    
    # Taksit seçeneklerini güncelle
    installments = request.form.getlist('installments')
    iyzico.installment_options = [int(x) for x in installments]

    try:
        db.session.add(iyzico)
        db.session.commit()
        flash('iyzico ayarları başarıyla güncellendi.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Ayarlar kaydedilirken bir hata oluştu: {str(e)}', 'error')

    return redirect(url_for('integration.payment_settings'))





@integration_bp.route('/admin/integration/api-keys')
@login_required
def api_keys():
    return render_template('admin/integration/api_keys.html')

@integration_bp.route('/admin/integration/google-analytics')
@login_required
def google_analytics():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    settings = get_settings()

    # Google Analytics verilerini çek
    monthly_data = analytics_service.get_monthly_visitors()
    weekly_data = analytics_service.get_weekly_visitors()
    daily_data = analytics_service.get_daily_visitors()
    top_pages = analytics_service.get_top_pages(5)
    credentials_status = analytics_service.get_credentials_status()

    return render_template('admin/integration/google_analytics.html',
                         settings=settings,
                         monthly_data=monthly_data,
                         weekly_data=weekly_data,
                         daily_data=daily_data,
                         top_pages=top_pages,
                         credentials_status=credentials_status)

@integration_bp.route('/admin/integration/google-analytics/test-connection', methods=['POST'])
@login_required
def test_google_analytics_connection():
    """Google Analytics bağlantısını test et"""
    try:
        test_result = analytics_service.test_connection()
        return jsonify(test_result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Test sırasında hata: {str(e)}'
        })

@integration_bp.route('/admin/integration/google-analytics/refresh-data', methods=['POST'])
@login_required
def refresh_analytics_data():
    """Google Analytics verilerini yenile"""
    try:
        # Tüm verileri yeniden çek
        daily_data = analytics_service.get_daily_visitors()
        weekly_data = analytics_service.get_weekly_visitors()
        monthly_data = analytics_service.get_monthly_visitors()
        top_pages = analytics_service.get_top_pages(10)

        return jsonify({
            'success': True,
            'message': 'Veriler başarıyla yenilendi',
            'data': {
                'daily': daily_data,
                'weekly': weekly_data,
                'monthly': monthly_data,
                'top_pages': top_pages
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Veri yenileme hatası: {str(e)}'
        })

@integration_bp.route('/admin/integration/google-analytics', methods=['POST'])
@login_required
def update_google_analytics():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    try:
        # Google Analytics ayarları
        ga_id = request.form.get('google_analytics_id', '').strip()
        ga_active = 'True' if request.form.get('google_analytics_active') == 'on' else 'False'

        # Google Tag Manager ayarları
        gtm_id = request.form.get('google_tag_manager_id', '').strip()
        gtm_active = 'True' if request.form.get('google_tag_manager_active') == 'on' else 'False'

        # Veritabanına kaydet
        Setting.set_setting('google_analytics_id', ga_id, 'text', 'analytics')
        Setting.set_setting('google_analytics_active', ga_active, 'boolean', 'analytics')
        Setting.set_setting('google_tag_manager_id', gtm_id, 'text', 'analytics')
        Setting.set_setting('google_tag_manager_active', gtm_active, 'boolean', 'analytics')

        flash('Google Analytics ayarları başarıyla güncellendi!', 'success')

    except Exception as e:
        flash(f'Ayarlar kaydedilirken bir hata oluştu: {str(e)}', 'error')

    return redirect(url_for('integration.google_analytics'))



@integration_bp.route('/admin/integration/google-analytics/data', methods=['GET'])
@login_required
def get_analytics_data():
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'})

    try:
        period = request.args.get('period', 'monthly')

        if period == 'monthly':
            data = analytics_service.get_monthly_visitors()
        elif period == 'weekly':
            data = analytics_service.get_weekly_visitors()
        elif period == 'daily':
            data = analytics_service.get_daily_visitors()
        else:
            data = analytics_service.get_monthly_visitors()

        return jsonify({
            'success': True,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@integration_bp.route('/admin/integration/google-analytics/test-connection', methods=['POST'])
@login_required
def test_analytics_connection():
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Yetkiniz yok'})

    try:
        result = analytics_service.test_connection()
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

