{% extends "base.html" %}

{% block title %}Blog | {{ settings.site_title or 'Zeppelin Hotel' }}{% endblock %}

{% block head %}
<!-- <PERSON>kstra stil ve script -->
<style>
    /* Blog Section Professional Styles */
    .blog-section {
        background: linear-gradient(135deg, #E3DCD3 0%, #F0F0F0 100%);
    }

    /* Blog Card Hover Effects */
    .blog-card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center bottom;
    }

    .blog-card:hover {
        transform: translateY(-8px) scale(1.02);
    }

    /* Image Aspect Ratio */
    .aspect-4\/3 {
        aspect-ratio: 4 / 3;
    }

    /* Line Clamp Utilities */
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-clamp: 1;
    }
    
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-clamp: 2;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-clamp: 3;
    }
    
    /* Fade-in animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-in-up {
        opacity: 0;
        animation: fadeInUp 0.6s ease-out forwards;
        animation-delay: var(--delay, 0s);
    }
</style>
{% endblock %}

{% block content %}
<section class="blog-section py-20 mt-20">
    <div class="max-w-[1920px] mx-auto px-4">
        <!-- Başlık Bölümü -->
        <div class="text-center mb-20">
            <div class="inline-block">
                <span class="text-gold font-great-vibes text-6xl block mb-2">Blog</span>
                <div class="flex items-center justify-center">
                    <div class="h-[1px] w-16 bg-gradient-to-r from-transparent to-gold"></div>
                    <div class="mx-6 text-gold text-xl">✦</div>
                    <div class="h-[1px] w-16 bg-gradient-to-l from-transparent to-gold"></div>
                </div>
            </div>
            <p class="mt-8 text-gray-700 text-lg max-w-2xl mx-auto leading-relaxed">
                {% if current_language == 'tr' %}
                    Kapadokya'nın büyülü dünyasından hikayeler, seyahat ipuçları ve unutulmaz deneyimler
                {% else %}
                    Stories, travel tips, and unforgettable experiences from the magical world of Cappadocia
                {% endif %}
            </p>
        </div>

        <!-- Blog Grid - 5'li Grid Yapısı -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5 px-2 lg:px-4">
            {% for post in posts.items %}
            <article class="blog-card group cursor-pointer fade-in-up" style="--delay: {{ loop.index0 * 0.1 }}s">
                <a href="{{ url_for('blog.blog_detail', slug=post.slug) }}" class="block">
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-gold/20">
                        <!-- Blog Görseli -->
                        {% if post.featured_image %}
                        <div class="relative overflow-hidden">
                            <div class="aspect-[4/3] overflow-hidden">
                                <img src="{{ url_for('static', filename='uploads/blog/' + post.featured_image) }}"
                                     alt="{{ get_lang_text(post, 'title', current_language) }}"
                                     class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">
                            </div>

                            <!-- Gradient Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                            <!-- Tarih Badge -->
                            <div class="absolute bottom-4 left-4">
                                <div class="bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg shadow-sm">
                                    <div class="flex items-center space-x-2">
                                        <i class="far fa-calendar-alt text-gold text-sm"></i>
                                        <span class="text-gray-700 text-sm font-medium">
                                            {{ post.created_at.strftime('%d %b %Y') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Blog İçeriği -->
                        <div class="p-5 xl:p-6">
                            <!-- Kategori -->
                            <div class="mb-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gold/10 text-gold border border-gold/20">
                                    <i class="fas fa-tag mr-1"></i>
                                    {{ post.category or 'Blog' }}
                                </span>
                            </div>

                            <!-- Başlık -->
                            <h3 class="text-base xl:text-lg font-bold text-gray-900 mb-3 line-clamp-2 leading-tight group-hover:text-gold transition-colors duration-300">
                                {{ get_lang_text(post, 'title', current_language) }}
                            </h3>

                            <!-- Özet -->
                            <p class="text-gray-600 text-sm xl:text-base leading-relaxed mb-4 line-clamp-2 xl:line-clamp-3">
                                {% if post.get_excerpt(current_language) %}
                                    {{ post.get_excerpt(current_language) }}
                                {% else %}
                                    {{ post.get_content(current_language)|striptags|truncate(100) }}
                                {% endif %}
                            </p>

                            <!-- Alt Bilgiler -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                <!-- Meta Bilgiler -->
                                <div class="flex items-center space-x-2 xl:space-x-3">
                                    <div class="flex items-center text-gray-500">
                                        <i class="fas fa-clock text-gold mr-1"></i>
                                        <span class="text-xs xl:text-sm">5dk</span>
                                    </div>
                                    {% if post.view_count %}
                                    <div class="flex items-center text-gray-500">
                                        <i class="fas fa-eye text-gold mr-1"></i>
                                        <span class="text-xs xl:text-sm">{{ post.view_count }}</span>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Okuma Butonu -->
                                <div class="flex items-center text-gold font-medium group-hover:text-gold/80 transition-colors duration-300">
                                    <span class="text-xs xl:text-sm mr-1">
                                        {% if current_language == 'tr' %}
                                            Oku
                                        {% else %}
                                            Read
                                        {% endif %}
                                    </span>
                                    <i class="fas fa-arrow-right text-xs transform group-hover:translate-x-1 transition-transform duration-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </article>
            {% endfor %}
        </div>

        <!-- Sayfalama -->
        {% if posts.pages > 1 %}
        <div class="flex justify-center mt-12">
            <div class="flex flex-wrap justify-center gap-2">
                <!-- Önceki Sayfa -->
                {% if posts.has_prev %}
                <a href="{{ url_for('blog.blog_index', page=posts.prev_num) }}" 
                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gold hover:border-gold hover:text-white transition-all duration-300">
                    <i class="fas fa-chevron-left mr-1"></i> 
                    {% if current_language == 'tr' %}Önceki{% else %}Previous{% endif %}
                </a>
                {% endif %}
                
                <!-- Sayfa Numaraları -->
                {% for page in range(1, posts.pages + 1) %}
                <a href="{{ url_for('blog.blog_index', page=page) }}" 
                   class="px-4 py-2 {% if page == posts.page %}bg-gold text-white border border-gold{% else %}bg-white text-gray-700 border border-gray-300{% endif %} rounded-lg hover:bg-gold hover:text-white hover:border-gold transition-all duration-300">
                    {{ page }}
                </a>
                {% endfor %}
                
                <!-- Sonraki Sayfa -->
                {% if posts.has_next %}
                <a href="{{ url_for('blog.blog_index', page=posts.next_num) }}" 
                   class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gold hover:border-gold hover:text-white transition-all duration-300">
                    {% if current_language == 'tr' %}Sonraki{% else %}Next{% endif %} 
                    <i class="fas fa-chevron-right ml-1"></i>
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %} 