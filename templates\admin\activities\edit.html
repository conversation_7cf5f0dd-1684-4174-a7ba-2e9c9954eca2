{% extends "admin/base.html" %}

{% block title %}Aktivite Düzenle{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Aktivite Düzenle: {{ activity.title_tr }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('activities.activity_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Geri Dön
                        </a>
                    </div>
                </div>
                
                <form method="POST" enctype="multipart/form-data">
                    <div class="card-body">
                        <!-- Türkçe İçerik -->
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-flag"></i> Türkçe İçerik
                                </h5>
                                
                                <div class="form-group">
                                    <label for="title_tr">Başlık (TR) *</label>
                                    <input type="text" class="form-control" id="title_tr" name="title_tr" 
                                           value="{{ activity.title_tr }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="description_tr">Kısa Açıklama (TR)</label>
                                    <textarea class="form-control" id="description_tr" name="description_tr" rows="3">{{ activity.description_tr or '' }}</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="content_tr">Detaylı İçerik (TR)</label>
                                    <textarea class="form-control" id="content_tr" name="content_tr" rows="6">{{ activity.content_tr or '' }}</textarea>
                                </div>
                            </div>
                            
                            <!-- İngilizce İçerik -->
                            <div class="col-md-6">
                                <h5 class="text-success mb-3">
                                    <i class="fas fa-flag"></i> İngilizce İçerik
                                </h5>
                                
                                <div class="form-group">
                                    <label for="title_en">Başlık (EN) *</label>
                                    <input type="text" class="form-control" id="title_en" name="title_en" 
                                           value="{{ activity.title_en }}" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="description_en">Kısa Açıklama (EN)</label>
                                    <textarea class="form-control" id="description_en" name="description_en" rows="3">{{ activity.description_en or '' }}</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="content_en">Detaylı İçerik (EN)</label>
                                    <textarea class="form-control" id="content_en" name="content_en" rows="6">{{ activity.content_en or '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Aktivite Detayları -->
                        <h5 class="text-info mb-3">
                            <i class="fas fa-info-circle"></i> Aktivite Detayları
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="duration">Süre</label>
                                    <input type="text" class="form-control" id="duration" name="duration" 
                                           value="{{ activity.duration or '' }}">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="difficulty">Zorluk Seviyesi</label>
                                    <select class="form-control" id="difficulty" name="difficulty">
                                        <option value="">Seçiniz</option>
                                        {% for key, value in difficulty_levels.items() %}
                                        <option value="{{ key }}" {% if activity.difficulty == key %}selected{% endif %}>{{ value }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="price">Fiyat (₺)</label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" value="{{ activity.price or '' }}">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_participants">Maks. Katılımcı</label>
                                    <input type="number" class="form-control" id="max_participants" name="max_participants" 
                                           min="1" value="{{ activity.max_participants or '' }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sort_order">Sıralama</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="{{ activity.sort_order }}" min="0">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Durum</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="active" {% if activity.status == 'active' %}selected{% endif %}>Aktif</option>
                                        <option value="inactive" {% if activity.status == 'inactive' %}selected{% endif %}>Pasif</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Mevcut Görseller -->
                        {% if activity.featured_image or activity.gallery_images %}
                        <h5 class="text-warning mb-3">
                            <i class="fas fa-images"></i> Mevcut Görseller
                        </h5>
                        
                        <div class="row mb-4">
                            {% if activity.featured_image %}
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="{{ url_for('static', filename='uploads/activities/' + activity.featured_image) }}" 
                                         class="card-img-top" style="height: 150px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <small class="text-muted">Ana Resim</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% for image in activity.images_list %}
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="{{ url_for('static', filename='uploads/activities/' + image) }}" 
                                         class="card-img-top" style="height: 150px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <button type="button" class="btn btn-sm btn-danger btn-block" 
                                                onclick="removeImage('{{ image }}')">
                                            <i class="fas fa-trash"></i> Sil
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <hr>
                        {% endif %}
                        
                        <!-- Yeni Görseller -->
                        <h5 class="text-warning mb-3">
                            <i class="fas fa-upload"></i> Yeni Görseller Ekle
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="featured_image">Yeni Ana Resim</label>
                                    <input type="file" class="form-control-file" id="featured_image" name="featured_image" 
                                           accept="image/*">
                                    <small class="form-text text-muted">Mevcut ana resmi değiştirmek için</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="gallery_images">Yeni Galeri Resimleri</label>
                                    <input type="file" class="form-control-file" id="gallery_images" name="gallery_images" 
                                           accept="image/*" multiple>
                                    <small class="form-text text-muted">Mevcut galerive eklenecek</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Güncelle
                        </button>
                        <a href="{{ url_for('activities.activity_list') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function removeImage(imageName) {
    if (confirm('Bu resmi silmek istediğinizden emin misiniz?')) {
        fetch("{{ url_for('activities.remove_image', id=activity.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({image: imageName})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Resim silinirken bir hata oluştu: ' + data.message);
            }
        })
        .catch(error => {
            alert('Bir hata oluştu: ' + error);
        });
    }
}
</script>
{% endblock %}
