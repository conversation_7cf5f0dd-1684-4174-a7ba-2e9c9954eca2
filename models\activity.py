from datetime import datetime
from models.setting import db
from slugify import slugify
import os
from flask import current_app

class Activity(db.Model):
    __tablename__ = 'activity'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Türkçe içerik
    title_tr = db.Column(db.String(200), nullable=False)
    description_tr = db.Column(db.Text)
    content_tr = db.Column(db.Text)  # Detaylı içerik
    
    # İngilizce içerik
    title_en = db.Column(db.String(200), nullable=False)
    description_en = db.Column(db.Text)
    content_en = db.Column(db.Text)  # Detaylı içerik
    
    # Ortak alanlar
    slug = db.Column(db.String(200), unique=True)
    featured_image = db.Column(db.String(200))  # Ana resim
    gallery_images = db.Column(db.Text)  # Virgülle ayrılmış resim dosya adları
    duration = db.Column(db.String(100))  # Süre (örn: "2 saat", "Yarım gün")
    difficulty = db.Column(db.String(50))  # Z<PERSON>luk seviyesi
    price = db.Column(db.Float)  # Fiyat
    max_participants = db.Column(db.Integer)  # Maksimum katılımcı sayısı
    status = db.Column(db.String(20), default='active')  # active, inactive
    sort_order = db.Column(db.Integer, default=0)  # Sıralama
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    DIFFICULTY_LEVELS = {
        'easy': 'Kolay',
        'medium': 'Orta',
        'hard': 'Zor',
        'extreme': 'Çok Zor'
    }

    def get_difficulty_display(self):
        return self.DIFFICULTY_LEVELS.get(self.difficulty, self.difficulty)

    def __init__(self, *args, **kwargs):
        super(Activity, self).__init__(*args, **kwargs)
        if self.title_tr:
            self.generate_slug()

    def generate_slug(self):
        """Başlıktan slug oluştur"""
        if not self.slug:
            base_slug = slugify(self.title_tr)
            slug = base_slug
            counter = 1
            while Activity.query.filter_by(slug=slug).first() is not None:
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug

    def __repr__(self):
        return f'<Activity {self.title_tr}>'

    def get_title(self, lang='tr'):
        """Dile göre başlık getir"""
        return getattr(self, f'title_{lang}')

    def get_description(self, lang='tr'):
        """Dile göre açıklama getir"""
        return getattr(self, f'description_{lang}')

    def get_content(self, lang='tr'):
        """Dile göre içerik getir"""
        return getattr(self, f'content_{lang}')

    def remove_image(self, image_name):
        """Belirtilen resmi galeri listesinden kaldır"""
        if not self.gallery_images:
            return False
        
        images = self.images_list
        if image_name in images:
            images.remove(image_name)
            self.gallery_images = ','.join(images)
            return True
        return False

    @property
    def images_list(self):
        """Galeri resimlerini liste olarak döndür"""
        if self.gallery_images:
            return [img.strip() for img in self.gallery_images.split(',') if img.strip()]
        return []

    @property
    def all_images(self):
        """Tüm resimleri liste olarak döndür (ana resim + galeri)"""
        images = []
        if self.featured_image:
            images.append(self.featured_image)
        images.extend(self.images_list)
        return images

    @classmethod
    def get_active_activities(cls, limit=None):
        """Aktif aktiviteleri getir"""
        query = cls.query.filter_by(status='active').order_by(
            cls.sort_order.asc(), cls.created_at.desc()
        )
        if limit:
            query = query.limit(limit)
        return query.all()

    @classmethod
    def get_featured_activities(cls, limit=4):
        """Ana sayfada gösterilecek öne çıkan aktiviteler"""
        return cls.query.filter_by(status='active').order_by(
            cls.sort_order.asc(), cls.created_at.desc()
        ).limit(limit).all()
