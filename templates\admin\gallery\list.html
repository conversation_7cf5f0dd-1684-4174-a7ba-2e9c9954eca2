{% extends 'admin/base.html' %}

{% block breadcrumb %}<PERSON><PERSON>{% endblock %}
{% block page_title %}<PERSON><PERSON>{% endblock %}
{% block page_subtitle %}Tüm galeri öğelerini yönetin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <!-- Üst <PERSON> -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h2 class="text-lg font-medium text-gray-900"><PERSON><PERSON></h2>
            <p class="mt-1 text-sm text-gray-600">Galerinizdeki tüm resimler burada listelenir.</p>
        </div>
        <div class="flex space-x-2">
            <a href="{{ url_for('gallery.admin_gallery_bulk_upload') }}"
               class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                <i class="fas fa-cloud-upload-alt mr-2"></i>
                <PERSON><PERSON>
            </a>
            <a href="{{ url_for('gallery.admin_gallery_create') }}"
               class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Yeni Ekle
            </a>
        </div>
    </div>

    <!-- Galeri Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for gallery in galleries %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Resim -->
            <div class="aspect-w-16 aspect-h-9">
                <img src="{{ url_for('static', filename='uploads/gallery/' + gallery.image) }}"
                     alt="{{ gallery.title }}"
                     class="w-full h-48 object-cover"
                     onerror="this.src='data:image/svg+xml;charset=UTF-8,<svg width=\'200\' height=\'200\' xmlns=\'http://www.w3.org/2000/svg\'><rect width=\'200\' height=\'200\' fill=\'%23f3f4f6\'/><text x=\'50%\' y=\'50%\' font-size=\'16\' text-anchor=\'middle\' fill=\'%236b7280\'>Resim Bulunamadı</text></svg>'">
            </div>

            <!-- Bilgiler -->
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-medium text-gray-900">{{ gallery.title }}</h3>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-bold rounded-full">
                        #{{ gallery.order }}
                    </span>
                </div>
                <p class="mt-1 text-sm text-gray-600">{{ gallery.description }}</p>

                <div class="mt-3 flex items-center space-x-2">
                    <span class="px-2 py-1 text-xs font-medium rounded-full
                        {% if gallery.active %}
                            bg-green-100 text-green-800
                        {% else %}
                            bg-red-100 text-red-800
                        {% endif %}">
                        {{ 'Aktif' if gallery.active else 'Pasif' }}
                    </span>
                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                        {{ gallery.category }}
                    </span>
                </div>

                <!-- Sıralama Düzenleme -->
                <div class="mt-3 flex items-center space-x-2">
                    <label class="text-sm text-gray-600">Sıra:</label>
                    <input type="number"
                           value="{{ gallery.order }}"
                           class="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                           onchange="updateOrder({{ gallery.id }}, this.value)">
                </div>

                <!-- İşlem Butonları -->
                <div class="mt-4 flex justify-end space-x-2">
                    <a href="{{ url_for('gallery.admin_gallery_edit', id=gallery.id) }}"
                       class="px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-50 rounded">
                        <i class="fas fa-edit"></i>
                    </a>
                    <form action="{{ url_for('gallery.admin_gallery_delete', id=gallery.id) }}"
                          method="POST" class="inline"
                          onsubmit="return confirm('Bu öğeyi silmek istediğinizden emin misiniz?');">
                        <button type="submit" class="px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function updateOrder(galleryId, newOrder) {
    fetch('{{ url_for("gallery.admin_gallery_update_order") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: galleryId,
            order: newOrder
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Başarılı güncelleme için sayfayı yenile
            setTimeout(() => {
                location.reload();
            }, 500);
        } else {
            alert('Hata: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Bir hata oluştu');
    });
}
</script>
{% endblock %}