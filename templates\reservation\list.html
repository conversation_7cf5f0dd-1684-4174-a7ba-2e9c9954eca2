{% extends "base.html" %}

{% block title %}{{ _('Rezervasyonlarım') }}{% endblock title %}

{% block head %}
<style>
    /* <PERSON><PERSON> stiller */
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 16px;
    }
    
    .page-title {
        font-size: 2rem;
        color: #333;
        margin-bottom: 1.5rem;
        font-weight: 600;
        text-align: center;
    }
    
    .page-description {
        text-align: center;
        max-width: 600px;
        margin: 0 auto 2rem;
        color: #666;
    }
    
    /* Arama ve filtreleme */
    .search-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .search-form {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .search-input {
        flex: 1;
        min-width: 200px;
        padding: 0.75rem 1rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.9rem;
    }
    
    .search-button {
        padding: 0.75rem 1.5rem;
        background-color: #C6A87D;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .search-button:hover {
        background-color: #b39669;
    }
    
    /* Rezervasyon kartları */
    .reservation-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }
    
    .reservation-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .reservation-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .reservation-header {
        padding: 1.25rem;
        background-color: #f9f9f9;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .reservation-code {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
    }
    
    .reservation-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-confirmed {
        background-color: #d1fae5;
        color: #064e3b;
    }
    
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-cancelled {
        background-color: #fee2e2;
        color: #b91c1c;
    }
    
    .reservation-body {
        padding: 1.25rem;
    }
    
    .reservation-info-item {
        margin-bottom: 0.75rem;
        display: flex;
        align-items: flex-start;
    }
    
    .reservation-info-icon {
        width: 18px;
        height: 18px;
        margin-right: 0.75rem;
        margin-top: 0.25rem;
        color: #C6A87D;
    }
    
    .reservation-info-content {
        flex: 1;
    }
    
    .reservation-info-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 0.1rem;
    }
    
    .reservation-info-value {
        font-size: 0.95rem;
        color: #333;
    }
    
    .reservation-footer {
        padding: 1rem 1.25rem;
        background-color: #f9f9f9;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .price-amount {
        font-size: 1.1rem;
        font-weight: 600;
        color: #C6A87D;
    }
    
    /* Butonlar */
    .button-group {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.6rem 1rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-primary {
        background-color: #C6A87D;
        color: white;
        border: none;
    }
    
    .btn-primary:hover {
        background-color: #b39669;
    }
    
    .btn-outline {
        background-color: transparent;
        border: 1px solid #C6A87D;
        color: #C6A87D;
    }
    
    .btn-outline:hover {
        background-color: #f9f7f4;
    }
    
    .btn-danger {
        background-color: transparent;
        border: 1px solid #ef4444;
        color: #ef4444;
    }
    
    .btn-danger:hover {
        background-color: #fee2e2;
    }
    
    .btn-icon {
        width: 16px;
        height: 16px;
        margin-right: 0.4rem;
    }
    
    /* Boş durum */
    .empty-state {
        text-align: center;
        padding: 3rem 0;
    }
    
    .empty-state-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        color: #C6A87D;
        opacity: 0.7;
    }
    
    .empty-state-text {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 1.5rem;
    }
    
    /* Modal Stili */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
        align-items: center;
        justify-content: center;
    }
    
    .modal.active {
        display: flex;
    }
    
    .modal-content {
        background-color: #fff;
        border-radius: 8px;
        width: 100%;
        max-width: 450px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        transform: translateY(-20px);
        transition: transform 0.3s ease;
        animation: modal-appear 0.3s forwards;
    }
    
    @keyframes modal-appear {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .modal-header {
        padding: 1.25rem;
        border-bottom: 1px solid #eee;
    }
    
    .modal-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .modal-body {
        padding: 1.25rem;
    }
    
    .modal-footer {
        padding: 1.25rem;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
    }
    
    .close-modal {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 24px;
        height: 24px;
        background: none;
        border: none;
        cursor: pointer;
        color: #666;
        transition: color 0.2s;
    }
    
    .close-modal:hover {
        color: #333;
    }
    
    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        gap: 0.5rem;
    }
    
    .page-item {
        display: inline-flex;
    }
    
    .page-link {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: 1px solid #ddd;
        color: #666;
        text-decoration: none;
        transition: all 0.2s;
    }
    
    .page-link:hover {
        background-color: #f9f9f9;
        border-color: #ccc;
    }
    
    .page-item.active .page-link {
        background-color: #C6A87D;
        border-color: #C6A87D;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-gray-50 py-12 min-h-screen">
    <div class="container">
        <h1 class="page-title">{{ _('Rezervasyonlarım') }}</h1>
        <p class="page-description">{{ _('Rezervasyonlarınızı görüntüleyebilir, düzenleyebilir ve iptal edebilirsiniz.') }}</p>
        
        <!-- Arama ve Filtreleme -->
        <div class="search-container">
            <form class="search-form" id="searchForm">
                <input type="text" name="code" class="search-input" placeholder="{{ _('Rezervasyon kodu ile ara...') }}" value="{{ code or '' }}">
                <button type="submit" class="search-button">{{ _('Ara') }}</button>
            </form>
        </div>
        
        <!-- Rezervasyon Listesi -->
        <div class="reservation-grid">
            {% if reservations %}
                {% for reservation in reservations %}
                <div class="reservation-card">
                    <div class="reservation-header">
                        <span class="reservation-code">{{ _('Rezervasyon') }} #{{ reservation.reservation_code }}</span>
                        <span class="reservation-status 
                            {% if reservation.status == 'confirmed' %}status-confirmed
                            {% elif reservation.status == 'pending' %}status-pending
                            {% elif reservation.status == 'cancelled' %}status-cancelled{% endif %}">
                            {% if reservation.status == 'confirmed' %}{{ _('Onaylandı') }}
                            {% elif reservation.status == 'pending' %}{{ _('Beklemede') }}
                            {% elif reservation.status == 'cancelled' %}{{ _('İptal Edildi') }}{% endif %}
                        </span>
                    </div>
                    
                    <div class="reservation-body">
                        <!-- Oda Bilgisi -->
                        <div class="reservation-info-item">
                            <svg class="reservation-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            <div class="reservation-info-content">
                                <div class="reservation-info-label">{{ _('Oda Tipi') }}</div>
                                <div class="reservation-info-value">{{ reservation.room_type }}</div>
                            </div>
                        </div>
                        
                        <!-- Tarih Bilgisi -->
                        <div class="reservation-info-item">
                            <svg class="reservation-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <div class="reservation-info-content">
                                <div class="reservation-info-label">{{ _('Tarih') }}</div>
                                <div class="reservation-info-value">
                                    {{ reservation.check_in.strftime('%d.%m.%Y') }} - {{ reservation.check_out.strftime('%d.%m.%Y') }}
                                    ({{ (reservation.check_out - reservation.check_in).days }} {{ _('Gece') }})
                                </div>
                            </div>
                        </div>
                        
                        <!-- Misafir Bilgisi -->
                        <div class="reservation-info-item">
                            <svg class="reservation-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <div class="reservation-info-content">
                                <div class="reservation-info-label">{{ _('Misafir') }}</div>
                                <div class="reservation-info-value">{{ reservation.name }} {{ reservation.surname }}</div>
                            </div>
                        </div>
                        
                        <!-- İletişim Bilgisi -->
                        <div class="reservation-info-item">
                            <svg class="reservation-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <div class="reservation-info-content">
                                <div class="reservation-info-label">{{ _('İletişim') }}</div>
                                <div class="reservation-info-value">{{ reservation.email }}<br>{{ reservation.phone }}</div>
                            </div>
                        </div>
                        
                        <!-- Ödeme Bilgisi -->
                        <div class="reservation-info-item">
                            <svg class="reservation-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            <div class="reservation-info-content">
                                <div class="reservation-info-label">{{ _('Ödeme Durumu') }}</div>
                                <div class="reservation-info-value">
                                    {% if reservation.payment_status == 'paid' %}
                                        <span class="text-green-600">{{ _('Ödenmiş') }}</span>
                                    {% elif reservation.payment_status == 'processing' %}
                                        <span class="text-yellow-600">{{ _('İşleniyor') }}</span>
                                    {% else %}
                                        <span class="text-red-600">{{ _('Ödenmemiş') }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="reservation-footer">
                        <div class="price-amount">
                            {% if reservation.currency == 'TRY' %}
                                {{ reservation.amount|round|int }} ₺
                            {% elif reservation.currency == 'USD' %}
                                ${{ reservation.amount|round|int }}
                            {% elif reservation.currency == 'EUR' %}
                                €{{ reservation.amount|round|int }}
                            {% else %}
                                {{ reservation.amount|round|int }} {{ reservation.currency }}
                            {% endif %}
                        </div>
                        
                        <div class="button-group">
                            {% if reservation.status != 'cancelled' and reservation.payment_status != 'paid' %}
                                <button class="btn btn-danger cancel-reservation" data-code="{{ reservation.reservation_code }}">
                                    <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    {{ _('İptal Et') }}
                                </button>
                            {% endif %}
                            
                            {% if reservation.status == 'confirmed' and reservation.payment_status == 'paid' %}
                                <a href="{{ url_for('reservation.download_reservation_pdf', code=reservation.reservation_code) }}" class="btn btn-outline">
                                    <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                    </svg>
                                    {{ _('PDF İndir') }}
                                </a>
                            {% endif %}
                            
                            <a href="{{ url_for('rooms.room_detail', slug=reservation.room.slug) if reservation.room else '#' }}" class="btn btn-primary">
                                <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                {{ _('Odayı Görüntüle') }}
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Boş Durum -->
                <div class="empty-state col-span-full">
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <div class="empty-state-text">{{ _('Henüz bir rezervasyonunuz bulunmamaktadır.') }}</div>
                    <a href="{{ url_for('rooms.room_index') }}" class="btn btn-primary">
                        {{ _('Odaları Görüntüle') }}
                    </a>
                </div>
            {% endif %}
        </div>
        
        <!-- Pagination -->
        {% if reservations and pagination.pages > 1 %}
        <div class="pagination">
            {% if pagination.has_prev %}
            <div class="page-item">
                <a class="page-link" href="{{ url_for('reservation.reservation_list', page=pagination.prev_num, code=code) }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
            </div>
            {% endif %}
            
            {% for page in pagination.iter_pages() %}
                {% if page %}
                <div class="page-item {% if page == pagination.page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('reservation.reservation_list', page=page, code=code) }}">{{ page }}</a>
                </div>
                {% else %}
                <div class="page-item">
                    <span class="page-link">...</span>
                </div>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <div class="page-item">
                <a class="page-link" href="{{ url_for('reservation.reservation_list', page=pagination.next_num, code=code) }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- İptal Onay Modalı -->
<div class="modal" id="cancelModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">{{ _('Rezervasyon İptali') }}</h3>
            <button class="close-modal" id="closeModal">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="modal-body">
            <p>{{ _('Bu rezervasyonu iptal etmek istediğinizden emin misiniz? Bu işlem geri alınamaz.') }}</p>
            <input type="hidden" id="reservationCode">
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" id="cancelNo">{{ _('Vazgeç') }}</button>
            <button class="btn btn-danger" id="cancelYes">{{ _('İptal Et') }}</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal İşlemleri
    const modal = document.getElementById('cancelModal');
    const closeModal = document.getElementById('closeModal');
    const cancelNo = document.getElementById('cancelNo');
    const cancelYes = document.getElementById('cancelYes');
    const reservationCodeInput = document.getElementById('reservationCode');
    const cancelButtons = document.querySelectorAll('.cancel-reservation');
    
    // İptal butonlarına olay dinleyicileri ekle
    cancelButtons.forEach(button => {
        button.addEventListener('click', function() {
            const code = this.getAttribute('data-code');
            reservationCodeInput.value = code;
            modal.classList.add('active');
        });
    });
    
    // Modal kapatma
    function closeModalFunc() {
        modal.classList.remove('active');
    }
    
    closeModal.addEventListener('click', closeModalFunc);
    cancelNo.addEventListener('click', closeModalFunc);
    
    // İptal onaylama
    cancelYes.addEventListener('click', function() {
        const code = reservationCodeInput.value;
        if (!code) return;
        
        // AJAX isteği ile iptal işlemini gerçekleştir
        fetch(`/reservation/cancel/${code}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.reload(); // Sayfayı yenile
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('İptal işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.');
        })
        .finally(() => {
            closeModalFunc(); // Modal'ı kapat
        });
    });
});
</script>
{% endblock %} 