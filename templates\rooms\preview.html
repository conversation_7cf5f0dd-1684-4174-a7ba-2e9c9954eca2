{% extends "base.html" %}

{% block title %}{{ get_lang_text(room, 'title') }} - {% if current_language == 'tr' %}Oda Önizleme{% else %}Room Preview{% endif %}{% endblock title %}

{% block head %}
<!-- Flickity CSS -->
<link rel="stylesheet" href="https://unpkg.com/flickity@2/dist/flickity.min.css">
<!-- Flickity JavaScript -->
<script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js"></script>

<style>
/* Ana slider container */
.carousel-wrapper {
    max-width: 1400px;
    margin: 0 auto 20px;
    padding: 0 10px;
}

@media (min-width: 640px) {
    .carousel-wrapper {
        margin-bottom: 30px;
        padding: 0 20px;
    }
}

.carousel {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.carousel-cell {
    width: 100%;
    height: 300px;
}

@media (min-width: 640px) {
    .carousel-cell {
        height: 400px;
    }
}

@media (min-width: 768px) {
    .carousel-cell {
        height: 500px;
    }
}

@media (min-width: 1024px) {
    .carousel-cell {
        height: 600px;
    }
}

.carousel-cell img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Thumbnail grid */
.gallery-thumbs {
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 10px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
}

@media (min-width: 640px) {
    .gallery-thumbs {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
        padding: 0 20px;
        margin: 30px auto;
    }
}

.gallery-thumb {
    position: relative;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-thumb:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.gallery-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.gallery-thumb:hover img {
    opacity: 1;
}

/* Slider navigasyon butonları */
.flickity-button {
    width: 36px !important;
    height: 36px !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

@media (min-width: 640px) {
    .flickity-button {
        width: 44px !important;
        height: 44px !important;
    }
}

.flickity-prev-next-button.previous { left: 10px; }
.flickity-prev-next-button.next { right: 10px; }

@media (min-width: 640px) {
    .flickity-prev-next-button.previous { left: 20px; }
    .flickity-prev-next-button.next { right: 20px; }
}

/* Modal stilleri */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    padding: 40px;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 1001;
}

.modal img {
    max-height: 80vh;
    max-width: 90vw;
    object-fit: contain;
}

/* Aktif thumbnail stili */
.gallery-thumb.is-selected {
    border: 2px solid #C6A87D;
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(198, 168, 125, 0.2);
}

.gallery-thumb.is-selected img {
    opacity: 1;
}

/* Mobil için slider düzenlemeleri */
@media (max-width: 768px) {
    .flickity-button {
        width: 36px !important;
        height: 36px !important;
        opacity: 0.8;
    }

    .flickity-button:hover {
        opacity: 1;
    }

    .flickity-prev-next-button.previous { left: 10px; }
    .flickity-prev-next-button.next { right: 10px; }

    /* Mobilde nokta navigasyonu göster */
    .flickity-page-dots {
        bottom: 10px;
    }

    .flickity-page-dots .dot {
        width: 8px;
        height: 8px;
        margin: 0 5px;
        background: rgba(255, 255, 255, 0.8);
    }

    .flickity-page-dots .dot.is-selected {
        background: white;
    }

    /* Mobilde thumbnail grid düzenlemesi */
    .gallery-thumbs {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        padding: 0 10px;
    }
}

/* Oda bilgileri bölümü */
.room-info-section {
    padding: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 640px) {
    .room-info-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

/* Özellikler grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

@media (min-width: 640px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}

/* Rezervasyon butonu */
.reservation-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
}

@media (min-width: 640px) {
    .reservation-button {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Üst başlık kısmı -->
    <div class="container mx-auto px-4 sm:px-[2.5rem] pt-32 sm:pt-40 pb-8 sm:pb-12">
        <!-- Header Bölümü -->
        <div class="flex flex-col items-center justify-between gap-4 sm:gap-6">
            <!-- Geri Dön Butonu -->
            <div class="w-full text-center sm:text-left">
                <a href="javascript:void(0);" 
                   class="inline-flex items-center text-gold hover:opacity-80 transition-colors group">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2 transform group-hover:-translate-x-1 transition-transform" 
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    <span class="font-great-vibes text-2xl sm:text-3xl md:text-4xl" data-translate="back_to_rooms">Geri</span>
                </a>
            </div>

            <!-- Oda Başlığı -->
            <div class="text-center w-full">
                <span class="text-gold font-great-vibes text-3xl sm:text-4xl md:text-5xl block">
                    {{ get_lang_text(room, 'title') }}
                </span>
                <div class="flex items-center justify-center mt-2 sm:mt-3">
                    <div class="h-[2px] w-6 sm:w-8 md:w-12 bg-gold"></div>
                    <div class="mx-2 sm:mx-3 md:mx-4 text-gold text-lg sm:text-xl md:text-2xl">⚜</div>
                    <div class="h-[2px] w-6 sm:w-8 md:w-12 bg-gold"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ana Slider -->
    <div class="carousel-wrapper">
        <div class="carousel" data-flickity='{ 
            "cellAlign": "left", 
            "contain": true,
            "wrapAround": true,
            "autoPlay": 3000,
            "pauseAutoPlayOnHover": true,
            "prevNextButtons": true,
            "pageDots": false
        }'>
            {% if room.featured_image %}
            <div class="carousel-cell">
                <img src="{{ url_for('static', filename='uploads/rooms/' + room.featured_image) }}"
                     alt="{{ get_lang_text(room, 'title') }}">
            </div>
            {% endif %}
            
            {% for image in room.images_list %}
            <div class="carousel-cell">
                <img src="{{ url_for('static', filename='uploads/rooms/' + image) }}"
                     alt="{{ get_lang_text(room, 'title') }}">
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Thumbnails -->
    <div class="gallery-thumbs">
        {% if room.featured_image %}
        <div class="gallery-thumb" data-image="{{ url_for('static', filename='uploads/rooms/' + room.featured_image) }}">
            <img src="{{ url_for('static', filename='uploads/rooms/' + room.featured_image) }}"
                 alt="{{ get_lang_text(room, 'title') }}">
        </div>
        {% endif %}
        
        {% for image in room.images_list %}
        <div class="gallery-thumb" data-image="{{ url_for('static', filename='uploads/rooms/' + image) }}">
            <img src="{{ url_for('static', filename='uploads/rooms/' + image) }}"
                 alt="{{ get_lang_text(room, 'title') }}">
        </div>
        {% endfor %}
    </div>

    <!-- Alt kısım -->
    <div class="max-w-[1400px] mx-auto px-4 sm:px-8">
        <!-- Oda Bilgileri -->
        <div class="bg-white rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-4 sm:gap-6">
                <!-- Temel Özellikler -->
                <div class="md:col-span-4 bg-gray-50/30 rounded-lg p-4 sm:p-6">
                    <div class="border-b border-gray-300 pb-2 sm:pb-3 mb-4 sm:mb-6">
                        <h3 class="text-base sm:text-lg font-light text-gray-700" data-translate="basic_features">
                            Temel Özellikler
                        </h3>
                    </div>
                    <div class="space-y-3 sm:space-y-4">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-ruler-combined text-gold"></i>
                            <div>
                                <p class="text-xs text-gray-400" data-translate="room_size">Oda Boyutu</p>
                                <p class="font-light">{{ room.size }}m²</p>
                            </div>
                        </div>

                        <div class="flex items-center gap-3">
                            <i class="fas fa-user-friends text-gold"></i>
                            <div>
                                <p class="text-xs text-gray-400" data-translate="max_capacity">Maksimum Kapasite</p>
                                <p class="font-light">{{ room.capacity }} <span data-translate="person">Kişi</span></p>
                            </div>
                        </div>

                        <div class="flex items-center gap-3">
                            <i class="fas fa-mountain text-gold"></i>
                            <div>
                                <p class="text-xs text-gray-400" data-translate="view">Manzara</p>
                                <p class="font-light">{{ room.get_view_type_display() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Oda Olanakları -->
                {% if room.features %}
                <div class="md:col-span-8 bg-gray-50/30 rounded-lg p-4 sm:p-6">
                    <div class="border-b border-gray-300 pb-2 sm:pb-3 mb-4 sm:mb-6">
                        <h3 class="text-base sm:text-lg font-light text-gray-700" data-translate="room_features">
                            Oda Olanakları
                        </h3>
                    </div>
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3">
                        {% for feature in room.features %}
                        <div class="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-white/50 rounded-lg">
                            <i class="{{ feature.icon }} text-gold text-sm sm:text-base"></i>
                            <span class="text-gray-600 text-sm sm:text-base">{{ feature.name }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Rezervasyon Butonu -->
        <div class="text-center mb-8 sm:mb-12">
            <a href="{{ settings.reservation_link or '#' }}" 
               onclick="countReservationClick(event)"
               class="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gold hover:bg-gold/90 text-white 
                      rounded-full transition-all duration-300 group space-x-2 text-sm sm:text-base tracking-wider font-medium"
               {% if settings.reservation_link %}target="_blank"{% endif %}>
                <i class="fas fa-concierge-bell text-base sm:text-lg"></i>
                <span data-translate="make_reservation">REZERVASYON YAP</span>
                <svg class="w-4 h-4 sm:w-5 sm:h-5 transform group-hover:translate-x-1 transition-transform" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
            </a>
        </div>

        <!-- Oda Açıklaması -->
        <div class="bg-white rounded-xl shadow-sm p-4 sm:p-6 md:p-8 mb-12 sm:mb-16 md:mb-24">
            <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-4 sm:mb-6" data-translate="about_room">
                Oda Hakkında
            </h2>
            <div class="prose max-w-none prose-sm sm:prose-base md:prose-lg">
                {{ get_lang_text(room, 'description')|safe }}
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div id="imageModal" class="modal">
    <span class="modal-close" onclick="closeModal()">&times;</span>
    
    <!-- Sol Ok -->
    <button class="modal-nav modal-prev" onclick="changeImage(-1)">
        <i class="fas fa-chevron-left"></i>
    </button>
    
    <img id="modalImage" src="" alt="Büyük Görsel" style="max-height: 90vh; max-width: 90vw; object-fit: contain;">
    
    <!-- Sağ Ok -->
    <button class="modal-nav modal-next" onclick="changeImage(1)">
        <i class="fas fa-chevron-right"></i>
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ana slider'ı başlat
    var flkty = new Flickity('.carousel', {
        cellAlign: 'left',
        contain: true,
        wrapAround: true,
        autoPlay: 3000,
        pauseAutoPlayOnHover: true,
        prevNextButtons: true,
        pageDots: false
    });

    // Thumbnail'ları seç
    const thumbnails = document.querySelectorAll('.gallery-thumb');
    
    // İlk thumbnail'ı seçili yap
    thumbnails[0]?.classList.add('is-selected');

    // Thumbnail tıklama işlemleri
    thumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', function(e) {
            // Slider'ı seçilen resme getir
            flkty.select(index);
            
            // Çift tıklama kontrolü için
            if (e.detail === 2) {
                // Modal'ı aç
                openModal(this.dataset.image);
            }
        });
    });

    // Ana slider'daki resimlere tıklama işlemi ekle
    document.querySelectorAll('.carousel-cell img').forEach(img => {
        img.addEventListener('click', function() {
            openModal(this.src);
        });
    });

    // Slider değiştiğinde thumbnail'ı güncelle
    flkty.on('change', function(index) {
        // Tüm thumbnail'lardan seçili sınıfını kaldır
        thumbnails.forEach(thumb => thumb.classList.remove('is-selected'));
        // Aktif thumbnail'a seçili sınıfını ekle
        thumbnails[index]?.classList.add('is-selected');
    });

    // Otomatik geçişte de thumbnail'ı güncelle
    flkty.on('select', function() {
        thumbnails.forEach(thumb => thumb.classList.remove('is-selected'));
        thumbnails[flkty.selectedIndex]?.classList.add('is-selected');
    });

    // Geri dön butonunu seç
    const backButton = document.querySelector('.ml-0.md\\:ml-11 a');
    
    backButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        const previousPage = document.referrer;
        const roomCategoryUrl = '{{ url_for("rooms.rooms_by_category", slug=room.room_category.slug) if room.room_category else "" }}';
        const roomIndexUrl = '{{ url_for("rooms.room_index") }}';
        
        if (previousPage) {
            window.history.back();
        } else {
            window.location.href = roomCategoryUrl || roomIndexUrl;
        }
    });
});

function openModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    modal.classList.add('active');
    modalImg.src = imageSrc;
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    const modal = document.getElementById('imageModal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function changeImage(direction) {
    const flkty = document.querySelector('.carousel');
    const selectedIndex = flkty.selectedIndex;
    const newIndex = (selectedIndex + direction + flkty.cells.length) % flkty.cells.length;
    flkty.select(newIndex);
}

// ESC tuşu ile modalı kapatma ve yön tuşlarıyla gezinme
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    } else if (e.key === 'ArrowLeft') {
        changeImage(-1);
    } else if (e.key === 'ArrowRight') {
        changeImage(1);
    }
});
</script>
{% endblock content %} 