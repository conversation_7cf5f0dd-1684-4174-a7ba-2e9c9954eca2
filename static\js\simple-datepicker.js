document.addEventListener('DOMContentLoaded', function() {
    // <PERSON>rih seçici için basit bir uygulama
    initSimpleDatepicker();
    
    // Para birimi seçimini başlat
    initCurrencySelector();

    // Form gönderimini kontrol et
    const form = document.getElementById('reservationForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const checkIn = document.getElementById('checkIn').value;
            const checkOut = document.getElementById('checkOut').value;
            
            if (!checkIn || !checkOut) {
                e.preventDefault();
                alert('Lütfen giriş ve çıkış tarihlerini seçin');
            }
        });
    }
});

// Global değişkenler
let currentCurrency = 'EUR'; // Varsayılan para birimi
const conversionRates = {
    'EUR': { 'USD': 1.08, 'TRY': 35.5, 'EUR': 1 },
    'USD': { 'EUR': 0.93, 'TRY': 33, 'USD': 1 },
    'TRY': { 'EUR': 0.028, 'USD': 0.03, 'TRY': 1 }
};

function initCurrencySelector() {
    // Para birimi butonlarını bul
    const currencyButtons = document.querySelectorAll('.currency-btn');
    if (!currencyButtons.length) return;
    
    // EUR butonunu varsayılan olarak aktif yap
    const eurButton = document.querySelector('.currency-btn[data-currency="EUR"]');
    if (eurButton) {
        eurButton.classList.add('active');
    }
    
    // Her butona tıklama olayı ekle
    currencyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const newCurrency = this.getAttribute('data-currency');
            if (newCurrency === currentCurrency) return;
            
            // Aktif butonu güncelle
            currencyButtons.forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
            
            // Para birimini güncelle
            changeCurrency(newCurrency);
        });
    });
}

function changeCurrency(newCurrency) {
    const form = document.getElementById('reservationForm');
    if (!form) return;
    
    const originalCurrency = form.dataset.roomCurrency;
    const originalPrice = parseFloat(form.dataset.roomPrice);
    
    // Gecelik fiyatı güncelle
    const nightlyPriceElement = document.getElementById('nightlyPrice');
    if (nightlyPriceElement) {
        const convertedPrice = convertPrice(originalPrice, originalCurrency, newCurrency);
        nightlyPriceElement.textContent = formatPrice(convertedPrice, newCurrency);
    }
    
    // Toplam fiyatı güncelle
    const checkIn = document.getElementById('checkIn').value;
    const checkOut = document.getElementById('checkOut').value;
    
    if (checkIn && checkOut) {
        const checkInDate = new Date(checkIn);
        const checkOutDate = new Date(checkOut);
        const convertedPrice = convertPrice(originalPrice, originalCurrency, newCurrency);
        
        updateTotalPrice(checkInDate, checkOutDate, convertedPrice, newCurrency);
    }
    
    // Güncel para birimini kaydet
    currentCurrency = newCurrency;
}

function convertPrice(price, fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) return price;
    
    // Doğrudan çevirme oranı varsa kullan
    if (conversionRates[fromCurrency] && conversionRates[fromCurrency][toCurrency]) {
        return price * conversionRates[fromCurrency][toCurrency];
    }
    
    // Doğrudan oran yoksa EUR üzerinden çevir
    return price * conversionRates[fromCurrency]['EUR'] * conversionRates['EUR'][toCurrency];
}

function formatPrice(price, currency) {
    price = Math.round(price);
    
    switch (currency) {
        case 'TRY': return `${price} ₺`;
        case 'USD': return `$${price}`;
        case 'EUR': return `€${price}`;
        default: return `${price} ${currency}`;
    }
}

function initSimpleDatepicker() {
    // Gerekli elementleri bul
    const dateRangeInput = document.getElementById('dateRange');
    const checkInInput = document.getElementById('checkIn');
    const checkOutInput = document.getElementById('checkOut');
    const totalPriceElement = document.getElementById('totalPrice');
    const form = document.getElementById('reservationForm');
    
    // Form yoksa çık
    if (!form || !dateRangeInput) return;
    
    // Form verilerini al
    const today = form.dataset.today || new Date().toISOString().split('T')[0];
    const roomPrice = parseFloat(form.dataset.roomPrice) || 0;
    const roomCurrency = form.dataset.roomCurrency || 'EUR';
    
    // Flatpickr'ı doğrudan input üzerinde başlat
    flatpickr(dateRangeInput, {
        mode: "range",
        dateFormat: "d.m.Y",
        minDate: today,
        locale: "tr",
        disableMobile: "true",
        onChange: function(selectedDates, dateStr) {
            if (selectedDates.length === 2) {
                const checkIn = selectedDates[0];
                const checkOut = selectedDates[1];
                
                // Gizli inputları güncelle
                checkInInput.value = formatDate(checkIn);
                checkOutInput.value = formatDate(checkOut);
                
                // Toplam fiyatı hesapla - mevcut para birimini kullan
                const originalPrice = parseFloat(form.dataset.roomPrice) || 0;
                const originalCurrency = form.dataset.roomCurrency || 'EUR';
                const convertedPrice = convertPrice(originalPrice, originalCurrency, currentCurrency);
                
                updateTotalPrice(checkIn, checkOut, convertedPrice, currentCurrency);
                
                console.log("Tarih seçildi:", {
                    checkIn: formatDate(checkIn),
                    checkOut: formatDate(checkOut)
                });
            }
        },
        onReady: function() {
            console.log("Tarih seçici hazır");
        },
        onOpen: function() {
            console.log("Tarih seçici açıldı");
        }
    });
    
    // Debug için konsola bilgi yazdır
    console.log("Tarih seçici başlatıldı");
}

// Tarihi YYYY-MM-DD formatına çevirir
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Toplam fiyatı hesapla ve göster
function updateTotalPrice(checkIn, checkOut, basePrice, currency) {
    const totalPriceElement = document.getElementById('totalPrice');
    if (!totalPriceElement) return;
    
    // Gece sayısını hesapla
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    if (nights <= 0) return;
    
    // Gece sayısını göster
    const nightCountElement = document.getElementById('nightCount');
    if (nightCountElement) {
        nightCountElement.textContent = nights + ' gece';
    }
    
    // Misafir sayısını al
    const guestsSelect = document.getElementById('guests');
    const guests = guestsSelect ? parseInt(guestsSelect.value) || 1 : 1;
    
    // Toplam fiyatı hesapla
    let totalPrice = basePrice * nights;
    
    // Misafir sayısına göre fiyatı güncelle (isteğe bağlı)
    if (guests > 1) {
        totalPrice += (basePrice * 0.5 * (guests - 1)) * nights;
    }
    
    // Fiyatı göster
    totalPriceElement.textContent = formatPrice(totalPrice, currency);
    
    console.log("Fiyat hesaplandı:", {
        nights, guests, basePrice, totalPrice, currency
    });
}

// Misafir sayısı değişikliğini dinle
document.addEventListener('DOMContentLoaded', function() {
    const guestsSelect = document.getElementById('guests');
    const form = document.getElementById('reservationForm');
    
    if (guestsSelect && form) {
        const originalPrice = parseFloat(form.dataset.roomPrice) || 0;
        const originalCurrency = form.dataset.roomCurrency || 'EUR';
        
        guestsSelect.addEventListener('change', function() {
            const checkIn = document.getElementById('checkIn').value;
            const checkOut = document.getElementById('checkOut').value;
            
            if (checkIn && checkOut) {
                const convertedPrice = convertPrice(originalPrice, originalCurrency, currentCurrency);
                
                updateTotalPrice(
                    new Date(checkIn), 
                    new Date(checkOut),
                    convertedPrice,
                    currentCurrency
                );
            }
        });
    }
}); 