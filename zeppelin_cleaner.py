#!/usr/bin/env python3
"""
Zeppelin 2025 - Kapsamlı Temizleme ve Hazırlama Aracı

Bu script:
1. Python önbellek dosyalarını temizler
2. Tüm yüklenen medyaları (resimler, dosyalar) temizler
3. Veritabanını sıfırlar
4. Geçici dosyaları ve logları temizler
5. Boş dizinleri tespit eder

Kullanım:
python zeppelin_cleaner.py [seçenekler]

Seçenekler:
--clean-all         Tüm temizleme işlemlerini gerçekleştirir
--clean-cache       Sadece önbellek ve geçici dosyaları temizler
--clean-media       Sadece yüklenen medya dosyalarını temizler
--clean-db          Sadece veritabanını sıfırlar
--dry-run           Değişiklik yapmadan önce ne yapılacağını gösterir
-y, --yes           Onay sormadan işlemleri gerçekleştirir
"""

import os
import sys
import shutil
import argparse
import sqlite3
from pathlib import Path
import time
from datetime import datetime, timedelta

# Proje kök dizini
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))

# --- Yapılandırma ---

# Temizlenecek klasör türleri
CLEANUP_DIRS = [
    '__pycache__',
    '.pytest_cache',
    '.cache',
    'flask_session',
    '.ipynb_checkpoints',
    'carkifelek',
    'public',
    'logs',
    '.sass-cache',
    'node_modules',
    'dist',
    'build',
    '.coverage',
    '.mypy_cache',
    '__MACOSX'
]

# Temizlenecek dosya uzantıları
CLEANUP_EXTENSIONS = [
    # Python derleme dosyaları
    '.pyc', '.pyo', '.pyd',
    
    # Log ve geçici dosyalar
    '.log', '.tmp', '.bak', '.swp', '.swo', '.~', '.orig',
    
    # Sistem dosyaları
    '.DS_Store', 'Thumbs.db', 'desktop.ini', '.directory',
    
    # IDE ayar dosyaları
    '.idea', '.vscode', '.vs',
    
    # Önbellek ve paket dosyaları
    '.cache', '.deb', '.rpm',
    
    # Medya işleme geçici dosyaları
    '.thm', '.lrv'
]

# Temizlenecek belirli dosyalar
CLEANUP_FILES = [
    'debug.log', 'error.log', 'access.log', 'nohup.out',
    '.env.local', '.env.development.local', '.env.test.local', '.env.production.local',
    'npm-debug.log', 'yarn-debug.log', 'yarn-error.log',
    '.eslintcache'
]

# Temizlikten hariç tutulacak dosya ve dizinler
EXCLUDE_PATHS = [
    'google_analytics_credentials.json',
    'config.py',
    '.gitignore',
    '.git',
    'venv',
    'env',
    'SETUP_GUIDE.md',
    'KURULUM_REHBERI.md',
    'zeppelin_cleaner.py'
]

# Temizlenecek medya dizinleri
MEDIA_DIRS = [
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'activities'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'banners'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'blog'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'food'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'gallery'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'rooms'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'settings'),
    os.path.join(PROJECT_ROOT, 'static', 'uploads', 'sponsors'),
]

# PDF dosyalarının bulunduğu dizin
PDF_DIR = os.path.join(PROJECT_ROOT, 'static', 'pdfs', 'reservations')

# Veritabanı dosyası
DB_FILE = os.path.join(PROJECT_ROOT, 'instance', 'hotel.db')
# Veritabanı yedek dosyası
DB_BACKUP = os.path.join(PROJECT_ROOT, 'instance', 'hotel.db.bak')

# --- Yardımcı Fonksiyonlar ---

def get_human_readable_size(size_bytes):
    """Dosya boyutunu okunabilir formata çevirir"""
    if size_bytes == 0:
        return "0B"
    
    size_name = ("B", "KB", "MB", "GB", "TB")
    i = 0
    
    while size_bytes >= 1024 and i < len(size_name) - 1:
        size_bytes /= 1024
        i += 1
    
    return f"{size_bytes:.2f} {size_name[i]}"

def is_path_excluded(path, exclude_paths):
    """Yolun hariç tutulup tutulmadığını kontrol eder"""
    path = os.path.normpath(path)
    for exclude in exclude_paths:
        exclude = os.path.normpath(exclude)
        if path == exclude or path.startswith(exclude + os.sep):
            return True
    return False

def find_empty_dirs(directory):
    """Boş dizinleri bulur"""
    empty_dirs = []
    for root, dirs, files in os.walk(directory, topdown=False):
        if not dirs and not files and not is_path_excluded(root, EXCLUDE_PATHS):
            empty_dirs.append(root)
    return empty_dirs

def confirm_action(prompt):
    """Kullanıcıdan onay alır"""
    while True:
        reply = input(f"{prompt} [y/n]: ").lower().strip()
        if reply in ['y', 'yes', 'evet', 'e']:
            return True
        if reply in ['n', 'no', 'hayır', 'h']:
            return False
        print("Geçersiz giriş. Lütfen 'y' veya 'n' girin.")

# --- Ana Temizleme Fonksiyonları ---

def cleanup_cache(dry_run=False, non_interactive=False, verbose=False):
    """Gereksiz dosyaları ve önbelleği temizler"""
    print("\n🧹 Önbellek ve Geçici Dosyalar Temizleniyor...")
    
    items_to_delete = []
    
    for root, dirs, files in os.walk(PROJECT_ROOT, topdown=True):
        # Hariç tutulan dizinleri atla
        dirs[:] = [d for d in dirs if not is_path_excluded(os.path.join(root, d), EXCLUDE_PATHS)]
        
        # Temizlenecek dizinleri bul
        for d in list(dirs):
            dir_path = os.path.join(root, d)
            if d in CLEANUP_DIRS and not is_path_excluded(dir_path, EXCLUDE_PATHS):
                items_to_delete.append({'type': 'dir', 'path': dir_path})
                dirs.remove(d)  # Bu dizini taramamak için kaldır
        
        # Temizlenecek dosyaları bul
        for f in files:
            file_path = os.path.join(root, f)
            if is_path_excluded(file_path, EXCLUDE_PATHS):
                continue
            
            # Dosya adı veya uzantısı eşleşmesi kontrol et
            if (f in CLEANUP_FILES or 
                os.path.splitext(f)[1].lower() in CLEANUP_EXTENSIONS):
                items_to_delete.append({'type': 'file', 'path': file_path})
    
    if not items_to_delete:
        print("✨ Temizlenecek önbellek veya geçici dosya bulunamadı.")
        return 0
    
    # Silinecek öğeleri listele
    if verbose or dry_run:
        print(f"🗑️ Aşağıdaki {len(items_to_delete)} öğe silinmek üzere:")
        for item in items_to_delete:
            print(f"  - [{item['type']}] {item['path']}")
    else:
        print(f"🗑️ Toplam {len(items_to_delete)} öğe silme için işaretlendi.")
        
    print(f"📊 Dosya Türlerine Göre: {sum(1 for i in items_to_delete if i['type'] == 'file')} dosya, {sum(1 for i in items_to_delete if i['type'] == 'dir')} klasör")
    
    if dry_run:
        print("🔍 Kuru çalışma modu: Hiçbir dosya silinmedi.")
        return 0
    
    if not non_interactive:
        if not confirm_action("Bu önbellek öğelerini kalıcı olarak silmek istiyor musunuz?"):
            print("❌ İşlem iptal edildi.")
            return 0
    
    # Silme işlemini gerçekleştir
    deleted_count = 0
    for item in items_to_delete:
        try:
            if item['type'] == 'dir':
                shutil.rmtree(item['path'])
                if verbose:
                    print(f"✅ Dizin silindi: {item['path']}")
            elif item['type'] == 'file':
                os.remove(item['path'])
                if verbose:
                    print(f"✅ Dosya silindi: {item['path']}")
            deleted_count += 1
        except Exception as e:
            print(f"❌ '{item['path']}' silinemedi: {e}")
    
    print(f"🎉 Önbellek temizliği tamamlandı! {deleted_count} öğe silindi.")
    return deleted_count

def clean_media_directories(dirs_list, dry_run=False, non_interactive=False, verbose=False):
    """Belirtilen yükleme dizinlerindeki tüm dosyaları temizler"""
    print("\n🧹 Medya Dosyaları Temizleniyor...")
    
    cleaned_dirs = 0
    cleaned_files = 0
    
    # Tüm medya dosyalarını topla
    media_files = []
    for dir_path in dirs_list:
        if os.path.exists(dir_path):
            # Dizin içindeki dosyaları topla
            for root, _, files in os.walk(dir_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    media_files.append({'path': file_path, 'dir': dir_path})
    
    if not media_files:
        print("✨ Temizlenecek medya dosyası bulunamadı.")
        return 0, 0
    
    # Silinecek öğeleri listele
    if verbose or dry_run:
        print(f"🗑️ Aşağıdaki {len(media_files)} medya dosyası silinmek üzere:")
        for item in media_files:
            print(f"  - {item['path']}")
    else:
        print(f"🗑️ Toplam {len(media_files)} medya dosyası silme için işaretlendi.")
    
    if dry_run:
        print("🔍 Kuru çalışma modu: Hiçbir dosya silinmedi.")
        return 0, 0
    
    if not non_interactive:
        if not confirm_action("Bu medya dosyalarını kalıcı olarak silmek istiyor musunuz?"):
            print("❌ İşlem iptal edildi.")
            return 0, 0
    
    # Silme işlemini gerçekleştir
    deleted_files = 0
    processed_dirs = set()
    
    for item in media_files:
        try:
            os.remove(item['path'])
            deleted_files += 1
            processed_dirs.add(item['dir'])
            if verbose:
                print(f"✅ Dosya silindi: {item['path']}")
        except Exception as e:
            print(f"❌ '{item['path']}' silinemedi: {e}")
    
    # Dizinleri yeniden oluştur (boş olarak)
    for dir_path in dirs_list:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            if verbose:
                print(f"✅ Dizin oluşturuldu: {dir_path}")
        processed_dirs.add(dir_path)
    
    print(f"🎉 Medya temizliği tamamlandı! {deleted_files} dosya silindi, {len(processed_dirs)} dizin işlendi.")
    return len(processed_dirs), deleted_files

def clean_pdf_directory(dry_run=False, non_interactive=False, verbose=False):
    """PDF dizinindeki tüm dosyaları temizler"""
    print("\n🧹 PDF Dosyaları Temizleniyor...")
    
    files_removed = 0
    if os.path.exists(PDF_DIR):
        # PDF dosyalarını topla
        pdf_files = []
        for file in os.listdir(PDF_DIR):
            if os.path.isfile(os.path.join(PDF_DIR, file)):
                pdf_files.append(os.path.join(PDF_DIR, file))
        
        if not pdf_files:
            print("✨ Temizlenecek PDF dosyası bulunamadı.")
            return 0
        
        # Silinecek öğeleri listele
        if verbose or dry_run:
            print(f"🗑️ Aşağıdaki {len(pdf_files)} PDF dosyası silinmek üzere:")
            for path in pdf_files:
                print(f"  - {path}")
        else:
            print(f"🗑️ Toplam {len(pdf_files)} PDF dosyası silme için işaretlendi.")
        
        if dry_run:
            print("🔍 Kuru çalışma modu: Hiçbir dosya silinmedi.")
            return 0
        
        if not non_interactive:
            if not confirm_action("Bu PDF dosyalarını kalıcı olarak silmek istiyor musunuz?"):
                print("❌ İşlem iptal edildi.")
                return 0
        
        # Silme işlemini gerçekleştir
        for file_path in pdf_files:
            try:
                os.remove(file_path)
                files_removed += 1
                if verbose:
                    print(f"✅ Dosya silindi: {file_path}")
            except Exception as e:
                print(f"❌ '{file_path}' silinirken hata: {e}")
            
        print(f"✅ PDF dizini temizlendi: {PDF_DIR} ({files_removed} dosya)")
    else:
        # Dizin yoksa oluştur
        os.makedirs(PDF_DIR, exist_ok=True)
        print(f"✅ PDF dizini oluşturuldu: {PDF_DIR}")
    
    return files_removed

def reset_database(dry_run=False, non_interactive=False):
    """Veritabanını sıfırlar (siler)"""
    print("\n🧹 Veritabanı Sıfırlanıyor...")
    
    if dry_run:
        print("🔍 Kuru çalışma modu: Veritabanı değiştirilmeyecek.")
        return True
    
    if not non_interactive:
        if not confirm_action("⚠️ DİKKAT: Veritabanı sıfırlanacak. Tüm verileriniz silinecektir. Devam etmek istiyor musunuz?"):
            print("❌ İşlem iptal edildi.")
            return False
    
    try:
        # Mevcut veritabanını yedekle
        if os.path.exists(DB_FILE):
            if os.path.exists(DB_BACKUP):
                os.remove(DB_BACKUP)
            shutil.copy2(DB_FILE, DB_BACKUP)
            print(f"✅ Mevcut veritabanı yedeklendi: {DB_BACKUP}")
            
            # Mevcut veritabanını sil
            os.remove(DB_FILE)
            print(f"✅ Veritabanı silindi: {DB_FILE}")
        else:
            print(f"⚠️ Veritabanı dosyası mevcut değil: {DB_FILE}")
        
        # Dizin yoksa oluştur
        os.makedirs(os.path.dirname(DB_FILE), exist_ok=True)
        
        # Boş bir veritabanı oluştur
        conn = sqlite3.connect(DB_FILE)
        conn.close()
        print(f"✅ Boş veritabanı oluşturuldu: {DB_FILE}")
        
        print("🎉 Veritabanı başarıyla sıfırlandı!")
        return True
    except Exception as e:
        print(f"❌ Veritabanı sıfırlanırken hata: {e}")
        return False

def remove_temp_scripts(dry_run=False, non_interactive=False):
    """Geçici temizleme scriptlerini siler"""
    print("\n🧹 Geçici Temizleme Scriptleri Temizleniyor...")
    
    temp_scripts = [
        'reset_all_data.py',
        'setup_fresh_db.py',
        'prepare_empty_project.py',
        'cleanup_project.py',
        'deep_cleanup.py'
    ]
    
    if dry_run:
        print("🔍 Kuru çalışma modu: Hiçbir script silinmeyecek.")
        print(f"🗑️ Silinecek scriptler: {', '.join(temp_scripts)}")
        return 0
    
    if not non_interactive:
        if not confirm_action("Bu geçici temizleme scriptlerini silmek istiyor musunuz?"):
            print("❌ İşlem iptal edildi.")
            return 0
    
    deleted_count = 0
    for script in temp_scripts:
        script_path = os.path.join(PROJECT_ROOT, script)
        if os.path.exists(script_path):
            try:
                os.remove(script_path)
                print(f"✅ Script silindi: {script}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ '{script}' silinemedi: {e}")
    
    print(f"🎉 Script temizliği tamamlandı! {deleted_count} script silindi.")
    return deleted_count

# --- Ana Program ---

def parse_args():
    """Komut satırı argümanlarını ayrıştırır"""
    parser = argparse.ArgumentParser(
        description="Zeppelin 2025 - Kapsamlı Temizleme ve Hazırlama Aracı",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__.split("\n\n")[1]
    )
    
    parser.add_argument('--clean-all', action='store_true', 
                        help="Tüm temizleme işlemlerini gerçekleştirir")
    parser.add_argument('--clean-cache', action='store_true', 
                        help="Sadece önbellek ve geçici dosyaları temizler")
    parser.add_argument('--clean-media', action='store_true', 
                        help="Sadece yüklenen medya dosyalarını temizler")
    parser.add_argument('--clean-db', action='store_true', 
                        help="Sadece veritabanını sıfırlar")
    parser.add_argument('--remove-scripts', action='store_true',
                        help="Gereksiz temizleme scriptlerini siler")
    parser.add_argument('--dry-run', action='store_true', 
                        help="Değişiklik yapmadan önce ne yapılacağını gösterir")
    parser.add_argument('-v', '--verbose', action='store_true', 
                        help="Detaylı çıktı gösterir")
    parser.add_argument('-y', '--yes', action='store_true', dest='non_interactive',
                        help="Onay sormadan işlemleri gerçekleştirir")
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    print("\n🚀 Zeppelin 2025 - Kapsamlı Temizleme ve Hazırlama Aracı 🚀")
    print("=" * 60)
    
    # Argüman kontrolü
    if not any([args.clean_all, args.clean_cache, args.clean_media, args.clean_db, args.remove_scripts]):
        args.clean_all = True
        print("⚠️ Hiçbir temizleme seçeneği belirtilmedi. Tüm temizleme işlemleri gerçekleştirilecek.")
    
    if args.dry_run:
        print("🔍 KURU ÇALIŞMA MODU: Gerçek değişiklik yapılmayacak\n")
    
    start_time = time.time()
    
    # İşlemleri gerçekleştir
    if args.clean_all or args.clean_cache:
        cleanup_cache(args.dry_run, args.non_interactive, args.verbose)
    
    if args.clean_all or args.clean_media:
        clean_media_directories(MEDIA_DIRS, args.dry_run, args.non_interactive, args.verbose)
        clean_pdf_directory(args.dry_run, args.non_interactive, args.verbose)
    
    if args.clean_all or args.clean_db:
        reset_database(args.dry_run, args.non_interactive)
    
    if args.clean_all or args.remove_scripts:
        remove_temp_scripts(args.dry_run, args.non_interactive)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print("\n✨ İşlem tamamlandı.")
    print(f"⏱️ Geçen süre: {elapsed_time:.2f} saniye")
    print("=" * 60)
    print("📝 Sonraki adımlar:")
    print("  1. Flask uygulamanızı başlatın: flask run")
    print("  2. Tarayıcınızda http://127.0.0.1:5000/admin adresine gidin")
    print("  3. Yeni bir yönetici hesabı oluşturun")
    print("  4. Sistemi ihtiyaçlarınıza göre yapılandırın")
    print("=" * 60)

if __name__ == "__main__":
    main() 