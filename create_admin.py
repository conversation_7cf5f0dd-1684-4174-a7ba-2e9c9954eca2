#!/usr/bin/env python3
"""
Admin <PERSON>llanıcı Oluşturma Scripti
- Yeni bir admin kullanıcısı oluşturur
- Parametreler ile özelleştirilebilir
"""

import os
import sys
import argparse
from getpass import getpass

# Proje kök dizinini Python path'ine ekle
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Flask uygulamasını ve modelleri import et
from app import app, db
from models.user import User

def create_admin_user(username, email, password, force=False):
    """
    Yeni bir admin kullanıcısı oluşturur
    
    :param username: Kullanıcı adı
    :param email: E-posta adresi
    :param password: Şifre
    :param force: Eğer True ise, kullanıcı zaten varsa günceller
    :return: Oluşturulan veya güncellenen kullanıcı
    """
    with app.app_context():
        # <PERSON><PERSON><PERSON>cı adı veya e-posta ile kullanıcı var mı kontrol et
        existing_user = User.query.filter(
            (User.username == username) | (User.email == email)
        ).first()
        
        if existing_user:
            if force:
                print(f"⚠️ '{existing_user.username}' kullanıcısı zaten var, güncelleniyor...")
                existing_user.email = email
                existing_user.set_password(password)
                existing_user.is_admin = True
                existing_user.active = True
                db.session.commit()
                print(f"✅ Admin kullanıcısı güncellendi: {existing_user.username}")
                return existing_user
            else:
                print(f"❌ Hata: '{username}' kullanıcısı veya '{email}' e-posta adresi zaten kullanımda.")
                return None
        
        # Yeni admin kullanıcısı oluştur
        new_admin = User(
            username=username,
            email=email,
            is_admin=True,
            active=True
        )
        new_admin.set_password(password)
        
        # Veritabanına ekle
        db.session.add(new_admin)
        db.session.commit()
        
        print(f"✅ Yeni admin kullanıcısı başarıyla oluşturuldu: {new_admin.username}")
        return new_admin

def parse_args():
    """Komut satırı argümanlarını işler"""
    parser = argparse.ArgumentParser(description="Admin Kullanıcı Oluşturma Aracı")
    parser.add_argument("-u", "--username", help="Admin kullanıcı adı")
    parser.add_argument("-e", "--email", help="Admin e-posta adresi")
    parser.add_argument("-p", "--password", help="Admin şifresi (belirtilmezse güvenli şekilde istenir)")
    parser.add_argument("-f", "--force", action="store_true", help="Kullanıcı varsa günceller")
    return parser.parse_args()

if __name__ == "__main__":
    # Argümanları al
    args = parse_args()
    
    # Etkileşimli mod
    if not args.username:
        args.username = input("Admin kullanıcı adı: ")
    
    if not args.email:
        args.email = input("Admin e-posta adresi: ")
    
    if not args.password:
        args.password = getpass("Admin şifresi (görünmeyecek): ")
        confirm_password = getpass("Şifreyi tekrar girin: ")
        
        if args.password != confirm_password:
            print("❌ Hata: Şifreler eşleşmiyor.")
            sys.exit(1)
    
    # Admin kullanıcısı oluştur
    create_admin_user(args.username, args.email, args.password, args.force) 