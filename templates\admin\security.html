{% extends "admin/base.html" %}

{% block page_title %}Güvenlik Yönetimi{% endblock %}
{% block breadcrumb %}Güvenlik Yönetimi{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <!-- <PERSON><PERSON> -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt me-2"></i>
            Güvenlik Yönetimi
        </h1>
        <button onclick="refreshSecurityInfo()" class="btn btn-primary">
            <i class="fas fa-sync-alt me-1"></i>
            Yenile
        </button>
    </div>

    <!-- Güvenlik Özeti -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Son 1 Saatteki İstekler
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="requests-hour">
                                {{ security_info.requests.total_hour if security_info.requests else 'Yükleniyor...' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Engellenmiş IP'ler
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="blocked-ips-count">
                                {{ security_info.security.blocked_ips|length if security_info.security else 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ban fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Başarısız Giriş Denemeleri
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="failed-logins">
                                {{ security_info.security.failed_logins if security_info.security else 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Şüpheli Aktiviteler
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="suspicious-activities">
                                {{ security_info.security.suspicious_activities if security_info.security else 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-search fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sistem Güvenlik Bilgileri -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-server me-2"></i>
                        Sistem Güvenlik Durumu
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">CPU Kullanımı</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="cpu-usage">
                                    {{ security_info.system.cpu_percent if security_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Bellek Kullanımı</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-usage">
                                    {{ security_info.system.memory_percent if security_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Aktif Bağlantılar</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-connections">
                                    {{ security_info.system.active_connections if security_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Çalışan İşlemler</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-processes">
                                    {{ security_info.system.total_processes if security_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- En Aktif IP'ler -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        En Aktif IP Adresleri
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="top-ips-table">
                            <thead>
                                <tr>
                                    <th>IP Adresi</th>
                                    <th>İstek Sayısı</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if security_info.requests and security_info.requests.top_ips %}
                                    {% for ip, count in security_info.requests.top_ips %}
                                    <tr>
                                        <td>{{ ip }}</td>
                                        <td>{{ count }}</td>
                                        <td>
                                            <button onclick="blockIP('{{ ip }}')" class="btn btn-sm btn-danger">
                                                <i class="fas fa-ban me-1"></i>Engelle
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="3" class="text-center">Veri yükleniyor...</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Engellenmiş IP'ler -->
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-ban me-2"></i>
                        Engellenmiş IP Adresleri
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="blocked-ips-table">
                            <thead>
                                <tr>
                                    <th>IP Adresi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if security_info.security and security_info.security.blocked_ips %}
                                    {% for ip in security_info.security.blocked_ips %}
                                    <tr>
                                        <td>{{ ip }}</td>
                                        <td>
                                            <button onclick="unblockIP('{{ ip }}')" class="btn btn-sm btn-success">
                                                <i class="fas fa-check me-1"></i>Engeli Kaldır
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="2" class="text-center">Engellenmiş IP yok</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Son Şüpheli Aktiviteler -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Son Şüpheli Aktiviteler
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="suspicious-activities-table">
                            <thead>
                                <tr>
                                    <th>Zaman</th>
                                    <th>Tip</th>
                                    <th>Açıklama</th>
                                    <th>IP Adresi</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if security_info.security and security_info.security.recent_suspicious %}
                                    {% for activity in security_info.security.recent_suspicious %}
                                    <tr>
                                        <td>{{ activity.timestamp.strftime('%Y-%m-%d %H:%M:%S') if activity.timestamp else 'Bilinmiyor' }}</td>
                                        <td>
                                            <span class="badge badge-warning">{{ activity.type }}</span>
                                        </td>
                                        <td>{{ activity.description }}</td>
                                        <td>{{ activity.ip or 'Bilinmiyor' }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">Şüpheli aktivite yok</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshSecurityInfo() {
    fetch('/admin/security/info')
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert('error', 'Güvenlik bilgileri alınamadı: ' + data.error);
            return;
        }
        
        // Özet bilgileri güncelle
        if (data.requests) {
            document.getElementById('requests-hour').textContent = data.requests.total_hour;
        }
        
        if (data.security) {
            document.getElementById('blocked-ips-count').textContent = data.security.blocked_ips.length;
            document.getElementById('failed-logins').textContent = data.security.failed_logins;
            document.getElementById('suspicious-activities').textContent = data.security.suspicious_activities;
        }
        
        if (data.system) {
            document.getElementById('cpu-usage').textContent = data.system.cpu_percent;
            document.getElementById('memory-usage').textContent = data.system.memory_percent;
            document.getElementById('active-connections').textContent = data.system.active_connections;
            document.getElementById('total-processes').textContent = data.system.total_processes;
        }
        
        showAlert('success', 'Güvenlik bilgileri güncellendi');
    })
    .catch(error => {
        showAlert('error', 'Bir hata oluştu: ' + error);
    });
}

function blockIP(ip) {
    if (!confirm('Bu IP adresini engellemek istediğinizden emin misiniz?')) {
        return;
    }
    
    fetch('/admin/security/block-ip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ip: ip})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            refreshSecurityInfo();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Bir hata oluştu: ' + error);
    });
}

function unblockIP(ip) {
    if (!confirm('Bu IP adresinin engelini kaldırmak istediğinizden emin misiniz?')) {
        return;
    }
    
    fetch('/admin/security/unblock-ip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ip: ip})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            refreshSecurityInfo();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Bir hata oluştu: ' + error);
    });
}

function showAlert(type, message) {
    // Admin base template'ine uygun flash message oluştur
    const iconClass = type === 'success' ? 'fas fa-check text-green-600' : 'fas fa-exclamation text-red-600';
    const bgClass = type === 'success' ? 'bg-green-100' : 'bg-red-100';

    const alertHtml = `
        <div class="flash-message bg-white border border-gray-200 shadow-lg px-4 py-3 rounded-lg flex items-center justify-between min-w-80 max-w-md">
            <div class="flex items-center">
                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center mr-3">
                    <i class="${iconClass} text-sm"></i>
                </div>
                <span class="text-gray-800 text-sm font-medium">${message}</span>
            </div>
            <button class="close-flash ml-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded hover:bg-gray-100">
                <i class="fas fa-times text-xs"></i>
            </button>
        </div>
    `;

    // Fixed position container oluştur veya mevcut olanı kullan
    let container = document.querySelector('.flash-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'flash-container fixed top-4 right-4 z-50 space-y-3';
        document.body.appendChild(container);
    }

    container.insertAdjacentHTML('beforeend', alertHtml);

    const flashMessage = container.lastElementChild;

    // Close button event
    flashMessage.querySelector('.close-flash').addEventListener('click', () => {
        flashMessage.style.opacity = '0';
        flashMessage.style.transform = 'translateX(100%)';
        setTimeout(() => flashMessage.remove(), 300);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (flashMessage.parentNode) {
            flashMessage.style.opacity = '0';
            flashMessage.style.transform = 'translateX(100%)';
            setTimeout(() => flashMessage.remove(), 300);
        }
    }, 5000);
}

// Sayfa yüklendiğinde otomatik yenile
document.addEventListener('DOMContentLoaded', function() {
    // 30 saniyede bir otomatik yenile
    setInterval(refreshSecurityInfo, 30000);
});
</script>
{% endblock %}
