{% extends "admin/base.html" %}

{% block title %}Aktiviteler{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Aktiviteler</h3>
                    <a href="{{ url_for('activities.activity_create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Yeni Aktivite
                    </a>
                </div>
                
                <div class="card-body">
                    {% if activities %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Resim</th>
                                    <th>Başlık</th>
                                    <th>Süre</th>
                                    <th>Zorluk</th>
                                    <th>Fiyat</th>
                                    <th>Durum</th>
                                    <th>Sıra</th>
                                    <th><PERSON><PERSON><PERSON><PERSON></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in activities %}
                                <tr>
                                    <td>
                                        {% if activity.featured_image %}
                                        <img src="{{ url_for('static', filename='uploads/activities/' + activity.featured_image) }}" 
                                             alt="{{ activity.title_tr }}" 
                                             class="img-thumbnail" 
                                             style="width: 60px; height: 60px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 60px; height: 60px;">
                                            <i class="fas fa-mountain text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ activity.title_tr }}</strong>
                                        {% if activity.title_en %}
                                        <br><small class="text-muted">{{ activity.title_en }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.duration %}
                                        <span class="badge badge-info">{{ activity.duration }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.difficulty %}
                                        <span class="badge badge-secondary">{{ activity.get_difficulty_display() }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.price %}
                                        <span class="text-success font-weight-bold">{{ "%.2f"|format(activity.price) }} ₺</span>
                                        {% else %}
                                        <span class="text-muted">Ücretsiz</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.status == 'active' %}
                                        <span class="badge badge-success">Aktif</span>
                                        {% else %}
                                        <span class="badge badge-danger">Pasif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-light">{{ activity.sort_order }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('activities.activity_edit', id=activity.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('activities.activity_detail', slug=activity.slug) }}" 
                                               class="btn btn-sm btn-outline-info" title="Görüntüle" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Sil"
                                                    onclick="deleteActivity({{ activity.id }}, '{{ activity.title_tr }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-mountain fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz aktivite eklenmemiş</h5>
                        <p class="text-muted">İlk aktivitenizi eklemek için yukarıdaki "Yeni Aktivite" butonunu kullanın.</p>
                        <a href="{{ url_for('activities.activity_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> İlk Aktiviteyi Ekle
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Silme Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aktiviteyi Sil</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Bu aktiviteyi silmek istediğinizden emin misiniz?</p>
                <p><strong id="activityName"></strong></p>
                <p class="text-danger"><small>Bu işlem geri alınamaz!</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">İptal</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteActivity(id, name) {
    document.getElementById('activityName').textContent = name;
    document.getElementById('deleteForm').action = "{{ url_for('activities.activity_delete', id=0) }}".replace('0', id);
    $('#deleteModal').modal('show');
}
</script>
{% endblock %}
