#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import app, db
from models.blog import BlogPost
from models.room import Room, RoomCategory, RoomFeature
from models.activity import Activity
from models.page import Page
from slugify import slugify
from datetime import datetime, timedelta
import random

def create_sample_blog_posts():
    posts = [
        {
            'title_tr': '<PERSON>padokya\'da Balon Turu',
            'title_en': 'Hot Air Balloon Tour in Cappadocia',
            'content_tr': '<PERSON><PERSON><PERSON><PERSON>\'da balon turu hakkında detaylı bilgi.',
            'content_en': 'Detailed information about hot air balloon tour in Cappadocia.',
            'status': 'published'
        },
        {
            'title_tr': 'Kapadok<PERSON>\'da Konaklama Seçenekleri',
            'title_en': 'Accommodation Options in Cappadocia',
            'content_tr': 'Kapadokya\'da konaklama seçenekleri hakkında bilgiler.',
            'content_en': 'Information about accommodation options in Cappadocia.',
            'status': 'published'
        }
    ]
    
    for post_data in posts:
        post = BlogPost(
            title_tr=post_data['title_tr'],
            title_en=post_data['title_en'],
            content_tr=post_data['content_tr'],
            content_en=post_data['content_en'],
            slug=slugify(post_data['title_tr']),
            status=post_data['status']
        )
        db.session.add(post)
    
    db.session.commit()
    print("Blog yazıları oluşturuldu")

def create_sample_rooms():
    with app.app_context():
        print("Örnek oda verileri oluşturuluyor...")
        
        # Önce oda kategorileri oluşturalım
        categories = [
            {
                'name_tr': 'Standart Oda',
                'name_en': 'Standard Room',
                'description_tr': 'Konforlu ve ekonomik standart odalarımız.',
                'description_en': 'Comfortable and economic standard rooms.',
                'order': 1,
                'status': 'active'
            },
            {
                'name_tr': 'Deluxe Oda',
                'name_en': 'Deluxe Room',
                'description_tr': 'Daha geniş ve konforlu deluxe odalarımız.',
                'description_en': 'More spacious and comfortable deluxe rooms.',
                'order': 2,
                'status': 'active'
            },
            {
                'name_tr': 'Suit Oda',
                'name_en': 'Suite Room',
                'description_tr': 'Lüks ve geniş suit odalarımız.',
                'description_en': 'Luxurious and spacious suite rooms.',
                'order': 3,
                'status': 'active'
            }
        ]
        
        # Kategorileri ekleyelim
        category_objects = []
        for cat_data in categories:
            category = RoomCategory.query.filter_by(name_tr=cat_data['name_tr']).first()
            if not category:
                category = RoomCategory(**cat_data)
                db.session.add(category)
                db.session.flush()  # ID'leri almak için flush yapalım
            category_objects.append(category)
        
        # Oda özellikleri oluşturalım
        features = [
            {
                'name': 'Wi-Fi',
                'icon': 'fas fa-wifi',
                'order': 1
            },
            {
                'name': 'Klima',
                'icon': 'fas fa-snowflake',
                'order': 2
            },
            {
                'name': 'Minibar',
                'icon': 'fas fa-glass-martini',
                'order': 3
            },
            {
                'name': 'TV',
                'icon': 'fas fa-tv',
                'order': 4
            },
            {
                'name': 'Saç Kurutma Makinesi',
                'icon': 'fas fa-wind',
                'order': 5
            },
            {
                'name': 'Balkon',
                'icon': 'fas fa-door-open',
                'order': 6
            },
            {
                'name': 'Jakuzi',
                'icon': 'fas fa-hot-tub',
                'order': 7
            }
        ]
        
        # Özellikleri ekleyelim
        feature_objects = []
        for feat_data in features:
            feature = RoomFeature.query.filter_by(name=feat_data['name']).first()
            if not feature:
                feature = RoomFeature(**feat_data)
                db.session.add(feature)
                db.session.flush()  # ID'leri almak için flush yapalım
            feature_objects.append(feature)
        
        # Odaları oluşturalım
        rooms = [
            # Standart Odalar
            {
                'title_tr': 'Standart Oda 101',
                'title_en': 'Standard Room 101',
                'slug': 'standart-oda-101',
                'description_tr': '<p>Konforlu ve ekonomik standart odamız. Şehir manzaralı ve tüm ihtiyaçlarınızı karşılayacak donanıma sahiptir.</p>',
                'description_en': '<p>Our comfortable and economic standard room. It has a city view and all the equipment to meet your needs.</p>',
                'price': 1200,
                'currency': 'TRY',
                'capacity': 2,
                'size': 25,  # metrekare
                'category_id': category_objects[0].id,
                'status': 'active',
                'features': [feature_objects[0], feature_objects[1], feature_objects[3], feature_objects[4]]  # Wi-Fi, Klima, TV, Saç Kurutma
            },
            {
                'title_tr': 'Standart Oda 102',
                'title_en': 'Standard Room 102',
                'slug': 'standart-oda-102',
                'description_tr': '<p>Konforlu ve ekonomik standart odamız. Bahçe manzaralı ve tüm ihtiyaçlarınızı karşılayacak donanıma sahiptir.</p>',
                'description_en': '<p>Our comfortable and economic standard room. It has a garden view and all the equipment to meet your needs.</p>',
                'price': 1250,
                'currency': 'TRY',
                'capacity': 2,
                'size': 25,  # metrekare
                'category_id': category_objects[0].id,
                'status': 'active',
                'features': [feature_objects[0], feature_objects[1], feature_objects[3], feature_objects[4]]  # Wi-Fi, Klima, TV, Saç Kurutma
            },
            
            # Deluxe Odalar
            {
                'title_tr': 'Deluxe Oda 201',
                'title_en': 'Deluxe Room 201',
                'slug': 'deluxe-oda-201',
                'description_tr': '<p>Daha geniş ve konforlu deluxe odamız. Şehir manzaralı ve geniş bir balkona sahiptir.</p>',
                'description_en': '<p>Our more spacious and comfortable deluxe room. It has a city view and a large balcony.</p>',
                'price': 1800,
                'currency': 'TRY',
                'capacity': 3,
                'size': 35,  # metrekare
                'category_id': category_objects[1].id,
                'status': 'active',
                'features': [feature_objects[0], feature_objects[1], feature_objects[2], feature_objects[3], feature_objects[4], feature_objects[5]]  # Wi-Fi, Klima, Minibar, TV, Saç Kurutma, Balkon
            },
            {
                'title_tr': 'Deluxe Oda 202',
                'title_en': 'Deluxe Room 202',
                'slug': 'deluxe-oda-202',
                'description_tr': '<p>Daha geniş ve konforlu deluxe odamız. Deniz manzaralı ve geniş bir balkona sahiptir.</p>',
                'description_en': '<p>Our more spacious and comfortable deluxe room. It has a sea view and a large balcony.</p>',
                'price': 1900,
                'currency': 'TRY',
                'capacity': 3,
                'size': 35,  # metrekare
                'category_id': category_objects[1].id,
                'status': 'active',
                'features': [feature_objects[0], feature_objects[1], feature_objects[2], feature_objects[3], feature_objects[4], feature_objects[5]]  # Wi-Fi, Klima, Minibar, TV, Saç Kurutma, Balkon
            },
            
            # Suit Odalar
            {
                'title_tr': 'Suit Oda 301',
                'title_en': 'Suite Room 301',
                'slug': 'suit-oda-301',
                'description_tr': '<p>Lüks ve geniş suit odamız. Panoramik manzara ve jakuzi ile unutulmaz bir konaklama deneyimi sunar.</p>',
                'description_en': '<p>Our luxurious and spacious suite room. It offers an unforgettable accommodation experience with panoramic view and jacuzzi.</p>',
                'price': 2500,
                'currency': 'TRY',
                'capacity': 4,
                'size': 50,  # metrekare
                'category_id': category_objects[2].id,
                'status': 'active',
                'features': feature_objects  # Tüm özellikler
            },
            {
                'title_tr': 'Suit Oda 302',
                'title_en': 'Suite Room 302',
                'slug': 'suit-oda-302',
                'description_tr': '<p>Lüks ve geniş suit odamız. Panoramik manzara ve jakuzi ile unutulmaz bir konaklama deneyimi sunar.</p>',
                'description_en': '<p>Our luxurious and spacious suite room. It offers an unforgettable accommodation experience with panoramic view and jacuzzi.</p>',
                'price': 2700,
                'currency': 'TRY',
                'capacity': 4,
                'size': 55,  # metrekare
                'category_id': category_objects[2].id,
                'status': 'active',
                'features': feature_objects  # Tüm özellikler
            }
        ]
        
        # Odaları ekleyelim
        for room_data in rooms:
            features = room_data.pop('features')
            room = Room.query.filter_by(slug=room_data['slug']).first()
            if not room:
                room = Room(**room_data)
                room.features = features
                db.session.add(room)
        
        # Değişiklikleri kaydedelim
        db.session.commit()
        print("Örnek oda verileri başarıyla oluşturuldu!")

def create_sample_activities():
    activities = [
        {
            'title_tr': 'ATV Turu',
            'title_en': 'ATV Tour',
            'description_tr': 'Kapadokya\'da ATV turu',
            'description_en': 'ATV tour in Cappadocia',
            'content_tr': 'Kapadokya\'da ATV turu hakkında detaylı bilgi.',
            'content_en': 'Detailed information about ATV tour in Cappadocia.',
            'duration': '2 saat',
            'price': 50,
            'status': 'active'
        },
        {
            'title_tr': 'Vadi Yürüyüşü',
            'title_en': 'Valley Hiking',
            'description_tr': 'Kapadokya\'da vadi yürüyüşü',
            'description_en': 'Valley hiking in Cappadocia',
            'content_tr': 'Kapadokya\'da vadi yürüyüşü hakkında detaylı bilgi.',
            'content_en': 'Detailed information about valley hiking in Cappadocia.',
            'duration': '3 saat',
            'price': 30,
            'status': 'active'
        }
    ]
    
    for activity_data in activities:
        activity = Activity(
            title_tr=activity_data['title_tr'],
            title_en=activity_data['title_en'],
            description_tr=activity_data['description_tr'],
            description_en=activity_data['description_en'],
            content_tr=activity_data['content_tr'],
            content_en=activity_data['content_en'],
            duration=activity_data['duration'],
            price=activity_data['price'],
            status=activity_data['status']
        )
        db.session.add(activity)
    
    db.session.commit()
    print("Aktiviteler oluşturuldu")

def create_sample_pages():
    pages = [
        {
            'title_tr': 'Hakkımızda',
            'title_en': 'About Us',
            'content_tr': 'Hakkımızda sayfası içeriği',
            'content_en': 'About us page content',
            'status': 'published'
        },
        {
            'title_tr': 'İletişim',
            'title_en': 'Contact',
            'content_tr': 'İletişim sayfası içeriği',
            'content_en': 'Contact page content',
            'status': 'published'
        }
    ]
    
    for page_data in pages:
        page = Page(
            title_tr=page_data['title_tr'],
            title_en=page_data['title_en'],
            content_tr=page_data['content_tr'],
            content_en=page_data['content_en'],
            slug=slugify(page_data['title_tr']),
            status=page_data['status']
        )
        db.session.add(page)
    
    db.session.commit()
    print("Sayfalar oluşturuldu")

def main():
    with app.app_context():
        # Mevcut verileri temizle
        BlogPost.query.delete()
        Room.query.delete()
        Activity.query.delete()
        Page.query.delete()
        db.session.commit()
        
        # Örnek veriler oluştur
        create_sample_blog_posts()
        create_sample_rooms()
        create_sample_activities()
        create_sample_pages()
        
        print("Örnek veriler başarıyla oluşturuldu!")

if __name__ == "__main__":
    create_sample_rooms() 