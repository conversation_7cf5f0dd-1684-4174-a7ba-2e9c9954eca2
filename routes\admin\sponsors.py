from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required
from models.sponsor import db, Sponsor
from werkzeug.utils import secure_filename
import os
from datetime import datetime

sponsors_bp = Blueprint('admin_sponsors', __name__, url_prefix='/admin/sponsors')

def save_sponsor_image(file):
    if file:
        filename = secure_filename(file.filename)
        base, ext = os.path.splitext(filename)
        filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'sponsors')
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        return filename
    return None

@sponsors_bp.route('/')
@login_required
def sponsor_list():
    sponsors = Sponsor.query.order_by(Sponsor.order).all()
    return render_template('admin/sponsors/list.html', sponsors=sponsors)

@sponsors_bp.route('/create', methods=['GET', 'POST'])
@login_required
def sponsor_create():
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            url = request.form.get('url')
            order = request.form.get('order', 0, type=int)
            active = request.form.get('active') == 'on'
            
            # Debug için print ekleyelim
            print("Form data:", request.form)
            print("Files:", request.files)
            
            sponsor = Sponsor(
                name=name,
                url=url,
                order=order,
                active=active
            )

            if 'image' in request.files:
                image = request.files['image']
                if image and image.filename:
                    filename = save_sponsor_image(image)
                    if filename:
                        sponsor.image = filename

            db.session.add(sponsor)
            db.session.commit()
            
            flash('Sponsor başarıyla eklendi.', 'success')
            return redirect(url_for('admin_sponsors.sponsor_list'))
            
        except Exception as e:
            db.session.rollback()
            print("Hata:", str(e))  # Debug için
            flash(f'Sponsor eklenirken bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/sponsors/form.html', sponsor=None)

@sponsors_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def sponsor_edit(id):
    sponsor = Sponsor.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            sponsor.name = request.form.get('name')
            sponsor.url = request.form.get('url')
            sponsor.order = request.form.get('order', 0, type=int)
            sponsor.active = request.form.get('active') == 'on'

            if 'image' in request.files:
                image = request.files['image']
                if image:
                    filename = save_sponsor_image(image)
                    if filename:
                        # Eski resmi sil
                        if sponsor.image:
                            old_image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'sponsors', sponsor.image)
                            if os.path.exists(old_image_path):
                                os.remove(old_image_path)
                        sponsor.image = filename

            db.session.commit()
            flash('Sponsor başarıyla güncellendi.', 'success')
            return redirect(url_for('admin_sponsors.sponsor_list'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Sponsor güncellenirken bir hata oluştu: {str(e)}', 'error')
    
    return render_template('admin/sponsors/form.html', sponsor=sponsor)

@sponsors_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def sponsor_delete(id):
    try:
        sponsor = Sponsor.query.get_or_404(id)
        
        # Resmi sil
        if sponsor.image:
            image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'sponsors', sponsor.image)
            if os.path.exists(image_path):
                os.remove(image_path)
        
        db.session.delete(sponsor)
        db.session.commit()
        
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}) 