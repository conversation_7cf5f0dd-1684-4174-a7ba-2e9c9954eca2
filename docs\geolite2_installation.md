# GeoLite2 Database Installation Guide

This guide explains how to set up the MaxMind GeoLite2 database for country detection in the Zeppelin Hotel visitor statistics feature.

## Overview

The visitor statistics feature uses the MaxMind GeoLite2 database to determine the country of origin for website visitors based on their IP addresses. This information is then used to generate country-based visitor statistics in the admin dashboard.

## Installation Steps

1. **Create a MaxMind Account**:
   - Go to [MaxMind.com](https://www.maxmind.com/en/geolite2/signup)
   - Create a free account

2. **Generate a License Key**:
   - Log in to your MaxMind account
   - Navigate to "Services > My License Key"
   - Generate a new license key
   - Make note of this key as you'll need it for downloading the database

3. **Download the GeoLite2 Database**:
   - You can download the database manually from the MaxMind website
   - Or use the following command to download it automatically:

   ```bash
   mkdir -p instance/geoip
   cd instance/geoip
   wget "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=YOUR_LICENSE_KEY&suffix=tar.gz" -O GeoLite2-Country.tar.gz
   tar -xzf GeoLite2-Country.tar.gz --strip-components=1
   rm GeoLite2-Country.tar.gz
   ```

4. **Configure the Application**:
   - Ensure the database file path is correctly set in your application configuration
   - The default path is `instance/geoip/GeoLite2-Country.mmdb`

## Usage in the Application

The application uses the `geoip2` Python package to interact with the GeoLite2 database. Make sure this package is installed:

```bash
pip install geoip2
```

The country detection functionality is implemented in the `middleware/hotel_network.py` file, specifically in the `get_country_from_ip` function.

## Updating the Database

The GeoLite2 database is updated regularly. It's recommended to update your copy periodically (e.g., monthly) to ensure accurate geolocation data.

You can set up a cron job or scheduled task to automate this process:

```bash
# Example cron job to update the database monthly
0 0 1 * * cd /path/to/your/app/instance/geoip && wget "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=YOUR_LICENSE_KEY&suffix=tar.gz" -O GeoLite2-Country.tar.gz && tar -xzf GeoLite2-Country.tar.gz --strip-components=1 && rm GeoLite2-Country.tar.gz
```

## Troubleshooting

If you encounter issues with country detection:

1. Verify that the GeoLite2 database file exists at the expected location
2. Check that the file permissions allow the application to read the database
3. Ensure the `geoip2` Python package is installed
4. Test with a known IP address to verify functionality

For local development, note that private IP addresses (like 127.0.0.1) will not return country information. 