{% extends "admin/base.html" %}

{% block page_title %}Promosyon İstatistikleri{% endblock %}

{% block admin_content %}
<div class="px-6">
    <!-- Başlık -->
    <div class="flex items-center mb-6">
        <a href="{{ url_for('admin_promotions.promotion_list') }}" 
           class="text-zinc-600 hover:text-zinc-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-2xl font-light text-zinc-800">Promosyon İstatistikleri</h1>
            <p class="text-zinc-600 mt-1">{{ promotion.code }} kodlu promosyon görüntüleme verileri</p>
        </div>
    </div>

    <!-- Promosyon Bilgileri -->
    <div class="mb-6">
        <div class="bg-white rounded-xl shadow-sm border border-zinc-100 p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-zinc-700 mb-2">Promosyon Kodu</h3>
                    <p class="text-lg font-semibold text-zinc-900">{{ promotion.code }}</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-zinc-700 mb-2">Durum</h3>
                    {% if promotion.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>
                            Aktif (Kullanıcı Bazlı)
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <i class="fas fa-stop mr-1"></i>
                            Pasif
                        </span>
                    {% endif %}
                </div>
                <div>
                    <h3 class="text-sm font-medium text-zinc-700 mb-2">Süre</h3>
                    <p class="text-lg font-semibold text-zinc-900">{{ promotion.duration_minutes }} dakika</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-zinc-700 mb-2">Oluşturulma</h3>
                    <p class="text-lg font-semibold text-zinc-900">{{ promotion.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                </div>
            </div>
            
            {% if promotion.description %}
            <div class="mt-4 pt-4 border-t border-zinc-100">
                <h3 class="text-sm font-medium text-zinc-700 mb-2">Açıklama</h3>
                <p class="text-zinc-900">{{ promotion.description }}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- İstatistikler -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <i class="fas fa-eye text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Toplam Görüntüleme</p>
                    <p class="text-2xl font-semibold text-zinc-800">
                        {{ views|sum(attribute='view_count') or 0 }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-lg">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Benzersiz Ziyaretçi</p>
                    <p class="text-2xl font-semibold text-zinc-800">{{ views|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-sm border border-zinc-100">
            <div class="flex items-center">
                <div class="p-3 bg-amber-100 rounded-lg">
                    <i class="fas fa-clock text-amber-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-zinc-600">Aktif Kullanıcı</p>
                    <p class="text-2xl font-semibold text-zinc-800" id="active-users-count">
                        <i class="fas fa-spinner fa-spin"></i>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Görüntüleme Detayları -->
    <div class="bg-white rounded-xl shadow-sm border border-zinc-100">
        <div class="p-6 border-b border-zinc-100 flex justify-between items-center">
            <h2 class="text-lg font-medium text-zinc-800">Canlı IP Durumları</h2>
            <button onclick="refreshStatuses()"
                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                <i class="fas fa-sync-alt mr-2"></i>
                Yenile
            </button>
        </div>
        
        {% if views %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-zinc-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">IP Adresi</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Durum</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Kalan Süre</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">İlk Görüntüleme</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Son Görüntüleme</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">Görüntüleme Sayısı</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-zinc-200" id="views-table-body">
                    {% for view in views %}
                    <tr class="hover:bg-zinc-50" data-ip="{{ view.ip_address }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-zinc-900">{{ view.ip_address }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="status-indicator" data-ip="{{ view.ip_address }}">
                                <i class="fas fa-spinner fa-spin text-blue-500"></i>
                                <span class="text-sm text-zinc-500 ml-1">Kontrol ediliyor...</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="remaining-time" data-ip="{{ view.ip_address }}">
                                <i class="fas fa-spinner fa-spin text-amber-500"></i>
                                <span class="text-sm text-zinc-500 ml-1">Hesaplanıyor...</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-zinc-900">{{ view.first_view.strftime('%d.%m.%Y %H:%M:%S') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-zinc-900">{{ view.last_view.strftime('%d.%m.%Y %H:%M:%S') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-zinc-900">{{ view.view_count }}</div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="p-12 text-center">
            <i class="fas fa-chart-bar text-zinc-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-zinc-900 mb-2">Henüz görüntüleme yok</h3>
            <p class="text-zinc-600">Bu promosyon henüz hiç görüntülenmemiş.</p>
        </div>
        {% endif %}
    </div>

    <!-- Geri Dön Butonu -->
    <div class="mt-6">
        <a href="{{ url_for('admin_promotions.promotion_list') }}"
           class="inline-flex items-center px-4 py-2 border border-zinc-300 text-zinc-700 rounded-lg hover:bg-zinc-50 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>
            Promosyon Listesine Dön
        </a>
    </div>
</div>

<script>
// IP bazlı durum ve kalan süre kontrolü
async function updateIPStatuses() {
    const promotionId = {{ promotion.id }};
    const ipElements = document.querySelectorAll('[data-ip]');
    const ipAddresses = [...new Set(Array.from(ipElements).map(el => el.getAttribute('data-ip')))];

    console.log('IP adresleri bulundu:', ipAddresses);

    if (ipAddresses.length === 0) {
        console.log('Hiç IP adresi bulunamadı');
        document.getElementById('active-users-count').textContent = '0';
        return;
    }

    let activeCount = 0;

    // Tüm IP'leri paralel olarak kontrol et
    const promises = ipAddresses.map(async (ip) => {
        try {
            const response = await fetch(`/api/promotion/check/${ip}`);
            const data = await response.json();

            const statusElement = document.querySelector(`.status-indicator[data-ip="${ip}"]`);
            const timeElement = document.querySelector(`.remaining-time[data-ip="${ip}"]`);

            if (data.success && data.can_view && data.promotion) {
                // Aktif kullanıcı
                activeCount++;

                statusElement.innerHTML = `
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span class="text-sm text-green-600 ml-1 font-medium">Aktif</span>
                `;

                // Kalan süreyi hesapla
                const remainingSeconds = data.promotion.remaining_seconds || 0;
                if (remainingSeconds > 0) {
                    const minutes = Math.floor(remainingSeconds / 60);
                    const seconds = remainingSeconds % 60;
                    timeElement.innerHTML = `
                        <i class="fas fa-clock text-amber-500"></i>
                        <span class="text-sm text-amber-600 ml-1 font-medium">${minutes}:${seconds.toString().padStart(2, '0')}</span>
                    `;
                } else {
                    timeElement.innerHTML = `
                        <i class="fas fa-hourglass-end text-red-500"></i>
                        <span class="text-sm text-red-600 ml-1">Süresi Doldu</span>
                    `;
                }

                return true; // Aktif
            } else {
                // Pasif kullanıcı
                statusElement.innerHTML = `
                    <i class="fas fa-times-circle text-gray-400"></i>
                    <span class="text-sm text-gray-500 ml-1">Pasif</span>
                `;

                timeElement.innerHTML = `
                    <i class="fas fa-minus text-gray-400"></i>
                    <span class="text-sm text-gray-500 ml-1">-</span>
                `;

                return false; // Pasif
            }
        } catch (error) {
            console.error('Error checking IP status:', error);
            const statusElement = document.querySelector(`.status-indicator[data-ip="${ip}"]`);
            const timeElement = document.querySelector(`.remaining-time[data-ip="${ip}"]`);

            statusElement.innerHTML = `
                <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                <span class="text-sm text-yellow-600 ml-1">Hata</span>
            `;

            timeElement.innerHTML = `
                <i class="fas fa-question text-yellow-500"></i>
                <span class="text-sm text-yellow-600 ml-1">Bilinmiyor</span>
            `;

            return false; // Hata durumunda pasif say
        }
    });

    // Tüm promise'ları bekle ve aktif sayısını hesapla
    const results = await Promise.all(promises);
    const finalActiveCount = results.filter(result => result === true).length;

    console.log('Aktif kullanıcı sayısı:', finalActiveCount);

    // Aktif kullanıcı sayısını güncelle
    document.getElementById('active-users-count').textContent = finalActiveCount;
}

// Sayfa yüklendiğinde ve her 10 saniyede bir güncelle
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sayfa yüklendi, IP durumları kontrol ediliyor...');
    updateIPStatuses();
    setInterval(() => {
        console.log('Otomatik güncelleme başlatılıyor...');
        updateIPStatuses();
    }, 10000); // 10 saniye
});

// Manuel yenileme butonu
async function refreshStatuses() {
    // Tüm durumları "yükleniyor" yap
    document.querySelectorAll('.status-indicator').forEach(el => {
        el.innerHTML = `
            <i class="fas fa-spinner fa-spin text-blue-500"></i>
            <span class="text-sm text-zinc-500 ml-1">Kontrol ediliyor...</span>
        `;
    });

    document.querySelectorAll('.remaining-time').forEach(el => {
        el.innerHTML = `
            <i class="fas fa-spinner fa-spin text-amber-500"></i>
            <span class="text-sm text-zinc-500 ml-1">Hesaplanıyor...</span>
        `;
    });

    document.getElementById('active-users-count').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Durumları güncelle
    await updateIPStatuses();
}
</script>

{% endblock admin_content %}
