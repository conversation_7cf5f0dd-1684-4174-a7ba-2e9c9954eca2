{% extends "admin/base.html" %}

{% block breadcrumb %}Odalar / Oda Düzenle{% endblock %}
{% block page_title %}Oda Düzenle{% endblock %}
{% block page_subtitle %}Oda bilgilerini güncelleyin{% endblock %}

{% block head_scripts %}
{{ super() }}
<script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js"></script>
<script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/translations/tr.js"></script>
{% endblock %}

{% block admin_content %}
<!-- Bildirim <PERSON>dal -->
<div id="notificationModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    Oda Güncellendi
                </h3>
                <span id="currentTime" class="text-sm text-gray-500"></span>
            </div>
            <div class="space-y-4">
                <p class="text-gray-600">Oda bilgileri başarıyla güncellendi.</p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Oda Linki:</span>
                        <a id="roomLink" href="#" class="text-teal-600 hover:text-teal-700 text-sm font-medium">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            Odayı Görüntüle
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button onclick="closeNotification()" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors duration-200">
                    Kapat
                </button>
                <a href="{{ url_for('rooms.room_list') }}" class="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors duration-200">
                    Odalar Listesi
                </a>
            </div>
        </div>
    </div>
</div>

<div class="p-6">
    <div class="max-w-7xl mx-auto">
        <!-- Form -->
        <form method="POST" enctype="multipart/form-data" class="bg-white rounded-lg shadow-sm">
            <!-- Dil Seçimi -->
            <div class="border-b border-gray-200">
                <div class="p-4">
                    <div class="flex space-x-4">
                        <button type="button" class="language-btn px-4 py-2 text-sm font-medium rounded-md bg-teal-50 text-teal-700" data-lang="tr">
                            Türkçe İçerik
                        </button>
                        <button type="button" class="language-btn px-4 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700" data-lang="en">
                            English Content
                        </button>
                    </div>
                </div>
            </div>

            <!-- Form İçeriği -->
            <div class="p-6 space-y-6">
                <!-- Dil İçerikleri -->
                <div class="language-content" data-lang="tr">
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Başlık (TR)</label>
                            <input type="text" name="title_tr" value="{{ room.title_tr }}" required
                                   class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-150 ease-in-out">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Açıklama (TR)</label>
                            <textarea name="description_tr" id="summernote_tr" required>{{ room.description_tr }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="language-content hidden" data-lang="en">
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Başlık (EN)</label>
                            <input type="text" name="title_en" value="{{ room.title_en }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring-teal-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Açıklama (EN)</label>
                            <textarea name="description_en" id="summernote_en">{{ room.description_en }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Temel Bilgiler -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Kategori</label>
                        <select name="category_id" required
                                class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent appearance-none bg-no-repeat bg-right transition duration-150 ease-in-out"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg width=\'20\' height=\'20\' xmlns=\'http://www.w3.org/2000/svg\'><path d=\'M7 10l5 5 5-5z\' fill=\'%23555\'/></svg>'); background-position: right 0.75rem center;">
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if room.category_id == category.id %}selected{% endif %}>
                                {{ get_lang_text(category, 'name') }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Manzara Tipi</label>
                        <select name="view_type" required
                                class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent appearance-none bg-no-repeat bg-right transition duration-150 ease-in-out"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg width=\'20\' height=\'20\' xmlns=\'http://www.w3.org/2000/svg\'><path d=\'M7 10l5 5 5-5z\' fill=\'%23555\'/></svg>'); background-position: right 0.75rem center;">
                            {% for key, value in room.VIEW_TYPES.items() %}
                            <option value="{{ key }}" {% if room.view_type == key %}selected{% endif %}>
                                {{ value }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Kapasite (Kişi)</label>
                        <input type="number" name="capacity" value="{{ room.capacity }}" required min="1"
                               class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-150 ease-in-out">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Boyut (m²)</label>
                        <input type="number" name="size" value="{{ room.size }}" required min="1"
                               class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-150 ease-in-out">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Fiyat</label>
                        <input type="number" name="price" value="{{ room.price }}" required min="0" step="0.01"
                               class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-150 ease-in-out">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Para Birimi</label>
                        <select name="currency" required
                                class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent appearance-none bg-no-repeat bg-right transition duration-150 ease-in-out"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg width=\'20\' height=\'20\' xmlns=\'http://www.w3.org/2000/svg\'><path d=\'M7 10l5 5 5-5z\' fill=\'%23555\'/></svg>'); background-position: right 0.75rem center;">
                            <option value="EUR" {% if room.currency == 'EUR' %}selected{% endif %}>Euro (€)</option>
                            <option value="USD" {% if room.currency == 'USD' %}selected{% endif %}>Dolar ($)</option>
                            <option value="TRY" {% if room.currency == 'TRY' %}selected{% endif %}>Türk Lirası (₺)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Rezervasyon Linki</label>
                        <input type="url" name="booking_url" value="{{ room.booking_url }}"
                               class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-150 ease-in-out">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Durum</label>
                        <select name="status" required
                                class="mt-1 block w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent appearance-none bg-no-repeat bg-right transition duration-150 ease-in-out"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg width=\'20\' height=\'20\' xmlns=\'http://www.w3.org/2000/svg\'><path d=\'M7 10l5 5 5-5z\' fill=\'%23555\'/></svg>'); background-position: right 0.75rem center;">
                            <option value="active" {% if room.status == 'active' %}selected{% endif %}>Aktif</option>
                            <option value="inactive" {% if room.status == 'inactive' %}selected{% endif %}>Pasif</option>
                        </select>
                    </div>
                </div>

                <!-- Özellikler -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Oda Özellikleri</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {% for feature in features %}
                        <label class="flex items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer group">
                            <input type="checkbox" name="features[]" value="{{ feature.id }}" 
                                   {% if feature in room.features %}checked{% endif %}
                                   class="form-checkbox h-4 w-4 text-teal-600 focus:ring-2 focus:ring-teal-500 focus:ring-offset-0 transition duration-150 ease-in-out rounded border-gray-300">
                            <span class="ml-3 flex items-center">
                                <i class="{{ feature.icon }} text-gray-500 mr-2 group-hover:text-teal-500 transition-colors"></i>
                                <span class="text-sm text-gray-700 group-hover:text-gray-900 transition-colors">{{ feature.name }}</span>
                            </span>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- Mevcut Görseller -->
                {% if room.gallery_images %}
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mevcut Görseller</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {% for image in room.images_list %}
                        <div class="relative group">
                            <img src="{{ url_for('static', filename='uploads/rooms/' + image) }}"
                                 alt="Oda görseli"
                                 class="w-full h-32 object-cover rounded-lg">
                            <button type="button" 
                                    onclick="removeImage('{{ image }}', this)"
                                    class="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Yeni Görsel Yükleme -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Yeni Görseller</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-teal-500 transition-colors group">
                        <div class="space-y-1 text-center">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-3 group-hover:text-teal-500 transition-colors"></i>
                            <div class="flex text-sm text-gray-600">
                                <label for="images" 
                                      class="relative cursor-pointer bg-white rounded-md font-medium text-teal-600 hover:text-teal-500 transition-colors">
                                    <span>Görsel Yükle</span>
                                    <input id="images" name="images" type="file" class="sr-only" multiple accept="image/*">
                                </label>
                                <p class="pl-1">veya sürükleyip bırakın</p>
                            </div>
                            <p class="text-xs text-gray-500 group-hover:text-gray-600 transition-colors">PNG, JPG, GIF, WEBP max 10MB</p>
                            <div id="selected-images" class="mt-3 grid grid-cols-3 gap-2"></div>
                        </div>
                    </div>
                </div>
                
                <script>
                // Seçilen görselleri önizleme olarak göster
                document.getElementById('images').addEventListener('change', function(e) {
                    const selectedImagesContainer = document.getElementById('selected-images');
                    selectedImagesContainer.innerHTML = '';
                    
                    for (const file of this.files) {
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const imgPreview = document.createElement('div');
                                imgPreview.className = 'relative';
                                imgPreview.innerHTML = `
                                    <img src="${e.target.result}" class="h-20 w-full object-cover rounded" />
                                    <span class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">${file.name}</span>
                                `;
                                selectedImagesContainer.appendChild(imgPreview);
                            }
                            reader.readAsDataURL(file);
                        }
                    }
                });
                </script>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3">
                <a href="{{ url_for('rooms.room_list') }}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition duration-150 ease-in-out focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    İptal
                </a>
                <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 transition duration-150 ease-in-out focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    Kaydet
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summernote -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>

<script>
$(document).ready(function() {
    // Summernote editörlerini başlat
    $('#summernote_tr').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ]
    });

    $('#summernote_en').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ]
    });

    // Dil değiştirme butonları
    $('.language-btn').click(function() {
        const lang = $(this).data('lang');
        $('.language-btn').removeClass('bg-teal-50 text-teal-700').addClass('text-gray-500');
        $(this).addClass('bg-teal-50 text-teal-700').removeClass('text-gray-500');
        $('.language-content').addClass('hidden');
        $(`.language-content[data-lang="${lang}"]`).removeClass('hidden');
    });
});

// Resim silme fonksiyonu
function removeImage(imageName, button) {
    if (confirm('Bu görseli silmek istediğinizden emin misiniz?')) {
        fetch("{{ url_for('rooms.remove_room_image', id=room.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ image: imageName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $(button).closest('.relative').remove();
            } else {
                alert('Görsel silinirken bir hata oluştu');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu');
        });
    }
}
</script>
{% endblock %} 