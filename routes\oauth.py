"""
Google OAuth 2.0 Authentication Routes
"""

import os
import json
import secrets
from flask import Blueprint, request, redirect, url_for, session, flash, current_app
from flask_login import login_required
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

# Geliştirme ortamı için HTTPS zorunluluğunu kaldır
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

oauth_bp = Blueprint('oauth', __name__)

# OAuth 2.0 scopes
SCOPES = [
    'https://www.googleapis.com/auth/analytics.readonly',
    'https://www.googleapis.com/auth/webmasters.readonly'
]

def get_oauth_flow():
    """OAuth flow'u oluştur"""
    try:
        # Credentials dosyasını oku
        credentials_path = os.path.join(current_app.root_path, 'google_analytics_credentials.json')
        
        if not os.path.exists(credentials_path):
            raise FileNotFoundError("OAuth credentials dosyası bulunamadı")
        
        # Flow'u oluştur
        # Redirect URI'yi manuel olarak ayarla
        redirect_uri = request.url_root.rstrip('/') + '/oauth/callback'

        flow = Flow.from_client_secrets_file(
            credentials_path,
            scopes=SCOPES,
            redirect_uri=redirect_uri
        )
        
        return flow
    except Exception as e:
        current_app.logger.error(f"OAuth flow oluşturulamadı: {str(e)}")
        return None

@oauth_bp.route('/oauth/authorize')
@login_required
def oauth_authorize():
    """OAuth yetkilendirme başlat"""
    try:
        flow = get_oauth_flow()
        if not flow:
            flash('OAuth yapılandırması bulunamadı!', 'error')
            return redirect(url_for('integration.google_analytics'))
        
        # State parametresi oluştur (güvenlik için)
        state = secrets.token_urlsafe(32)
        session['oauth_state'] = state
        
        # Authorization URL'i oluştur
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            state=state
        )
        
        return redirect(authorization_url)
        
    except Exception as e:
        current_app.logger.error(f"OAuth yetkilendirme hatası: {str(e)}")
        flash(f'OAuth yetkilendirme hatası: {str(e)}', 'error')
        return redirect(url_for('integration.google_analytics'))

@oauth_bp.route('/oauth/callback')
def oauth_callback():
    """OAuth callback handler"""
    try:
        # State kontrolü
        if 'oauth_state' not in session:
            flash('Geçersiz OAuth oturumu!', 'error')
            return redirect(url_for('integration.google_analytics'))
        
        state = request.args.get('state')
        if state != session.get('oauth_state'):
            flash('OAuth state uyumsuzluğu!', 'error')
            return redirect(url_for('integration.google_analytics'))
        
        # Authorization code'u al
        code = request.args.get('code')
        if not code:
            error = request.args.get('error')
            flash(f'OAuth yetkilendirme reddedildi: {error}', 'error')
            return redirect(url_for('integration.google_analytics'))
        
        # Flow'u oluştur ve token'ı al
        flow = get_oauth_flow()
        if not flow:
            flash('OAuth flow oluşturulamadı!', 'error')
            return redirect(url_for('integration.google_analytics'))

        # Redirect URI'yi manuel olarak ayarla (authorize ile aynı olmalı)
        flow.redirect_uri = request.url_root.rstrip('/') + '/oauth/callback'
        
        # Token'ı fetch et
        flow.fetch_token(authorization_response=request.url)
        
        # Credentials'ı kaydet
        credentials = flow.credentials
        save_oauth_credentials(credentials)
        
        # Session'ı temizle
        session.pop('oauth_state', None)
        
        flash('Google OAuth yetkilendirmesi başarılı!', 'success')
        return redirect(url_for('integration.google_analytics'))
        
    except Exception as e:
        current_app.logger.error(f"OAuth callback hatası: {str(e)}")
        flash(f'OAuth callback hatası: {str(e)}', 'error')
        return redirect(url_for('integration.google_analytics'))

def save_oauth_credentials(credentials):
    """OAuth credentials'ı dosyaya kaydet"""
    try:
        credentials_data = {
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret,
            'scopes': credentials.scopes
        }
        
        # OAuth token'ları ayrı dosyaya kaydet
        token_path = os.path.join(current_app.root_path, 'oauth_tokens.json')
        with open(token_path, 'w') as f:
            json.dump(credentials_data, f, indent=2)
        
        current_app.logger.info("OAuth credentials başarıyla kaydedildi")
        
    except Exception as e:
        current_app.logger.error(f"OAuth credentials kaydedilemedi: {str(e)}")
        raise

def load_oauth_credentials():
    """Kaydedilmiş OAuth credentials'ı yükle"""
    try:
        token_path = os.path.join(current_app.root_path, 'oauth_tokens.json')
        
        if not os.path.exists(token_path):
            return None
        
        with open(token_path, 'r') as f:
            credentials_data = json.load(f)
        
        credentials = Credentials(
            token=credentials_data['token'],
            refresh_token=credentials_data.get('refresh_token'),
            token_uri=credentials_data['token_uri'],
            client_id=credentials_data['client_id'],
            client_secret=credentials_data['client_secret'],
            scopes=credentials_data['scopes']
        )
        
        # Token'ın geçerliliğini kontrol et ve gerekirse yenile
        if credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
            save_oauth_credentials(credentials)  # Yenilenen token'ı kaydet
        
        return credentials
        
    except Exception as e:
        current_app.logger.error(f"OAuth credentials yüklenemedi: {str(e)}")
        return None

@oauth_bp.route('/oauth/revoke')
@login_required
def oauth_revoke():
    """OAuth yetkilendirmesini iptal et"""
    try:
        credentials = load_oauth_credentials()
        if credentials:
            # Google'dan yetkilendirmeyi iptal et
            revoke_url = f'https://oauth2.googleapis.com/revoke?token={credentials.token}'
            import requests
            response = requests.post(revoke_url)
            
            # Yerel token dosyasını sil
            token_path = os.path.join(current_app.root_path, 'oauth_tokens.json')
            if os.path.exists(token_path):
                os.remove(token_path)
        
        flash('OAuth yetkilendirmesi iptal edildi!', 'success')
        
    except Exception as e:
        current_app.logger.error(f"OAuth iptal hatası: {str(e)}")
        flash(f'OAuth iptal hatası: {str(e)}', 'error')
    
    return redirect(url_for('integration.google_analytics'))

@oauth_bp.route('/oauth/status')
@login_required
def oauth_status():
    """OAuth durumunu kontrol et"""
    try:
        credentials = load_oauth_credentials()
        
        if not credentials:
            return {
                'authorized': False,
                'message': 'OAuth yetkilendirmesi yapılmamış'
            }
        
        if credentials.expired:
            return {
                'authorized': False,
                'message': 'OAuth token süresi dolmuş'
            }
        
        return {
            'authorized': True,
            'message': 'OAuth yetkilendirmesi aktif',
            'scopes': credentials.scopes
        }
        
    except Exception as e:
        return {
            'authorized': False,
            'message': f'OAuth durum kontrolü hatası: {str(e)}'
        }
