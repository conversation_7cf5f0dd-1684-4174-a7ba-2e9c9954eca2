{% extends "admin/base.html" %}

{% block breadcrumb %}Rezervasyonlar / Yeni Rezervasyon{% endblock %}
{% block page_title %}Yeni Rezervasyon{% endblock %}
{% block page_subtitle %}Oda için yeni rezervasyon oluştur{% endblock %}

{% block admin_content %}
<div class="p-6 bg-gray-50">
    <div class="max-w-5xl mx-auto">
        <!-- Başlık ve Bilgi -->
        <div class="mb-6 flex flex-wrap items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="bg-blue-50 p-3 rounded-full">
                    <i class="fas fa-calendar-plus text-blue-500 text-xl"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-800">Yeni Rezervasyon</h1>
                    <p class="text-sm text-gray-500">Odalar için yeni bir rezervasyon kaydı oluşturun</p>
                </div>
            </div>
        </div>
        
        <!-- Form -->
        <form action="{{ url_for('reservation.create_reservation') }}" method="POST" id="reservationForm" class="space-y-6">
            <!-- Ana Bilgiler -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Müşteri Bilgileri -->
                        <div class="space-y-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="bg-blue-100 p-2 rounded-full">
                                    <i class="fas fa-user text-blue-500"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">Müşteri Bilgileri</h3>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">İsim Soyisim</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user-circle text-gray-400"></i>
                                        </div>
                                        <input type="text" name="name" id="name" required 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">E-posta</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                        </div>
                                        <input type="email" name="email" id="email" required 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-phone text-gray-400"></i>
                                        </div>
                                        <input type="text" name="phone" id="phone" required 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="guests" class="block text-sm font-medium text-gray-700 mb-1">Misafir Sayısı</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-users text-gray-400"></i>
                                        </div>
                                        <input type="number" name="guests" id="guests" min="1" value="2" required 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Not</label>
                                    <div class="relative">
                                        <div class="absolute top-3 left-3 flex items-start pointer-events-none">
                                            <i class="fas fa-sticky-note text-gray-400"></i>
                                        </div>
                                        <textarea name="message" id="message" rows="3" 
                                                  class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Rezervasyon Bilgileri -->
                        <div class="space-y-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="bg-green-100 p-2 rounded-full">
                                    <i class="fas fa-bed text-green-500"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">Rezervasyon Bilgileri</h3>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="room_id" class="block text-sm font-medium text-gray-700 mb-1">Oda</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-door-open text-gray-400"></i>
                                        </div>
                                        <select name="room_id" id="room_id" required 
                                                class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                                data-price="{{ room.price if room else 0 }}">
                                            {% if room %}
                                            <option value="{{ room.id }}" selected>{{ room.title_tr }} ({{ room.category.name_tr }})</option>
                                            {% else %}
                                            <option value="">-- Oda Seçin --</option>
                                            {% for room_option in rooms %}
                                            <option value="{{ room_option.id }}" data-price="{{ room_option.price }}">{{ room_option.title_tr }} ({{ room_option.category.name_tr }})</option>
                                            {% endfor %}
                                            {% endif %}
                                        </select>
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-chevron-down text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="check_in" class="block text-sm font-medium text-gray-700 mb-1">Giriş Tarihi</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-calendar-check text-gray-400"></i>
                                            </div>
                                            <input type="date" name="check_in" id="check_in" required 
                                                   class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                                   value="{{ check_in|default(today) }}" min="{{ today }}">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="check_out" class="block text-sm font-medium text-gray-700 mb-1">Çıkış Tarihi</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-calendar-times text-gray-400"></i>
                                            </div>
                                            <input type="date" name="check_out" id="check_out" required 
                                                   class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                                   value="{{ check_out|default('') }}" min="{{ tomorrow }}">
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between items-center">
                                        <label for="price" class="block text-sm font-medium text-gray-700 mb-1">Fiyat</label>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Kalış süresi: <span id="nights" class="font-medium">0</span> gece</span>
                                    </div>
                                    <div class="relative flex items-center">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-tag text-gray-400"></i>
                                        </div>
                                        <input type="number" name="price" id="price" required step="0.01" 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                               value="{{ price|default(room.price if room else 0) }}">
                                        <select name="currency" id="currency" class="ml-2 block rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                            <option value="TRY">₺ TL</option>
                                            <option value="EUR">€ EUR</option>
                                            <option value="USD">$ USD</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Durum</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-check-circle text-gray-400"></i>
                                        </div>
                                        <select name="status" id="status" 
                                                class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                            <option value="pending">Beklemede</option>
                                            <option value="confirmed">Onaylandı</option>
                                            <option value="cancelled">İptal Edildi</option>
                                        </select>
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-chevron-down text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="payment-options hidden" id="paymentOptions">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Ödeme Seçenekleri</label>
                                    <div class="space-y-3 bg-gray-50 p-3 rounded-md">
                                        {% for method in payment_methods %}
                                        {% if method.is_active %}
                                        <label class="flex items-start p-3 border rounded-md hover:bg-white cursor-pointer transition-colors">
                                            <input type="radio" name="payment_method_id" value="{{ method.id }}" 
                                                   class="mt-1 form-radio text-blue-600 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                                   {% if loop.first %}checked{% endif %}>
                                            <div class="ml-3">
                                                <span class="font-medium">{{ method.name }}</span>
                                                {% if method.description %}
                                                <p class="text-sm text-gray-500">{{ method.description }}</p>
                                                {% endif %}
                                            </div>
                                        </label>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Ödeme Bölümü -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="border-b border-gray-200">
                    <div class="p-4 flex justify-between items-center bg-gray-50">
                        <div class="flex items-center space-x-2">
                            <div class="bg-purple-100 p-2 rounded-full">
                                <i class="fas fa-credit-card text-purple-500"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Ödeme Bilgileri</h3>
                        </div>
                        <div>
                            <label class="inline-flex items-center bg-white px-3 py-2 rounded-full shadow-sm border border-gray-300">
                                <input type="checkbox" name="collect_payment" id="collectPayment" 
                                       class="form-checkbox h-4 w-4 text-blue-600 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm font-medium">Ödeme Alınacak</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div id="paymentFormContainer" class="hidden space-y-6">
                        <!-- Kart Bilgileri Formu -->
                        <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                            <div class="flex items-center space-x-2 mb-4">
                                <i class="fas fa-credit-card text-gray-500"></i>
                                <h4 class="text-base font-medium text-gray-700">Kart Bilgileri</h4>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="card_holder" class="block text-sm font-medium text-gray-700 mb-1">Kart Sahibi</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" name="card_holder" id="card_holder" 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="card_number" class="block text-sm font-medium text-gray-700 mb-1">Kart Numarası</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-credit-card text-gray-400"></i>
                                        </div>
                                        <input type="text" name="card_number" id="card_number" placeholder="1234 5678 9012 3456" 
                                               class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-1">Son Kullanım Tarihi</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-calendar text-gray-400"></i>
                                            </div>
                                            <input type="text" name="expiry_date" id="expiry_date" placeholder="MM/YY" 
                                                   class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="cvv" class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-lock text-gray-400"></i>
                                            </div>
                                            <input type="text" name="cvv" id="cvv" placeholder="123" 
                                                   class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Taksit Seçenekleri -->
                        <div id="installmentOptions" class="bg-gray-50 rounded-lg p-5 border border-gray-200 hidden">
                            <div class="flex items-center space-x-2 mb-4">
                                <i class="fas fa-receipt text-gray-500"></i>
                                <h4 class="text-base font-medium text-gray-700">Taksit Seçenekleri</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-3" id="installmentOptionsContainer">
                                <!-- Taksit seçenekleri JavaScript ile doldurulacak -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ödeme Özeti -->
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-200 mt-6">
                        <div class="flex items-center space-x-2 mb-4">
                            <i class="fas fa-file-invoice-dollar text-gray-500"></i>
                            <h4 class="text-base font-medium text-gray-700">Ödeme Özeti</h4>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <span class="text-sm text-gray-600">Toplam Geceleme:</span>
                                <span class="font-medium" id="totalNights">0 gece</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <span class="text-sm text-gray-600">Gecelik Fiyat:</span>
                                <span class="font-medium" id="nightlyRate">0.00 <span id="currencyLabel">TL</span></span>
                            </div>
                            <div class="flex justify-between items-center py-2">
                                <span class="text-base font-medium text-gray-700">Toplam Tutar:</span>
                                <span class="font-bold text-lg text-blue-600" id="totalPrice">0.00 <span id="currencyLabel2">TL</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Butonlar -->
            <div class="flex justify-end space-x-3">
                <a href="{{ url_for('reservation.reservation_list') }}" 
                   class="px-5 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-times mr-2"></i>İptal
                </a>
                <button type="submit" 
                        class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save mr-2"></i>Rezervasyonu Kaydet
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    const roomSelect = document.getElementById('room_id');
    const priceInput = document.getElementById('price');
    const nightsSpan = document.getElementById('nights');
    const totalNightsSpan = document.getElementById('totalNights');
    const nightlyRateSpan = document.getElementById('nightlyRate');
    const totalPriceSpan = document.getElementById('totalPrice');
    const collectPaymentCheckbox = document.getElementById('collectPayment');
    const paymentFormContainer = document.getElementById('paymentFormContainer');
    const paymentOptions = document.getElementById('paymentOptions');
    const installmentOptions = document.getElementById('installmentOptions');
    const currencySelect = document.getElementById('currency');
    const currencyLabel = document.getElementById('currencyLabel');
    const currencyLabel2 = document.getElementById('currencyLabel2');
    
    // Fiyat hesaplama fonksiyonu
    function calculatePrice() {
        const checkInDate = new Date(checkInInput.value);
        const checkOutDate = new Date(checkOutInput.value);
        
        if (checkInDate && checkOutDate && checkOutDate > checkInDate) {
            // Geceleme sayısı hesapla
            const diffTime = Math.abs(checkOutDate - checkInDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            nightsSpan.textContent = diffDays;
            totalNightsSpan.textContent = diffDays + ' gece';
            
            // Oda fiyatını al
            let roomPrice = 0;
            if (roomSelect.selectedIndex >= 0) {
                const option = roomSelect.options[roomSelect.selectedIndex];
                roomPrice = parseFloat(option.getAttribute('data-price') || 0);
            }
            
            // Gecelik fiyat
            nightlyRateSpan.innerHTML = roomPrice.toFixed(2) + ' <span id="currencyLabel">' + currencyLabel.textContent + '</span>';
            
            // Toplam fiyat
            const totalPrice = roomPrice * diffDays;
            priceInput.value = totalPrice.toFixed(2);
            totalPriceSpan.innerHTML = totalPrice.toFixed(2) + ' <span id="currencyLabel2">' + currencyLabel2.textContent + '</span>';
        }
    }
    
    // Tarih değişikliklerini izle
    checkInInput.addEventListener('change', calculatePrice);
    checkOutInput.addEventListener('change', calculatePrice);
    roomSelect.addEventListener('change', calculatePrice);
    
    // Ödeme seçeneği işlemleri
    collectPaymentCheckbox.addEventListener('change', function() {
        if (this.checked) {
            paymentFormContainer.classList.remove('hidden');
            paymentOptions.classList.remove('hidden');
        } else {
            paymentFormContainer.classList.add('hidden');
            paymentOptions.classList.add('hidden');
        }
    });
    
    // Form gönderilmeden önce doğrulama
    document.getElementById('reservationForm').addEventListener('submit', function(e) {
        const checkInDate = new Date(checkInInput.value);
        const checkOutDate = new Date(checkOutInput.value);
        
        if (checkOutDate <= checkInDate) {
            e.preventDefault();
            alert('Çıkış tarihi giriş tarihinden sonra olmalıdır.');
            return false;
        }
        
        if (collectPaymentCheckbox.checked) {
            // Kart bilgileri doğrulama
            const cardHolder = document.getElementById('card_holder').value;
            const cardNumber = document.getElementById('card_number').value;
            const expiryDate = document.getElementById('expiry_date').value;
            const cvv = document.getElementById('cvv').value;
            
            if (!cardHolder || !cardNumber || !expiryDate || !cvv) {
                e.preventDefault();
                alert('Ödeme alınacaksa kart bilgileri zorunludur.');
                return false;
            }
        }
    });
    
    // Sayfa yüklendiğinde fiyat hesapla
    calculatePrice();
    
    // Kart numarası formatlama
    const cardNumberInput = document.getElementById('card_number');
    cardNumberInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 0) {
            value = value.match(/.{1,4}/g).join(' ');
        }
        e.target.value = value.substring(0, 19); // 16 rakam + 3 boşluk
    });
    
    // Son kullanma tarihi formatlama
    const expiryDateInput = document.getElementById('expiry_date');
    expiryDateInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 2) {
            value = value.substring(0, 2) + '/' + value.substring(2, 4);
        }
        e.target.value = value.substring(0, 5); // MM/YY formatı
    });
    
    // CVV formatlama
    const cvvInput = document.getElementById('cvv');
    cvvInput.addEventListener('input', function(e) {
        e.target.value = e.target.value.replace(/\D/g, '').substring(0, 3);
    });

    function updateCurrencyLabel() {
        const val = currencySelect.value;
        if (val === 'TRY') { currencyLabel.textContent = 'TL'; currencyLabel2.textContent = 'TL'; }
        else if (val === 'EUR') { currencyLabel.textContent = '€'; currencyLabel2.textContent = '€'; }
        else if (val === 'USD') { currencyLabel.textContent = '$'; currencyLabel2.textContent = '$'; }
    }
    currencySelect.addEventListener('change', function() {
        updateCurrencyLabel();
        priceInput.dispatchEvent(new Event('input'));
        calculatePrice();
    });
    updateCurrencyLabel();

    priceInput.addEventListener('input', function() {
        // Manuel fiyat girildiğinde ödeme özetini güncelle
        const checkInDate = new Date(checkInInput.value);
        const checkOutDate = new Date(checkOutInput.value);
        let diffDays = 1;
        if (checkInDate && checkOutDate && checkOutDate > checkInDate) {
            const diffTime = Math.abs(checkOutDate - checkInDate);
            diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }
        const manualTotal = parseFloat(priceInput.value) || 0;
        const nightly = manualTotal / diffDays;
        nightlyRateSpan.innerHTML = nightly.toFixed(2) + ' <span id="currencyLabel">' + currencyLabel.textContent + '</span>';
        totalPriceSpan.innerHTML = manualTotal.toFixed(2) + ' <span id="currencyLabel2">' + currencyLabel2.textContent + '</span>';
    });
});
</script>
{% endblock %} 