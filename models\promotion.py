from models import db
from datetime import datetime, timedelta, timezone
from sqlalchemy import func

# Türkiye saat dilimi (UTC+3)
TURKEY_TZ = timezone(timedelta(hours=3))

def get_turkey_time():
    """Türkiye saatini döndür"""
    return datetime.now(TURKEY_TZ)

class Promotion(db.Model):
    __tablename__ = 'promotions'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    duration_minutes = db.Column(db.Integer, nullable=False, default=30)
    created_at = db.Column(db.DateTime, default=lambda: get_turkey_time().replace(tzinfo=None))
    is_active = db.Column(db.<PERSON>, default=True)
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    created_by = db.<PERSON>umn(db.Integer, db.<PERSON>ey('user.id'))
    
    # İlişkiler
    views = db.relationship('PromotionView', backref='promotion', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, code, description=None, duration_minutes=30, created_by=None):
        self.code = code
        self.description = description
        self.duration_minutes = duration_minutes
        self.created_by = created_by
        self.created_at = get_turkey_time().replace(tzinfo=None)  # Timezone bilgisini kaldır, sadece Türkiye saatini kaydet
        self.is_active = True
    
    def start_promotion(self):
        """Promosyonu başlat"""
        self.start_time = get_turkey_time().replace(tzinfo=None)
        self.end_time = self.start_time + timedelta(minutes=self.duration_minutes)
        db.session.commit()
    
    def stop_promotion(self):
        """Promosyonu durdur"""
        self.is_active = False
        db.session.commit()
    
    def is_expired(self):
        """Promosyonun süresi dolmuş mu?"""
        if not self.end_time:
            return False
        return get_turkey_time().replace(tzinfo=None) > self.end_time
    
    def is_running(self):
        """Promosyon şu anda aktif mi?"""
        if not self.is_active:
            return False
        if not self.start_time:
            return False
        if self.is_expired():
            return False
        return True
    
    def remaining_time(self):
        """Kalan süre (dakika)"""
        if not self.end_time or self.is_expired():
            return 0
        remaining = self.end_time - get_turkey_time().replace(tzinfo=None)
        return max(0, int(remaining.total_seconds() / 60))

    def remaining_seconds(self):
        """Kalan süre (saniye)"""
        if not self.end_time or self.is_expired():
            return 0
        remaining = self.end_time - get_turkey_time().replace(tzinfo=None)
        return max(0, int(remaining.total_seconds()))
    
    def can_user_view(self, ip_address):
        """Kullanıcı bu promosyonu görebilir mi? (IP kontrolü)"""
        if not self.is_active:
            return False

        # Bugünkü görüntülemeleri kontrol et (Türkiye saati)
        today_start = get_turkey_time().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

        existing_view = PromotionView.query.filter_by(
            promotion_id=self.id,
            ip_address=ip_address
        ).filter(
            PromotionView.first_view >= today_start
        ).first()

        if existing_view:
            # Eğer daha önce görüntülediyse ve süre dolmadıysa gösterebilir
            time_since_first_view = get_turkey_time().replace(tzinfo=None) - existing_view.first_view
            if time_since_first_view.total_seconds() / 60 < self.duration_minutes:
                return True
            else:
                return False

        return True
    
    def record_view(self, ip_address):
        """Görüntülemeyi kaydet ve kullanıcı için promosyon süresini başlat"""
        today_start = get_turkey_time().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

        existing_view = PromotionView.query.filter_by(
            promotion_id=self.id,
            ip_address=ip_address
        ).filter(
            PromotionView.first_view >= today_start
        ).first()

        if existing_view:
            # Mevcut görüntülemeyi güncelle
            existing_view.last_view = get_turkey_time().replace(tzinfo=None)
            existing_view.view_count += 1
        else:
            # Yeni görüntüleme kaydı oluştur - Bu IP için promosyon süresi başlıyor
            new_view = PromotionView(
                promotion_id=self.id,
                ip_address=ip_address,
                first_view=get_turkey_time().replace(tzinfo=None),
                last_view=get_turkey_time().replace(tzinfo=None),
                view_count=1
            )
            db.session.add(new_view)

        db.session.commit()

    def get_remaining_time_for_ip(self, ip_address):
        """Belirli IP için kalan süreyi getir"""
        today_start = get_turkey_time().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

        existing_view = PromotionView.query.filter_by(
            promotion_id=self.id,
            ip_address=ip_address
        ).filter(
            PromotionView.first_view >= today_start
        ).first()

        if not existing_view:
            return self.duration_minutes * 60  # Henüz başlamamış, tam süre

        # İlk görüntülemeden bu yana geçen süre
        time_since_first_view = get_turkey_time().replace(tzinfo=None) - existing_view.first_view
        elapsed_seconds = time_since_first_view.total_seconds()
        total_seconds = self.duration_minutes * 60

        remaining = max(0, total_seconds - elapsed_seconds)
        return int(remaining)
    
    @staticmethod
    def get_active_promotion():
        """Aktif promosyonu getir (artık sadece is_active kontrolü)"""
        return Promotion.query.filter_by(is_active=True).first()
    
    @staticmethod
    def cleanup_expired():
        """Süresi dolmuş promosyonları temizle"""
        expired_promotions = Promotion.query.filter(
            Promotion.end_time < get_turkey_time().replace(tzinfo=None)
        ).filter_by(is_active=True).all()

        for promotion in expired_promotions:
            promotion.is_active = False

        db.session.commit()
        return len(expired_promotions)
    
    def to_dict(self):
        """JSON için dict'e çevir"""
        return {
            'id': self.id,
            'code': self.code,
            'description': self.description,
            'duration_minutes': self.duration_minutes,
            'is_active': self.is_active,
            'is_running': self.is_running(),
            'remaining_time': self.remaining_time(),
            'remaining_seconds': self.remaining_seconds(),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Promotion {self.code}>'


class PromotionView(db.Model):
    __tablename__ = 'promotion_views'
    
    id = db.Column(db.Integer, primary_key=True)
    promotion_id = db.Column(db.Integer, db.ForeignKey('promotions.id'), nullable=False)
    ip_address = db.Column(db.String(45), nullable=False)  # IPv6 için 45 karakter
    first_view = db.Column(db.DateTime, default=lambda: get_turkey_time().replace(tzinfo=None))
    last_view = db.Column(db.DateTime, default=lambda: get_turkey_time().replace(tzinfo=None))
    view_count = db.Column(db.Integer, default=1)
    end_time = db.Column(db.DateTime)  # Görüntüleme süresi bitişi
    
    def __init__(self, promotion_id, ip_address, first_view, last_view, view_count=1):
        self.promotion_id = promotion_id
        self.ip_address = ip_address
        self.first_view = first_view
        self.last_view = last_view
        self.view_count = view_count
        
        # Promosyon süresini hesapla
        promotion = Promotion.query.get(promotion_id)
        if promotion:
            self.end_time = first_view + timedelta(minutes=promotion.duration_minutes)
    
    def is_expired(self):
        """Görüntüleme süresi dolmuş mu?"""
        if not self.end_time:
            # End time hesaplanmamış, promosyonu bul ve hesapla
            promotion = Promotion.query.get(self.promotion_id)
            if not promotion:
                return True  # Promosyon bulunamadı, süresi dolmuş say
                
            # İlk görüntülemeden bu yana geçen süre
            time_since_first_view = get_turkey_time().replace(tzinfo=None) - self.first_view
            return time_since_first_view.total_seconds() / 60 >= promotion.duration_minutes
            
        return get_turkey_time().replace(tzinfo=None) > self.end_time
    
    def get_remaining_seconds(self):
        """Kalan süre (saniye)"""
        if not self.end_time:
            # End time hesaplanmamış, promosyonu bul ve hesapla
            promotion = Promotion.query.get(self.promotion_id)
            if not promotion:
                return 0  # Promosyon bulunamadı, süre kalmamış
                
            # İlk görüntülemeden bu yana geçen süre
            time_since_first_view = get_turkey_time().replace(tzinfo=None) - self.first_view
            total_seconds = promotion.duration_minutes * 60
            return max(0, int(total_seconds - time_since_first_view.total_seconds()))
            
        # End time varsa, doğrudan hesapla
        remaining = self.end_time - get_turkey_time().replace(tzinfo=None)
        return max(0, int(remaining.total_seconds()))
    
    def __repr__(self):
        return f'<PromotionView {self.ip_address} - {self.promotion_id}>'
