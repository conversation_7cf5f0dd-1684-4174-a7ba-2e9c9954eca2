<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zeppelin Yönetim Paneli</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gold': '#C6A87D',
                        'dark-gold': '#A58A63',
                        'dark': '#141414',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/27.1.0/classic/ckeditor.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/27.1.0/classic/translations/tr.js"></script>

    <!-- jQuery ve Bootstrap -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Summernote -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/lang/summernote-tr-TR.min.js"></script>

    <style>
        /* Genel Stiller */
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(to bottom right, #eef0f2, #f2f4f6);
            min-height: 100vh;
        }

        /* Glassmorphism Efektleri */
        .glass-card {
            background: linear-gradient(145deg, rgba(242, 244, 246, 0.95), rgba(238, 240, 242, 0.9));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(198, 168, 125, 0.1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }

        .glass-effect {
            background: rgba(242, 244, 246, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(198, 168, 125, 0.1);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.03);
        }

        /* CKEditor Özelleştirmeleri */
        .ck-editor__editable {
            min-height: 300px;
            max-height: 600px;
            background: #ffffff !important;
            color: #333 !important;
        }

        .ck-editor__editable:focus {
            border-color: #C6A87D !important;
            box-shadow: 0 0 0 2px rgba(198, 168, 125, 0.2) !important;
        }

        /* Animasyonlar */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        /* Flash Mesajları */
        .flash-message {
            animation: slideIn 0.3s ease-out;
            transition: all 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Scrollbar Stilleri */
        ::-webkit-scrollbar {
            width: 3px;
        }

        ::-webkit-scrollbar-track {
            background: #eef0f2;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(198, 168, 125, 0.3);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(198, 168, 125, 0.5);
        }

        @keyframes fade-in {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in { animation: fade-in 0.25s ease; }
        
        /* Form Elemanları İyileştirmeleri */
        .form-input,
        .form-select,
        .form-textarea,
        .form-multiselect,
        .form-checkbox,
        .form-radio {
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: rgba(59, 130, 246, 0.5);
            --tw-ring-offset-shadow: 0 0 #0000;
            --tw-ring-shadow: 0 0 #0000;
        }
        
        /* Input, Select ve Textarea Stilleri */
        .form-input,
        .form-select,
        .form-textarea {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: #ffffff;
            border-color: #d1d5db;
            border-width: 1px;
            border-radius: 0.375rem;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            width: 100%;
            color: #111827;
            transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
            transition-duration: 200ms;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        /* Hover Efektleri */
        .form-input:hover,
        .form-select:hover,
        .form-textarea:hover {
            border-color: #9ca3af;
        }
        
        /* Disabled Durumu */
        .form-input:disabled,
        .form-select:disabled,
        .form-textarea:disabled {
            background-color: #f3f4f6;
            border-color: #e5e7eb;
            color: #9ca3af;
            cursor: not-allowed;
        }
        
        /* Placeholder Rengi */
        .form-input::placeholder,
        .form-textarea::placeholder {
            color: #9ca3af;
            opacity: 1;
        }
        
        /* Select İçin Özel Stiller */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
        
        /* Checkbox ve Radio Stilleri */
        .form-checkbox,
        .form-radio {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            display: inline-block;
            vertical-align: middle;
            background-origin: border-box;
            user-select: none;
            flex-shrink: 0;
            height: 1rem;
            width: 1rem;
            background-color: #fff;
            border-color: #d1d5db;
            border-width: 1px;
        }
        
        .form-checkbox {
            border-radius: 0.25rem;
        }
        
        .form-radio {
            border-radius: 100%;
        }
        
        .form-checkbox:checked,
        .form-radio:checked {
            border-color: transparent;
            background-color: currentColor;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        .form-checkbox:checked {
            background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
        }
        
        .form-radio:checked {
            background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
        }
        
        .form-checkbox:focus,
        .form-radio:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
            border-color: #3b82f6;
        }
        
        /* Buton Stilleri */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.375rem;
            font-weight: 500;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
            transition-duration: 200ms;
            border: 1px solid transparent;
        }
        
        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: #ffffff;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: #ffffff;
            color: #374151;
            border-color: #d1d5db;
        }
        
        .btn-secondary:hover {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        
        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }
        
        /* Form Grupları */
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
        }
        
        /* Form Hata Durumları */
        .form-input.error,
        .form-select.error,
        .form-textarea.error {
            border-color: #ef4444;
        }
        
        .form-input.error:focus,
        .form-select.error:focus,
        .form-textarea.error:focus {
            box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.25);
        }
        
        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        /* Input Grupları */
        .input-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            width: 100%;
        }
        
        .input-group > .form-input {
            position: relative;
            flex: 1 1 auto;
            width: 1%;
            min-width: 0;
        }
        
        .input-group-prepend,
        .input-group-append {
            display: flex;
        }
        
        .input-group-text {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.5;
            color: #374151;
            text-align: center;
            white-space: nowrap;
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
        }
        
        .input-group-prepend .input-group-text {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        
        .input-group-append .input-group-text {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        
        .input-group > .form-input:not(:first-child) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        
        .input-group > .form-input:not(:last-child) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    </style>
</head>

<body>
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        {% include 'admin/sidebar.html' %}

        <!-- Ana İçerik -->
        <div class="flex-1 flex flex-col">
            <!-- Üst Bar -->
             <header class="sticky top-0 z-40 w-full">
                <div class="flex items-center justify-between h-20 px-4 md:px-8 bg-white/90 backdrop-blur-lg border-b border-gray-200/50 shadow-sm">
                    <!-- Sol Taraf -->
                    <div class="flex items-center space-x-4">
                        <a href="{{ url_for('admin.dashboard') }}" class="flex items-center space-x-3 group">
                            <div class="w-11 h-11 flex items-center justify-center rounded-full bg-gradient-to-br from-zinc-800 to-zinc-900 shadow-lg group-hover:scale-105 transition-transform duration-300">
                                <i class="fas fa-gem text-gold text-xl"></i>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-base font-bold text-slate-800 tracking-wider">
                                    {% block page_title %}Kontrol Paneli{% endblock %}
                                </span>
                                <span class="text-xs text-slate-500 font-medium tracking-wide">
                                    {% block page_subtitle %}Genel Bakış{% endblock %}
                                </span>
                            </div>
                        </a>
                    </div>

                    <!-- Sağ Taraf -->
                    <div class="flex items-center space-x-2">
                        <!-- Siteye Git -->
                        <a href="{{ url_for('main.index') }}" target="_blank" class="hidden sm:flex items-center space-x-2 px-4 h-10 text-sm font-semibold text-slate-700 bg-white border border-slate-200 rounded-full hover:bg-slate-100 hover:text-slate-900 hover:border-slate-300 transition-all duration-300 group shadow-sm">
                            <i class="fas fa-external-link-alt text-slate-500 group-hover:text-slate-700 transition-colors"></i>
                            <span>Siteyi Gör</span>
                        </a>

                        <!-- Bildirimler -->
                        <div class="relative">
                            <button id="notificationBtn" class="w-10 h-10 flex items-center justify-center text-slate-500 transition-colors duration-200 rounded-full hover:text-dark-gold hover:bg-amber-100/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gold/50">
                                <i class="fas fa-bell text-lg"></i>
                                <span class="absolute top-2 right-2 flex h-2 w-2">
                                    <span class="absolute inline-flex w-full h-full bg-red-400 rounded-full opacity-75 animate-ping"></span>
                                    <span class="relative inline-flex w-2 h-2 bg-red-500 rounded-full border border-white"></span>
                                </span>
                            </button>
                            <div id="notificationDropdown" class="hidden absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 z-[9999] overflow-hidden animate-fade-in">
                                <div class="p-4 border-b border-gray-200/70">
                                    <h3 class="font-semibold text-gray-800">Bildirimler</h3>
                                </div>
                                <div class="py-1 max-h-80 overflow-y-auto">
                                    <a href="#" class="flex items-start px-4 py-3 text-sm text-gray-700 transition-colors hover:bg-gray-100/70">
                                        <div class="w-10 text-center"><i class="fas fa-calendar-check text-blue-500"></i></div>
                                        <div class="ml-2">
                                            <p class="font-medium text-gray-800">Yeni Rezervasyon</p>
                                            <p class="text-xs text-gray-500">Standart Oda - 2 gece</p>
                                        </div>
                                    </a>
                                    <a href="#" class="flex items-start px-4 py-3 text-sm text-gray-700 transition-colors hover:bg-gray-100/70">
                                        <div class="w-10 text-center"><i class="fas fa-feather-alt text-green-500"></i></div>
                                        <div class="ml-2">
                                            <p class="font-medium text-gray-800">Yeni Blog Yazısı</p>
                                            <p class="text-xs text-gray-500">"Yaz Fırsatları" yayınlandı.</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="p-2 border-t border-gray-200/70">
                                    <a href="#" class="block w-full px-4 py-2 text-sm font-medium text-center text-dark-gold transition-colors rounded-lg hover:bg-amber-100/50">Tüm bildirimleri gör</a>
                                </div>
                            </div>
                        </div>

                        <!-- Kullanıcı Menüsü -->
                        <div class="relative">
                            <button id="userMenuBtn" class="flex items-center space-x-3 transition-opacity rounded-full focus:outline-none hover:opacity-90">
                                <img src="https://ui-avatars.com/api/?name=Admin&background=C6A87D&color=fff&rounded=true&size=40" alt="Avatar" class="w-10 h-10 rounded-full border-2 border-gold/50 shadow-md">
                                <div class="hidden md:flex flex-col items-start -space-y-1">
                                    <span class="text-sm font-semibold text-slate-800">Admin</span>
                                    <span class="text-xs text-slate-500">Yönetici</span>
                                </div>
                            </button>
                            <div id="userDropdown" class="hidden absolute right-0 mt-3 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 z-[9999] overflow-hidden animate-fade-in">
                                <div class="flex items-center p-4 border-b border-gray-200/70">
                                    <img src="https://ui-avatars.com/api/?name=Admin&background=C6A87D&color=fff&rounded=true&size=40" alt="Avatar" class="w-10 h-10 rounded-full">
                                    <div class="ml-3">
                                        <p class="font-semibold text-gray-800">Admin</p>
                                        <p class="text-xs text-gray-500"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="py-2">
                                    <a href="#" data-toggle="modal" data-target="#supportModal" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100/70"><i class="fas fa-question-circle w-6 text-gray-500"></i> Yardım</a>
                                </div>
                                <div class="p-2 border-t border-gray-200/70">
                                    <a href="{{ url_for('auth.logout') }}" class="flex items-center w-full px-4 py-2 text-sm text-red-600 rounded-lg hover:bg-red-50"><i class="fas fa-sign-out-alt w-6"></i> Çıkış Yap</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Ana Sayfa İçeriği -->
            <main class="flex-1 overflow-y-auto">
                <div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
                    <!-- Flash Mesajları -->
                    <div id="flash-container" class="fixed top-24 right-8 z-[9999] w-full max-w-sm">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="flash-message mb-4 rounded-lg border-l-4 px-4 py-3 shadow-lg 
                                        {% if category == 'error' %} border-red-500 bg-red-100 text-red-700
                                        {% elif category == 'success' %} border-green-500 bg-green-100 text-green-700
                                        {% else %} border-blue-500 bg-blue-100 text-blue-700
                                        {% endif %}"
                                        role="alert">
                                        <p class="font-bold">{{ 'Hata' if category == 'error' else 'Başarılı' if category == 'success' else 'Bilgi' }}</p>
                                        <p>{{ message }}</p>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                    </div>

                    {% block admin_content %}
                    <h2 class="text-3xl font-bold text-gray-800">Hoş Geldiniz, {{ current_user.username }}!</h2>
                    <p class="mt-2 text-lg text-gray-600">Bugün ne yapmak istersiniz?</p>
                    {% endblock %}
                </div>
            </main>

            <!-- Alt Bilgi -->
            <footer class="bg-white border-t border-gray-200 py-4 px-6 shadow-sm mt-auto">
                <div class="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
                    <div class="text-sm text-gray-600">
                        &copy; 2025 Zeppelin | AYNWeb Yazılım
                    </div>
                    <!-- Ziyaretçi İstatistikleri -->
                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-gold mr-2"></i>
                            <span id="visitor-stats">
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-md" title="Bugün">
                                    <i class="fas fa-calendar-day mr-1"></i>
                                    <span id="today-visitors">Yükleniyor...</span>
                                </span>
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded-md ml-2" title="Bu Ay">
                                    <i class="fas fa-calendar-week mr-1"></i>
                                    <span id="month-visitors">Yükleniyor...</span>
                                </span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-md ml-2" title="Toplam">
                                    <i class="fas fa-users mr-1"></i>
                                    <span id="total-visitors">Yükleniyor...</span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <div class="text-sm">
                        <a href="#" class="px-4 py-2 text-sm font-semibold text-slate-600 bg-slate-100 rounded-full hover:bg-slate-200 hover:text-slate-800 transition-all duration-200" data-toggle="modal" data-target="#supportModal">
                            <i class="fas fa-headset mr-2"></i>Destek
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Destek Modalı -->
    <div class="modal fade" id="supportModal" tabindex="-1" role="dialog" aria-labelledby="supportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content glass-card border-0 shadow-2xl rounded-2xl overflow-hidden">
                <div class="modal-header border-b border-gray-200/50 px-6 py-4">
                    <h5 class="modal-title text-lg font-semibold text-slate-800" id="supportModalLabel">Destek Bilgileri</h5>
                    <button type="button" class="close text-slate-500 hover:text-slate-800" data-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body p-6 text-center">
                    <div class="flex flex-col items-center">
                        <img src="https://ui-avatars.com/api/?name=Ayhan+Zor&background=C6A87D&color=fff&size=80&rounded=true" alt="Ayhan Zor" class="w-20 h-20 rounded-full mb-4 shadow-md">
                        <p class="text-xl font-bold text-slate-900">Ayhan ZOR</p>
                        <p class="text-slate-600 font-medium mt-1">Teknik Destek</p>
                        <div class="mt-4 text-left space-y-2">
                            <p class="text-slate-700"><i class="fas fa-phone-alt w-5 text-dark-gold"></i> 0553 927 38 15</p>
                            <p class="text-slate-700"><i class="fas fa-info-circle w-5 text-dark-gold"></i> Herhangi bir sorun anında ulaşabilirsiniz.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-t border-gray-200/50 px-6 py-3 bg-gray-50/50">
                    <button type="button" class="px-4 py-2 text-sm font-medium text-slate-600 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors" data-dismiss="modal">Kapat</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dropdown menüleri yönet
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationDropdown = document.getElementById('notificationDropdown');

        function toggleDropdown(btn, dropdown) {
            if (dropdown.classList.contains('hidden')) {
                // Tüm açık dropdownları kapat
                document.querySelectorAll('.absolute.z-\\[9999\\]').forEach(d => d.classList.add('hidden'));
                dropdown.classList.remove('hidden');
            } else {
                dropdown.classList.add('hidden');
            }
        }

        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleDropdown(userMenuBtn, userDropdown);
        });

        notificationBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleDropdown(notificationBtn, notificationDropdown);
        });

        // Dışarı tıklamayı dinle
        document.addEventListener('click', (e) => {
            if (!userDropdown.contains(e.target)) {
                userDropdown.classList.add('hidden');
            }
            if (!notificationDropdown.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });

        // Flash mesajlarını 5 saniye sonra kaldır
        setTimeout(function() {
            let flashContainer = document.getElementById('flash-container');
            if (flashContainer) {
                let messages = flashContainer.children;
                for (let i = 0; i < messages.length; i++) {
                    messages[i].style.transition = 'opacity 0.5s';
                    messages[i].style.opacity = '0';
                    setTimeout(() => messages[i].remove(), 500);
                }
            }
        }, 5000);
        
        // Ziyaretçi istatistiklerini almak için AJAX isteği
        fetch('/api/visitor-stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('today-visitors').textContent = data.today;
                document.getElementById('month-visitors').textContent = data.month;
                document.getElementById('total-visitors').textContent = data.total;
            })
            .catch(error => {
                console.error('Ziyaretçi istatistikleri alınamadı:', error);
                document.getElementById('today-visitors').textContent = '0';
                document.getElementById('month-visitors').textContent = '0';
                document.getElementById('total-visitors').textContent = '0';
            });
    });
    </script>

    <!-- Diğer Scriptler -->
    {% block scripts %}{% endblock %}
</body>
</html>