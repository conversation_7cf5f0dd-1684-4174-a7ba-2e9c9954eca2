{% extends "admin/base.html" %}

{% block page_title %}<PERSON>ni <PERSON>{% endblock %}

{% block admin_content %}
<div class="px-6">
    <!-- Başlık -->
    <div class="flex items-center mb-6">
        <a href="{{ url_for('admin_promotions.promotion_list') }}" 
           class="text-zinc-600 hover:text-zinc-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-2xl font-light text-zinc-800">Yeni Promosyon Oluştur</h1>
            <p class="text-zinc-600 mt-1">Zaman sınırlı promosyon kodu oluşturun</p>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <div class="bg-white rounded-xl shadow-sm border border-zinc-100 p-6">
            <form method="POST" action="{{ url_for('admin_promotions.promotion_create') }}">
                <!-- Promosyon Kodu -->
                <div class="mb-6">
                    <label for="code" class="block text-sm font-medium text-zinc-700 mb-2">
                        Promosyon Kodu *
                    </label>
                    <input type="text" 
                           id="code" 
                           name="code" 
                           required
                           maxlength="100"
                           class="w-full px-4 py-3 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                           placeholder="Örn: YILBASI2025, INDIRIM30">
                    <p class="text-xs text-zinc-500 mt-1">Promosyon kodunuz benzersiz olmalıdır</p>
                </div>

                <!-- Açıklama -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-zinc-700 mb-2">
                        Açıklama
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-4 py-3 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                              placeholder="Promosyon hakkında kısa açıklama..."></textarea>
                </div>

                <!-- Süre -->
                <div class="mb-6">
                    <label for="duration_minutes" class="block text-sm font-medium text-zinc-700 mb-2">
                        Geçerlilik Süresi (Dakika) *
                    </label>
                    <div class="relative">
                        <input type="number" 
                               id="duration_minutes" 
                               name="duration_minutes" 
                               required
                               min="1"
                               max="1440"
                               value="30"
                               class="w-full px-4 py-3 border border-zinc-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-zinc-500 text-sm">dakika</span>
                        </div>
                    </div>
                    <p class="text-xs text-zinc-500 mt-1">1-1440 dakika arası (maksimum 24 saat)</p>
                    
                    <!-- Hızlı seçenekler -->
                    <div class="mt-3 flex flex-wrap gap-2">
                        <button type="button" onclick="setDuration(15)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            15 dk
                        </button>
                        <button type="button" onclick="setDuration(30)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            30 dk
                        </button>
                        <button type="button" onclick="setDuration(60)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            1 saat
                        </button>
                        <button type="button" onclick="setDuration(120)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            2 saat
                        </button>
                        <button type="button" onclick="setDuration(360)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            6 saat
                        </button>
                        <button type="button" onclick="setDuration(720)" 
                                class="px-3 py-1 text-xs bg-zinc-100 text-zinc-700 rounded-md hover:bg-zinc-200 transition-colors">
                            12 saat
                        </button>
                    </div>
                </div>

                <!-- Bilgi Kutusu -->
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">Yeni Promosyon Sistemi</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li><strong>Kullanıcı çarkı çevirdiğinde süre başlar</strong> - Artık admin panelinden başlatmaya gerek yok</li>
                                    <li>Her IP adresi için ayrı süre sayacı çalışır</li>
                                    <li>Kullanıcı çarkı çevirdiği andan itibaren belirlenen süre boyunca promosyonu görebilir</li>
                                    <li>Her IP günde sadece bir kez çark çevirebilir</li>
                                    <li>Promosyon oluşturduktan sonra otomatik olarak aktif hale gelir</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Butonlar -->
                <div class="flex items-center justify-end space-x-4">
                    <a href="{{ url_for('admin_promotions.promotion_list') }}" 
                       class="px-6 py-2.5 border border-zinc-300 text-zinc-700 rounded-lg hover:bg-zinc-50 transition-colors">
                        İptal
                    </a>
                    <button type="submit" 
                            class="bg-gradient-to-r from-amber-500 to-amber-600 text-white px-6 py-2.5 rounded-lg hover:from-amber-600 hover:to-amber-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-save mr-2"></i>
                        Promosyon Oluştur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function setDuration(minutes) {
    document.getElementById('duration_minutes').value = minutes;
}

// Form validasyonu
document.querySelector('form').addEventListener('submit', function(e) {
    const code = document.getElementById('code').value.trim();
    const duration = parseInt(document.getElementById('duration_minutes').value);
    
    if (!code) {
        alert('Promosyon kodu gereklidir!');
        e.preventDefault();
        return;
    }
    
    if (duration < 1 || duration > 1440) {
        alert('Süre 1-1440 dakika arasında olmalıdır!');
        e.preventDefault();
        return;
    }
});
</script>
{% endblock admin_content %}
