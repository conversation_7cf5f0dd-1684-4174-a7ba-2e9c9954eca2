from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, abort
from flask_login import login_required
from models.activity import db, Activity
from werkzeug.utils import secure_filename
import os
from datetime import datetime
from slugify import slugify
from flask import g

# Blueprint'i tanımla
activities_bp = Blueprint('activities', __name__, url_prefix='/activities')

def save_image(file, folder='activities'):
    if file:
        filename = secure_filename(file.filename)
        base, ext = os.path.splitext(filename)
        filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads', folder)
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        return filename
    return None

# Admin routes
@activities_bp.route('/admin', methods=['GET'])
@login_required
def activity_list():
    activities = Activity.query.order_by(Activity.sort_order.asc(), Activity.created_at.desc()).all()
    return render_template('admin/activities/list.html', activities=activities)

@activities_bp.route('/admin/create', methods=['GET', 'POST'])
@login_required
def activity_create():
    if request.method == 'POST':
        try:
            activity = Activity(
                # Türkçe içerik
                title_tr=request.form.get('title_tr'),
                description_tr=request.form.get('description_tr'),
                content_tr=request.form.get('content_tr'),
                
                # İngilizce içerik
                title_en=request.form.get('title_en'),
                description_en=request.form.get('description_en'),
                content_en=request.form.get('content_en'),
                
                # Diğer alanlar
                duration=request.form.get('duration'),
                difficulty=request.form.get('difficulty'),
                price=request.form.get('price', type=float),
                max_participants=request.form.get('max_participants', type=int),
                sort_order=request.form.get('sort_order', type=int, default=0),
                status=request.form.get('status', 'active')
            )

            # Ana resmi işle
            featured_image = request.files.get('featured_image')
            if featured_image and featured_image.filename:
                filename = save_image(featured_image)
                if filename:
                    activity.featured_image = filename

            # Galeri resimlerini işle
            saved_images = []
            files = request.files.getlist('gallery_images')
            for file in files:
                if file and file.filename:
                    filename = save_image(file)
                    if filename:
                        saved_images.append(filename)
            
            if saved_images:
                activity.gallery_images = ','.join(saved_images)

            db.session.add(activity)
            db.session.commit()
            
            flash('Aktivite başarıyla oluşturuldu!', 'success')
            return redirect(url_for('activities.activity_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')

    return render_template('admin/activities/create.html', 
                         difficulty_levels=Activity.DIFFICULTY_LEVELS,
                         datetime=datetime)

@activities_bp.route('/admin/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def activity_edit(id):
    activity = Activity.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # Türkçe içerik
            activity.title_tr = request.form.get('title_tr')
            activity.description_tr = request.form.get('description_tr')
            activity.content_tr = request.form.get('content_tr')
            
            # İngilizce içerik
            activity.title_en = request.form.get('title_en')
            activity.description_en = request.form.get('description_en')
            activity.content_en = request.form.get('content_en')
            
            # Diğer alanlar
            activity.duration = request.form.get('duration')
            activity.difficulty = request.form.get('difficulty')
            activity.price = request.form.get('price', type=float)
            activity.max_participants = request.form.get('max_participants', type=int)
            activity.sort_order = request.form.get('sort_order', type=int, default=0)
            activity.status = request.form.get('status', 'active')

            # Ana resmi güncelle
            featured_image = request.files.get('featured_image')
            if featured_image and featured_image.filename:
                filename = save_image(featured_image)
                if filename:
                    activity.featured_image = filename

            # Yeni galeri resimleri ekle
            if 'gallery_images' in request.files:
                files = request.files.getlist('gallery_images')
                current_images = activity.gallery_images.split(',') if activity.gallery_images else []
                for file in files:
                    if file and file.filename:
                        filename = save_image(file)
                        if filename:
                            current_images.append(filename)
                activity.gallery_images = ','.join(current_images) if current_images else None

            db.session.commit()
            flash('Aktivite başarıyla güncellendi!', 'success')
            return redirect(url_for('activities.activity_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')

    return render_template('admin/activities/edit.html', 
                         activity=activity,
                         difficulty_levels=Activity.DIFFICULTY_LEVELS,
                         datetime=datetime)

@activities_bp.route('/admin/delete/<int:id>', methods=['POST'])
@login_required
def activity_delete(id):
    activity = Activity.query.get_or_404(id)
    
    try:
        # Ana resmi sil
        if activity.featured_image:
            image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'activities', activity.featured_image)
            if os.path.exists(image_path):
                os.remove(image_path)
        
        # Galeri resimlerini sil
        if activity.gallery_images:
            for image in activity.images_list:
                image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'activities', image)
                if os.path.exists(image_path):
                    os.remove(image_path)
        
        db.session.delete(activity)
        db.session.commit()
        flash('Aktivite başarıyla silindi!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('activities.activity_list'))

@activities_bp.route('/admin/remove-image/<int:id>', methods=['POST'])
@login_required
def remove_image(id):
    try:
        activity = Activity.query.get_or_404(id)
        data = request.get_json()
        image_name = data.get('image')
        
        if image_name:
            # Görseli dosya sisteminden sil
            image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'activities', image_name)
            if os.path.exists(image_path):
                os.remove(image_path)
            
            # Görseli aktivitenin görsel listesinden kaldır
            if activity.remove_image(image_name):
                db.session.commit()
            
            return jsonify({'success': True, 'message': 'Görsel başarıyla silindi'})
        
        return jsonify({'success': False, 'message': 'Görsel adı belirtilmedi'}), 400
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 400

# Frontend routes
@activities_bp.route('')
def activity_index():
    activities = Activity.get_active_activities()
    current_language = g.get('language', 'tr')
    
    return render_template('activities/index.html', 
                         activities=activities,
                         current_language=current_language)

@activities_bp.route('/<slug>')
def activity_detail(slug):
    activity = Activity.query.filter_by(slug=slug, status='active').first_or_404()
    current_language = g.get('language', 'tr')
    
    # Diğer aktiviteler (benzer öneriler)
    other_activities = Activity.query.filter(
        Activity.id != activity.id,
        Activity.status == 'active'
    ).order_by(Activity.sort_order.asc()).limit(3).all()
    
    return render_template('activities/detail.html', 
                         activity=activity,
                         other_activities=other_activities,
                         current_language=current_language)

@activities_bp.route('/<int:id>')
def activity_detail_id(id):
    activity = Activity.query.get_or_404(id)
    return redirect(url_for('activities.activity_detail', slug=activity.slug), code=301)
