{% extends "base.html" %}

{% block title %}{% if current_language == 'tr' %}<PERSON>da Arama{% else %}Room Search{% endif %}{% endblock title %}

{% block head %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/tr.js"></script>

<style>
    /* <PERSON>onteyner */
    .search-container {
        max-width: 1200px;
        margin: 0 auto;
        margin-top: 120px;
        padding: 2rem 1rem;
        min-height: calc(100vh - 180px);
        display: flex;
        flex-direction: column;
    }
    
    /* Başlık */
    .page-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    /* <PERSON>ma formu */
    .search-form {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .form-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    @media (max-width: 991px) {
        .form-grid {
            flex-direction: column;
        }
    }
    
    @media (min-width: 992px) {
        .form-grid {
            align-items: flex-end;
        }
        
        .form-grid .form-group {
            flex: 1;
            min-width: 0;
        }
        
        .form-grid .form-group:last-child {
            flex: 0 0 auto;
            width: auto;
        }
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
    }
    
    .form-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #4b5563;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-input {
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        color: #1f2937;
        background-color: white;
        width: 100%;
        min-width: 0;
        height: 42px;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #C6A87D;
        box-shadow: 0 0 0 3px rgba(198, 168, 125, 0.2);
    }
    
    .search-button {
        background-color: #C6A87D;
        color: white;
        font-weight: 500;
        padding: 0 1.25rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;
        height: 42px;
        width: 100%;
        min-width: 120px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    @media (min-width: 992px) {
        .search-button {
            height: 42px;
            margin-bottom: 0;
        }
    }
    
    .search-button:hover {
        background-color: #b39669;
    }
    
    /* Dropdown stil düzeltmeleri */
    select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        cursor: pointer;
    }
    
    select::-ms-expand {
        display: none;
    }
    
    /* Özel dropdown stilleri */
    select option {
        font-size: 14px;
        padding: 8px;
        background-color: white;
        color: #333;
    }
    
    /* Dropdown zindex ayarı */
    .relative {
        position: relative;
        z-index: 10;
    }
    
    .relative:focus-within {
        z-index: 20;
    }
    
    /* Sonuçlar */
    .results-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        margin-top: 2rem;
    }
    
    .results-info {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1.5rem;
    }
    
    .results-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 1.5rem;
    }
    
    @media (min-width: 640px) {
        .results-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (min-width: 1024px) {
        .results-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    /* Oda kartı */
    .room-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .room-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    .room-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .room-content {
        padding: 1.25rem;
    }
    
    .room-category {
        font-size: 0.75rem;
        font-weight: 500;
        color: #C6A87D;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.5rem;
    }
    
    .room-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.75rem;
    }
    
    .room-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .room-features {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .room-feature {
        display: inline-flex;
        align-items: center;
        font-size: 0.75rem;
        color: #4b5563;
        background-color: #f3f4f6;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
    }
    
    .room-feature i {
        margin-right: 0.25rem;
        font-size: 0.75rem;
    }
    
    .room-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }
    
    .room-price {
        font-size: 1.25rem;
        font-weight: 600;
        color: #C6A87D;
    }
    
    .room-price-currency {
        font-size: 0.875rem;
        font-weight: 400;
    }
    
    .room-button {
        background-color: #C6A87D;
        color: white;
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: background-color 0.2s;
        text-decoration: none;
        display: inline-block;
    }
    
    .room-button:hover {
        background-color: #b39669;
    }
    
    /* Boş durum */
    .empty-state {
        text-align: center;
        padding: 3rem 0;
    }
    
    .empty-icon {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }
    
    .empty-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #4b5563;
        margin-bottom: 0.5rem;
    }
    
    .empty-text {
        font-size: 0.875rem;
        color: #6b7280;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Line clamp utilities */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;  
        overflow: hidden;
    }
</style>
{% endblock head %}

{% block content %}
<div class="search-container">
    <div class="text-center mb-8">
        <h1 class="text-4xl font-light text-gray-800 mb-2" data-translate="search_available_rooms">{{ _('Müsait Odaları Ara') }}</h1>
        <div class="flex items-center justify-center">
            <div class="h-[1px] w-10 bg-gold"></div>
            <div class="mx-3 text-gold text-sm">⚜</div>
            <div class="h-[1px] w-10 bg-gold"></div>
        </div>
    </div>
    
    <!-- Arama Formu -->
    <div class="bg-white/80 backdrop-blur-lg rounded-xl md:rounded-full shadow-xl max-w-5xl mx-auto overflow-hidden mb-8">
        <form action="{{ url_for('rooms.search_rooms') }}" method="GET" class="flex flex-wrap items-center">
            <!-- Mobile Grid: 2x2 Layout -->
            <div class="w-1/2 md:w-auto md:flex-1 p-2 md:p-3 md:pl-6 border-b border-r md:border-b-0 md:border-r border-gray-200">
                <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="check_in_date">{{ _('Giriş Tarihi') }}</label>
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt text-gold mx-1 md:mx-2"></i>
                    <input type="text" id="check_in" name="check_in" placeholder="Giriş" class="w-full bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700 datepicker" value="{{ check_in }}" required>
                </div>
            </div>
            
            <div class="w-1/2 md:w-auto md:flex-1 p-2 md:p-3 border-b md:border-b-0 md:border-r border-gray-200">
                <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="check_out_date">{{ _('Çıkış Tarihi') }}</label>
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt text-gold mx-1 md:mx-2"></i>
                    <input type="text" id="check_out" name="check_out" placeholder="Çıkış" class="w-full bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700 datepicker" value="{{ check_out }}" required>
                </div>
            </div>
            
            <div class="w-1/2 md:w-auto md:flex-1 p-2 md:p-3 border-r md:border-b-0 md:border-r border-gray-200">
                <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="guests">{{ _('Misafir Sayısı') }}</label>
                <div class="flex items-center relative">
                    <i class="fas fa-user-friends text-gold mx-1 md:mx-2"></i>
                    <select id="guests" name="guests" class="bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700 appearance-none w-full pr-6 md:pr-8 py-1">
                        {% for i in range(1, 11) %}
                        <option value="{{ i }}" {% if guests == i %}selected{% endif %}>{{ i }} {% if current_language == 'tr' %}K{% else %}P{% endif %}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute right-0 text-gold pointer-events-none">
                        <i class="fas fa-chevron-down text-xs"></i>
                    </div>
                </div>
            </div>
            
            <div class="w-1/2 md:w-auto md:flex-1 p-2 md:p-3 md:border-r border-gray-200">
                <label class="hidden md:block text-xs font-medium text-gold mb-1 pl-2" data-translate="room_type">{{ _('Oda Tipi') }}</label>
                <div class="flex items-center relative">
                    <i class="fas fa-bed text-gold mx-1 md:mx-2"></i>
                    <select id="category_id" name="category_id" class="bg-transparent border-0 focus:ring-0 text-xs md:text-sm text-gray-700 appearance-none w-full pr-6 md:pr-8 py-1">
                        <option value="" data-translate="all_room_types">{{ _('Tüm') }}</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>{{ category.name_tr|truncate(8, True) }}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute right-0 text-gold pointer-events-none">
                        <i class="fas fa-chevron-down text-xs"></i>
                    </div>
                </div>
            </div>
            
            <div class="w-full md:w-auto p-3 md:p-2">
                <button type="submit" class="w-full md:w-auto bg-gold hover:bg-gold/90 text-white font-medium py-2 md:py-3 px-4 md:px-8 rounded-full transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-search mr-2"></i>
                    <span data-translate="search">{{ _('Ara') }}</span>
                </button>
            </div>
        </form>
    </div>
    
    <!-- Sonuçlar -->
    {% if search_performed %}
        {% if available_rooms %}
            <div class="mb-8 bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-sm">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-light text-gray-800 mb-2" data-translate="available_rooms">{{ _('Müsait Odalar') }}</h2>
                        <p class="text-sm text-gray-500" data-translate="rooms_found_for_dates">
                            <i class="fas fa-calendar-check text-gold mr-2"></i>
                            {{ check_in }} - {{ check_out }} tarihleri arasında <span class="font-medium">{{ available_rooms|length }}</span> oda bulundu
                        </p>
                    </div>
                    <div class="mt-4 sm:mt-0">
                        <div class="flex items-center text-sm text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                            <i class="fas fa-info-circle text-gold mr-2"></i>
                            <span data-translate="select_room_tip">Rezervasyon için bir oda seçin</span>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for room in available_rooms %}
                    <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                        <div class="relative">
                            <img src="{{ room.image_url }}" alt="{{ room.name }}" class="w-full h-60 object-cover">
                            <div class="absolute top-4 left-4">
                                <div class="bg-gold text-white text-xs uppercase tracking-wider py-1 px-3 rounded-full font-medium">
                                    {{ room.category }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-5">
                            <h3 class="text-xl font-medium text-gray-800 mb-3">{{ room.name }}</h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ room.description|striptags|truncate(100) }}</p>
                            
                            <div class="grid grid-cols-3 gap-2 mb-4">
                                <div class="flex flex-col items-center justify-center bg-gray-50 rounded-lg py-2">
                                    <i class="fas fa-user text-gold mb-1"></i>
                                    <span class="text-xs text-gray-700">{{ room.max_guests }} {{ _('Misafir') }}</span>
                                </div>
                                
                                {% for feature in room.features[:2] %}
                                <div class="flex flex-col items-center justify-center bg-gray-50 rounded-lg py-2">
                                    <i class="{{ feature.icon }} text-gold mb-1"></i>
                                    <span class="text-xs text-gray-700">{{ feature.name }}</span>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                
                                
                                <a href="{{ settings.reservation_link or '#' }}" 
                                   class="bg-gold hover:bg-gold/90 text-white text-sm font-medium py-2 px-4 rounded-lg transition-all duration-300 flex items-center">
                                    <i class="fas fa-check-circle mr-1.5"></i>
                                    <span data-translate="make_reservation">{{ _('Rezervasyon Yap') }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-10 border border-gray-100 shadow-sm text-center max-w-lg mx-auto">
                <div class="w-16 h-16 rounded-full bg-gray-50 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-bed text-gold text-2xl"></i>
                </div>
                <h3 class="text-2xl font-light text-gray-800 mb-3" data-translate="no_available_rooms">{{ _('Müsait Oda Bulunamadı') }}</h3>
                <p class="text-gray-500 mb-6" data-translate="no_rooms_for_selected_dates">
                    {{ _('Seçtiğiniz tarih aralığında müsait oda bulunmamaktadır. Lütfen farklı tarihler seçmeyi deneyin.') }}
                </p>
                <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="inline-flex items-center text-gold hover:text-gold/80 font-medium">
                    <i class="fas fa-arrow-up mr-2"></i>
                    <span data-translate="try_different_dates">Farklı Tarih Seçin</span>
                </button>
            </div>
        {% endif %}
    {% else %}
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-10 border border-gray-100 shadow-sm text-center max-w-lg mx-auto">
            <div class="w-16 h-16 rounded-full bg-gray-50 flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-search text-gold text-2xl"></i>
            </div>
            <h3 class="text-2xl font-light text-gray-800 mb-3" data-translate="start_room_search">{{ _('Oda Aramaya Başlayın') }}</h3>
            <p class="text-gray-500 mb-6" data-translate="use_form_to_search">
                {{ _('Yukarıdaki formu kullanarak müsait odaları arayabilirsiniz.') }}
            </p>
            <div class="flex items-center justify-center text-sm text-gray-600">
                <i class="fas fa-lightbulb text-gold mr-2"></i>
                <span data-translate="search_tip">İstediğiniz tarihleri seçerek arama yapın</span>
            </div>
        </div>
    {% endif %}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dropdown'lar için stil düzeltmesi
        document.querySelectorAll('select').forEach(function(select) {
            // Dropdown açıldığında z-index'i artır
            select.addEventListener('focus', function() {
                this.closest('.relative').style.zIndex = '50';
            });
            
            // Dropdown kapandığında z-index'i sıfırla
            select.addEventListener('blur', function() {
                setTimeout(() => {
                    this.closest('.relative').style.zIndex = '10';
                }, 200);
            });
        });
        
        // Tarih seçici ayarları
        flatpickr('.datepicker', {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            locale: 'tr',
            disableMobile: true,
            position: 'below',
            positionElement: null,
        });
        
        // Check-in ve check-out tarihlerini bağla
        const checkInPicker = flatpickr('#check_in', {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            locale: 'tr',
            disableMobile: true,
            position: 'below',
            positionElement: null,
            onChange: function(selectedDates, dateStr) {
                // Check-out tarihi en az check-in tarihinden bir gün sonra olmalı
                checkOutPicker.set('minDate', new Date(selectedDates[0].getTime() + 86400000));
                
                // Eğer check-out tarihi check-in tarihinden önceyse, check-out tarihini güncelle
                if (checkOutPicker.selectedDates[0] <= selectedDates[0]) {
                    const nextDay = new Date(selectedDates[0].getTime() + 86400000);
                    checkOutPicker.setDate(nextDay);
                }
            }
        });
        
        const checkOutPicker = flatpickr('#check_out', {
            dateFormat: 'Y-m-d',
            minDate: new Date(new Date().getTime() + 86400000), // Yarın
            locale: 'tr',
            disableMobile: true,
            position: 'below',
            positionElement: null,
        });
    });
</script>
{% endblock content %} 