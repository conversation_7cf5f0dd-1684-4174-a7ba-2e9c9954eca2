# -*- coding: utf-8 -*-
import os
import json
import logging
from datetime import datetime, date, timedelta
from urllib.parse import urlparse, urljoin
from werkzeug.urls import url_parse
import xml.etree.ElementTree as ET
import requests
from babel.dates import format_datetime
from flask import Flask, render_template, request, redirect, url_for, flash, abort, jsonify, session, g
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, UserMixin, login_user, logout_user, current_user, login_required
from flask_babel import Babel
from flask_caching import Cache
from flask_session import Session
from sqlalchemy import func, desc
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.middleware.proxy_fix import ProxyFix

from models import db
from utils.helpers import get_turkey_time
from models.user import User
from models.room import Room
from models.reservation import Reservation
from models.blog import BlogPost
from models.menu import Menu
from models.seo import SEO
from models.language import Language
from models.ip_settings import VisitorStats, CountryVisitorStats
from middleware.hotel_network import is_hotel_network, check_hotel_network, HotelNetworkMiddleware
from middleware.keyword_tracker import KeywordTracker
from utils.helpers import get_country_from_ip

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# Önce temel modelleri import edelim
from models.setting import Setting, db
from models.order import Order  # Order modelini import et

# Uygulama başlangıcında klasörleri oluştur
def ensure_upload_folders():
    folders = [
        'static/uploads',
        'static/uploads/food',
        'static/uploads/banners',
        'static/uploads/gallery',
        'static/uploads/sponsors'
    ]
    
    for folder in folders:
        if not os.path.exists(folder):
            try:
                os.makedirs(folder)
                print(f"Klasör oluşturuldu: {folder}")
            except Exception as e:
                print(f"Klasör oluşturma hatası: {str(e)}")

# Flask uygulamasını oluştur
app = Flask(__name__, static_folder='static')
app.jinja_env.auto_reload = True  # Template otomatik yenileme
app.config['TEMPLATES_AUTO_RELOAD'] = True  # Template cache'i devre dışı bırak
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Statik dosyalar için önbelleği devre dışı bırak
app.config['SECRET_KEY'] = 'gizli-anahtar-buraya'

# Jinja2 global fonksiyonlarını ekle
app.jinja_env.globals.update(hasattr=hasattr, min=min)

# SQLite bağlantısı
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'instance', 'hotel.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300
}
app.config['JSON_AS_ASCII'] = False  # JSON UTF-8 desteği

# Session güvenliği
if app.debug:
    app.config['SESSION_COOKIE_SECURE'] = False  # HTTP için
    app.config['SESSION_COOKIE_HTTPONLY'] = True
else:
    app.config['SESSION_COOKIE_SECURE'] = True   # Sadece HTTPS için
    app.config['SESSION_COOKIE_HTTPONLY'] = True

app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=1)

# Dosya yükleme limitleri
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
app.config['UPLOAD_EXTENSIONS'] = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm', '.ogg']

# Dosya yükleme ayarları
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['FOOD_UPLOAD_FOLDER'] = 'static/uploads/food'

# Klasörleri oluştur
ensure_upload_folders()

# Database ve Migration kurulumu
db.init_app(app)
migrate = Migrate(app, db)

# Login manager kurulumu
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Sonra diğer modelleri import edelim
from models.slider import Slider
from models.user import User
from models.menu import Menu
from models.blog import BlogPost
from models.room_category import RoomCategory
from models.room_feature import RoomFeature
from models.room import Room
from models.page import Page
from models.banner import Banner
from models.gallery import Gallery
from models.sponsor import Sponsor
from models.seo import SEO
from models.under_construction import UnderConstruction
from models.whatsapp import WhatsApp
from models.food import Food, FoodCategory
from models.language import Language
from models.reservation import Reservation
from models.activity import Activity
from services.currency_service import CurrencyService, CurrencyRate

# Eğer etkinlikler için ayrı bir model varsa onu da import et
try:
    from models.event import Event
    has_event = True
except ImportError:
    has_event = False

# Blueprint kayıtları
from routes.main import main_bp
from routes.slider import slider_bp, init_app
from routes.users import users_bp
from routes.menu import menu_bp
from routes.pages import pages_bp
from routes.settings import settings_bp
from routes.footer import footer_bp
from routes.blog import blog_bp
from routes.rooms import rooms_bp
from routes.reservation import reservation_bp
from routes.food import food_bp, admin_food_bp
from routes.banner import banner_bp
from routes.gallery import gallery_bp
from routes.admin.sponsors import sponsors_bp
from routes.admin.promotions import promotions_bp, promotion_api_bp
from routes.seo import seo_bp
from routes.integration import integration_bp
from routes.under_construction import under_construction_bp
from routes.whatsapp import whatsapp_bp
from routes.room_settings import room_settings_bp
from routes.activities import activities_bp
from routes.auth import auth_bp
from routes.language import language_bp
from routes.cache_management import cache_bp
from routes.security import security_bp
from routes.server_monitoring import server_bp

# Blueprint'leri kaydet
app.register_blueprint(main_bp)
app.register_blueprint(slider_bp)
init_app(app)
app.register_blueprint(users_bp)
app.register_blueprint(menu_bp)
app.register_blueprint(pages_bp)
app.register_blueprint(settings_bp)
app.register_blueprint(footer_bp)
app.register_blueprint(blog_bp)
app.register_blueprint(rooms_bp)
app.register_blueprint(reservation_bp)
app.register_blueprint(food_bp, url_prefix='/food')
app.register_blueprint(admin_food_bp, url_prefix='/admin/food')
app.register_blueprint(banner_bp)
app.register_blueprint(gallery_bp)
app.register_blueprint(sponsors_bp)
app.register_blueprint(promotions_bp) # Blueprint'in kendi tanımladığı URL önekini kullan
app.register_blueprint(promotion_api_bp) # API endpoint'leri için ayrı blueprint
app.register_blueprint(seo_bp)
app.register_blueprint(integration_bp)
app.register_blueprint(under_construction_bp)
app.register_blueprint(whatsapp_bp)
app.register_blueprint(room_settings_bp)
app.register_blueprint(activities_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(language_bp)
app.register_blueprint(cache_bp)
app.register_blueprint(security_bp)
app.register_blueprint(server_bp)

app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1)
app.wsgi_app = KeywordTracker(app.wsgi_app)  # Önce KeywordTracker
# HotelNetworkMiddleware'i doğrudan app nesnesiyle başlatıyoruz
app.wsgi_app = HotelNetworkMiddleware(app.wsgi_app, app)  # Sonra HotelNetworkMiddleware, app nesnesini de geçiyoruz

# Flask-Babel yapılandırması
def get_locale():
    # Önceden ayarlı bir dil varsa onu kullan
    if 'language' in session:
        return session.get('language', 'tr')
    # İstemci tarayıcı diline göre dil seç
    return request.accept_languages.best_match(['tr', 'en']) or 'tr'

def get_timezone():
    return 'UTC'

# Flask-Babel'i yapılandır
babel = Babel(app, locale_selector=get_locale, timezone_selector=get_timezone)

# Gizli anahtarı ayarla
app.secret_key = os.environ.get('SECRET_KEY', 'zeppelinhotelcappadocia2025')

# Ana sayfa yönlendirmesi
@app.route('/')
def index():
    return redirect(url_for('index2'))

# Cache konfigürasyonu
cache_config = {
    "CACHE_TYPE": "simple"  # Redis yerine memory cache kullan
}
app.config.from_mapping(cache_config)
cache = Cache(app)

# Statik dosyalar için cache kontrolü
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 yıl

# Basit filesystem session kullan
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_FILE_DIR'] = os.path.join(os.getcwd(), 'flask_session')
app.config['SESSION_FILE_THRESHOLD'] = 500  # Maksimum dosya sayısı

@login_manager.user_loader
def load_user(user_id):
    try:
        return User.query.get(int(user_id))
    except Exception as e:
        app.logger.error(f"Kullanıcı yüklenirken hata: {str(e)}")
        return None

# /main route'u artık routes/main.py blueprint'inde yönetiliyor

# Admin paneli route'larını düzenleyelim
@app.route('/admin/')
@app.route('/admin')
@login_required
def admin():
    if not current_user.is_admin:
        flash('Bu sayfaya erişim yetkiniz yok.', 'error')
        return redirect(url_for('index'))

    try:
        # Son 7 günün rezervasyon verilerini çek
        reservations_last_7_days = db.session.query(
            db.func.date(Reservation.created_at),
            db.func.count(Reservation.id)
        ).filter(
            Reservation.created_at >= get_turkey_time() - timedelta(days=7)
        ).group_by(
            db.func.date(Reservation.created_at)
        ).order_by(
            db.func.date(Reservation.created_at)
        ).all()

        # Veriyi Chart.js için hazırla
        last_7_days_data = []
        date_map = {date.strftime('%Y-%m-%d'): count for date, count in reservations_last_7_days}
        for i in range(7):
            day = get_turkey_time() - timedelta(days=i)
            date_str = day.strftime('%Y-%m-%d')
            last_7_days_data.append({
                'date': day.strftime('%b %d'),
                'count': date_map.get(date_str, 0)
            })
        last_7_days_data.reverse()

        # Tüm istatistikleri tek bir yerde topla
        stats = {
            'rooms': {
                'total': Room.query.count(),
                'active': Room.query.filter_by(status='active').count(),
            },
            'reservations': {
                'total': Reservation.query.count(),
                'pending': Reservation.query.filter_by(status='pending').count(),
                'confirmed': Reservation.query.filter_by(status='confirmed').count(),
                'last_7_days': last_7_days_data,
            },
            'posts': {
                'total': BlogPost.query.count(),
                'published': BlogPost.query.filter_by(status='published').count(),
            },
            'users': {
                'total': User.query.count(),
                'admins': User.query.filter_by(is_admin=True).count(),
            }
        }

        # Son aktiviteleri hazırla (örnek veriler)
        recent_activities = [
            {'message': '<strong>Yeni Rezervasyon:</strong> Ahmet Yılmaz - Standart Oda', 'timestamp': get_turkey_time() - timedelta(minutes=15), 'icon': 'fa-calendar-plus', 'color': 'teal'},
            {'message': '<strong>Yeni Blog Yazısı:</strong> "Yaz Tatili İçin 5 Öneri" yayınlandı', 'timestamp': get_turkey_time() - timedelta(hours=2), 'icon': 'fa-feather-alt', 'color': 'green'},
            {'message': '<strong>Kullanıcı Kaydı:</strong> Zeynep Kaya sisteme katıldı.', 'timestamp': get_turkey_time() - timedelta(hours=5), 'icon': 'fa-user-plus', 'color': 'purple'},
            {'message': '<strong>Oda Durumu Güncellendi:</strong> Kral Dairesi temizlendi.', 'timestamp': get_turkey_time() - timedelta(days=1), 'icon': 'fa-bed', 'color': 'blue'},
        ]

        return render_template('admin/dashboard.html', 
                               stats=stats, 
                               recent_activities=recent_activities)
                            
    except Exception as e:
        app.logger.error(f"Admin dashboard error: {str(e)}", exc_info=True)
        flash('Kontrol paneli yüklenirken bir hata oluştu.', 'error')
        # Hata durumunda boş bir dashboard göstermek daha iyi olabilir
        empty_stats = {
            'rooms': {'total': 0, 'active': 0},
            'reservations': {'total': 0, 'pending': 0, 'confirmed': 0, 'last_7_days': []},
            'posts': {'total': 0, 'published': 0},
            'users': {'total': 0, 'admins': 0},
            'activities': {'total': 0, 'active': 0},
            'media': {'galleries': 0, 'sliders': 0, 'banners': 0},
            'sponsors': {'total': 0, 'active': 0},
            'promotions': {'total': 0, 'active': 0},
            'food': {'total': 0, 'active': 0},
            'menu': {'total': 0},
            'pages': {'total': 0, 'published': 0},
            'languages': {'total': 0},
            'seo': {'total': 0}
        }
        return render_template('admin/dashboard.html', stats=empty_stats, recent_activities=[], error=True)

# Login route'u
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = 'remember_me' in request.form
        
        # Kullanıcı doğrulama işlemleri...
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.active:
            login_user(user, remember=remember_me)
            user.last_login = get_turkey_time().replace(tzinfo=None)
            try:
                db.session.commit()
            except Exception as e:
                print(f"DB Error: {str(e)}")
                db.session.rollback()

            next_page = request.args.get('next')
            if next_page and url_parse(next_page).netloc == '':
                return redirect(next_page)
            return redirect(url_for('admin'))
        else:
            # Başarısız giriş denemesini logla
            from routes.security import log_failed_login
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if client_ip and ',' in client_ip:
                client_ip = client_ip.split(',')[0].strip()
            log_failed_login(client_ip)
            flash('Hatalı kullanıcı adı veya şifre!')
    
    return render_template('login.html')

# Logout route'u
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

# Admin login route'u
@app.route('/admin/login', methods=['GET'])
def admin_login():
    return render_template('login.html')  # Ana login sayfasına yönlendir

# İkinci ana sayfa route'u
@app.route('/index2')
def index2():
    from models.room import Room
    rooms = Room.query.filter_by(status='active').all()
    return render_template('index2.html', rooms=rooms)

@app.template_filter('from_json')
def from_json_filter(value):
    if not value:
        return []
    try:
        if isinstance(value, str):
            return json.loads(value)
        elif isinstance(value, (list, dict)):
            return value
        return []
    except (json.JSONDecodeError, TypeError):
        print(f"JSON parse error for value: {value}")
        return []

@app.template_filter('nl2br')
def nl2br_filter(value):
    """Yeni satırları <br> etiketlerine çevir"""
    if not value:
        return ''
    from markupsafe import Markup
    return Markup(str(value).replace('\n', '<br>\n'))

@app.context_processor
def inject_settings():
    try:
        settings = Setting.query.all()
        settings_dict = {}
        for setting in settings:
            # Convert None values to empty string to avoid concatenation issues
            settings_dict[setting.key] = setting.value if setting.value is not None else ''
            
        # Debug için yazdıralım
        print("Mevcut ayarlar:", settings_dict)
        return dict(settings=settings_dict)
    except Exception as e:
        print(f"Settings error: {str(e)}")
        # Return empty strings for common settings to avoid template errors
        return dict(settings={
            'site_logo_small': '',
            'site_logo': '',
            'site_title': '',
            'site_description': ''
        })

def get_locale():
    # Önce URL'den dil parametresini kontrol et
    if request.args.get('lang'):
        lang = request.args.get('lang')
        if lang in ['tr', 'en']:
            session['language'] = lang
            g.language = lang
            return lang

    # Session'da kayıtlı dili kontrol et
    if 'language' in session:
        g.language = session['language']
        return session['language']

    # Kullanıcının tarayıcı dilini kontrol et
    lang = request.accept_languages.best_match(['tr', 'en']) or 'tr'
    g.language = lang
    return lang

@app.before_request
def before_request():
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    if client_ip and ',' in client_ip:
        client_ip = client_ip.split(',')[0].strip()

    print(f"Processing request from IP: {client_ip}")
    is_allowed = is_hotel_network(client_ip)

    session.permanent = True
    session['is_hotel_network'] = is_allowed

    # Güvenlik izleme
    from routes.security import log_request, is_ip_blocked, detect_suspicious_activity

    # IP engellenmiş mi kontrol et
    if is_ip_blocked(client_ip):
        abort(403)  # Forbidden

    # İsteği logla
    log_request(client_ip, request.method, request.path, request.headers.get('User-Agent'))

    # Şüpheli aktivite tespit et
    if detect_suspicious_activity(client_ip, request.path, request.method):
        # Şüpheli aktivite tespit edildi, ek güvenlik önlemleri alınabilir
        pass

    # Her istekte dil ayarını yap
    g.language = get_locale()

    # Template'lerde kullanılacak dil yardımcı fonksiyonlarını ekle
    g.get_text = lambda obj, field: getattr(obj, f'{field}_{g.language}', '')
    
    # Ziyaretçi takibi - sadece admin olmayan ve statik olmayan sayfalar için
    if not request.path.startswith('/admin') and not request.path.startswith('/static'):
        try:
            # Bugünün tarihi
            today = date.today()
            today_str = today.strftime('%Y-%m-%d')
            
            # Sayfa görüntüleme sayısını artır
            stats = VisitorStats.query.filter_by(date=today).first()
            if not stats:
                stats = VisitorStats(date=today, unique_visitors=0, page_views=1)
                db.session.add(stats)
            else:
                stats.page_views += 1
            
            # Bu IP bugün daha önce sayılmış mı kontrol et
            session_key = f"visitor_tracked_{client_ip}_{today_str}"
            if session_key not in session:
                # Bu IP bugün daha önce sayılmamış, şimdi say
                session[session_key] = True
                
                # Ziyaretçi sayısını artır
                stats.unique_visitors += 1
                
                # Ülke bazlı istatistikleri güncelle
                country_info = get_country_from_ip(client_ip)
                if country_info and 'country_code' in country_info and 'country_name' in country_info:
                    country_code = country_info.get('country_code')
                    country_name = country_info.get('country_name')
                    
                    country_stats = CountryVisitorStats.query.filter_by(
                        date=today,
                        country_code=country_code
                    ).first()
                    
                    if not country_stats:
                        country_stats = CountryVisitorStats(
                            date=today,
                            country_code=country_code,
                            country_name=country_name,
                            visitors=1
                        )
                        db.session.add(country_stats)
                    else:
                        country_stats.visitors += 1
            
            db.session.commit()
        except Exception as e:
            print(f"Error tracking visitor: {str(e)}")
            db.session.rollback()

@app.context_processor
def utility_processor():
    def get_lang_text(obj, field):
        """Template'lerde kullanılacak dil seçim yardımcısı"""
        lang = g.get('language', 'tr')
        return getattr(obj, f'{field}_{lang}', '')
    
    return dict(
        get_lang_text=get_lang_text,
        current_language=lambda: g.get('language', 'tr'),
        datetime=datetime
    )

@app.context_processor
def inject_menus():
    try:
        # Tüm aktif menüleri sıralı şekilde getir
        menus = Menu.query.filter_by(parent_id=None).order_by(Menu.order).all()
        return dict(menus=menus)
    except Exception as e:
        app.logger.error(f"Menu injection error: {str(e)}")
        return dict(menus=[])

@app.context_processor
def inject_seo():
    if request.endpoint:
        # Mevcut sayfanın SEO ayarlarını bul
        page_name = request.endpoint.split('.')[-1]
        seo = SEO.query.filter_by(page_name=page_name).first()
        return dict(seo=seo)
    return dict(seo=None)

# Bülten aboneliği için model
class Newsletter(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: get_turkey_time().replace(tzinfo=None))

    def __repr__(self):
        return f'<Newsletter {self.email}>'

# Bülten aboneliği route'u
@app.route('/subscribe-newsletter', methods=['POST'])
def subscribe_newsletter():
    try:
        email = request.form.get('email')
        if not email:
            flash('E-posta adresi gereklidir.', 'error')
            return redirect(request.referrer or url_for('index'))

        # E-posta adresi kontrolü
        existing = Newsletter.query.filter_by(email=email).first()
        if existing:
            if existing.active:
                flash('Bu e-posta adresi zaten kayıtlı!', 'error')
            else:
                existing.active = True
                db.session.commit()
                flash('Bülten aboneliğiniz yeniden aktifleştirildi!', 'success')
            return redirect(request.referrer or url_for('index'))

        # Yeni abone oluştur
        subscriber = Newsletter(email=email)
        db.session.add(subscriber)
        db.session.commit()
        
        flash('Bülten aboneliğiniz başarıyla oluşturuldu!', 'success')
        return redirect(request.referrer or url_for('index'))

    except Exception as e:
        db.session.rollback()
        flash('Bir hata oluştu. Lütfen tekrar deneyin.', 'error')
        return redirect(request.referrer or url_for('index'))

@app.cli.command("init-db")
def init_db():
    """Veritabanını başlat"""
    try:
        db.create_all()
        print('Veritabanı başarıyla oluşturuldu!')
        
        # Admin kullanıcısını oluştur
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                active=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print('Admin kullanıcısı oluşturuldu!')
        
        # Varsayılan dil kayıtlarını ekle
        default_translations = [
            {
                'key': 'welcome_message',
                'tr': 'Hoş Geldiniz',
                'en': 'Welcome',
                'page': 'main'
            },
            {
                'key': 'about_us',
                'tr': 'Hakkımızda',
                'en': 'About Us',
                'page': 'main'
            },
            {
                'key': 'contact_us',
                'tr': 'İletişim',
                'en': 'Contact Us',
                'page': 'main'
            }
        ]
        
        for trans in default_translations:
            if not Language.query.filter_by(key=trans['key']).first():
                language = Language(**trans)
                db.session.add(language)
        
        # Döviz kurlarını başlat
        try:
            CurrencyService.update_db_rates()
            print('Döviz kurları güncellendi!')
        except Exception as e:
            print(f'Döviz kurları güncellenirken hata: {str(e)}')
        
        db.session.commit()
        print('Varsayılan çeviriler eklendi!')
        
    except Exception as e:
        print(f'Hata oluştu: {str(e)}')
        db.session.rollback()

@app.cli.command("update-currency")
def update_currency():
    """Döviz kurlarını güncelle"""
    try:
        result = CurrencyService.update_db_rates()
        if result:
            print("Döviz kurları başarıyla güncellendi!")
            rates = CurrencyService.get_latest_rates()
            for code, rate in rates.items():
                print(f"{code}: Alış: {rate['buying_rate']}, Satış: {rate['selling_rate']}")
        else:
            print("Döviz kurları güncellenemedi!")
    except Exception as e:
        print(f"Hata: {str(e)}")

# Upload klasörü yapılandırması
UPLOAD_FOLDER = os.path.join('static', 'uploads', 'blog')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB

# Mevcut konfigürasyonların altına ekleyin
app.config['DEBUG'] = True
app.config['PROPAGATE_EXCEPTIONS'] = True

# Sunucu izleme sistemini başlat
from routes.server_monitoring import start_monitoring
start_monitoring()

# Dil değiştirme route'u routes/language.py'de tanımlı

@app.context_processor
def inject_language():
    return {
        'current_language': g.get('language', 'tr'),
        'get_lang_text': get_lang_text
    }

def get_lang_text(obj, field, lang=None):
    """Dile göre içerik getir"""
    if not lang:
        lang = g.get('language', 'tr')
    field_name = f'{field}_{lang}'
    return getattr(obj, field_name, '') or getattr(obj, f'{field}_tr', '')

@app.context_processor
def inject_whatsapp():
    whatsapp = WhatsApp.query.filter_by(active=True).first()
    return dict(whatsapp=whatsapp)

# Flash mesajları için yardımcı fonksiyon
def create_flash_message(message, category='success', title=None):
    default_titles = {
        'success': 'Başarılı!',
        'error': 'Hata!',
        'warning': 'Uyarı!',
        'info': 'Bilgi!'
    }
    
    return {
        'message': message,
        'category': category,
        'title': title or default_titles.get(category, 'Bilgi!')
    }

# Context processor'a flash mesaj yöneticisini ekleyelim
@app.context_processor
def inject_flash_message_manager():
    def format_flash_message(message, category='success'):
        if isinstance(message, dict):
            return message
        return create_flash_message(message, category)
    
    return dict(format_flash_message=format_flash_message)

# AJAX istekleri için flash mesaj handler'ı
@app.route('/get-flash-messages')
def get_flash_messages_route():
    messages = get_flashed_messages(with_categories=True)
    return jsonify(messages)

def delete_file_if_exists(file_path):
    """Eğer dosya varsa siler"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
    except Exception as e:
        print(f"Dosya silme hatası: {str(e)}")
        return False
    return False

# Bu fonksiyon artık routes/rooms.py içinde blueprint ile tanımlanmıştır
# @app.route('/admin/room/delete/<int:id>', methods=['POST'])
# @login_required
# def delete_room(id):
#     # Bu fonksiyon kaldırıldı, yerine rooms.room_delete kullanılıyor
#     pass

# Tekil resim silme endpoint'i ekleyelim
@app.route('/admin/room/<int:id>/remove-image', methods=['POST'])
@login_required
def remove_room_image(id):
    try:
        room = Room.query.get_or_404(id)
        image_name = request.json.get('image')
        
        if not image_name:
            return jsonify({'success': False, 'message': 'Resim adı belirtilmedi'})

        # Room modeli üzerinden resmi sil
        if room.remove_image(image_name):
            db.session.commit()
            return jsonify({
                'success': True, 
                'message': 'Resim başarıyla silindi',
                'image': image_name
            })
        else:
            return jsonify({'success': False, 'message': 'Resim silinirken bir hata oluştu'})

    except Exception as e:
        db.session.rollback()
        print(f"Resim silme hatası: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/admin/room/update/<int:id>', methods=['POST'])
@login_required
def update_room(id):
    try:
        room = Room.query.get_or_404(id)
        
        # Yeni resim yüklendiyse
        if 'image' in request.files and request.files['image'].filename:
            # Eski resimleri kontrol et
            if room.images_list:
                for old_image in room.images_list:
                    old_file_path = os.path.join(app.static_folder, 'uploads', 'rooms', old_image)
                    try:
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                    except Exception as e:
                        print(f"Eski resim silinirken hata: {str(e)}")
            
            # Yeni resmi kaydet
            file = request.files['image']
            filename = secure_filename(file.filename)
            file.save(os.path.join(app.static_folder, 'uploads', 'rooms', filename))
            
            # Yeni resmi gallery_images'e ekle
            if room.gallery_images:
                room.gallery_images = f"{room.gallery_images},{filename}"
            else:
                room.gallery_images = filename
        
        # Diğer güncellemeler...
        db.session.commit()
        flash('Oda başarıyla güncellendi.', 'success')
        return redirect(url_for('admin.rooms'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Güncelleme sırasında bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('admin.rooms'))

@app.after_request
def add_header(response):
    """Tarayıcı önbelleğini devre dışı bırak"""
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    return response

# 404 hata sayfası
@app.route('/errors/404')
def error_404():
    seo_data = {
        'title': '404 - Sayfa Bulunamadı',
        'description': 'Aradığınız sayfa bulunamadı. Lütfen ana sayfaya dönün veya menüyü kullanın.',
        'robots': 'noindex, nofollow'
    }
    
    return render_template(
        'errors/404.html',
        seo=seo_data,
        error="Page not found",
        current_url=request.url,
        referrer=request.referrer
    ), 404

# 500 hata sayfası
@app.errorhandler(500)
def internal_server_error(e):
    app.logger.error(f'500 Error: {str(e)}')
    return render_template('errors/500.html'), 500

# Genel hata yakalayıcı
@app.errorhandler(Exception)
def handle_error(error):
    app.logger.error(f'Unhandled Exception: {str(error)}', exc_info=True)
    if app.debug:
        return {
            'error': str(error),
            'type': error.__class__.__name__
        }, 500
    return render_template('errors/500.html'), 500

@app.template_filter('is_hotel_ip')
def is_hotel_ip_filter(ip):
    """Template'lerde IP kontrolü için filtre"""
    return is_hotel_network(ip)

@app.route('/test')
def test():
    return jsonify({
        'status': 'ok',
        'ip': request.remote_addr,
        'time': get_turkey_time().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/ip-check')
def ip_check():
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    if client_ip and ',' in client_ip:
        client_ip = client_ip.split(',')[0].strip()
    
    is_allowed = is_hotel_network(client_ip)
    
    return jsonify({
        'client_ip': client_ip,
        'is_allowed': is_allowed,
        'server_ip': request.host,
        'headers': dict(request.headers)
    })

# robots.txt route kaldırıldı - routes/seo.py'de tanımlı

def add_url_to_sitemap(root, url, lastmod, priority, changefreq):
    """Sitemap'e URL ekle"""
    url_element = ET.SubElement(root, 'url')
    
    loc = ET.SubElement(url_element, 'loc')
    loc.text = url
    
    lastmod_element = ET.SubElement(url_element, 'lastmod')
    lastmod_element.text = lastmod
    
    changefreq_element = ET.SubElement(url_element, 'changefreq')
    changefreq_element.text = changefreq
    
    priority_element = ET.SubElement(url_element, 'priority')
    priority_element.text = str(priority)
    
    return url_element

# Basit Google indexing status route'u
@app.route('/admin/indexing-status')
@login_required
def indexing_status():
    """Basit indexing durumu kontrolü"""
    if not current_user.is_admin:
        return redirect(url_for('index'))

    try:
        import urllib.request
        import urllib.error

        site_url = request.host_url.rstrip('/')

        # Sitemap kontrolü
        sitemap_status = "❌ Hata"
        try:
            response = urllib.request.urlopen(f"{site_url}/sitemap.xml", timeout=10)
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                url_count = content.count('<loc>')
                sitemap_status = f"✅ OK ({url_count} URL)"
        except:
            sitemap_status = "❌ Erişilemez"

        # Robots.txt kontrolü
        robots_status = "❌ Hata"
        try:
            response = urllib.request.urlopen(f"{site_url}/robots.txt", timeout=10)
            if response.getcode() == 200:
                robots_status = "✅ OK"
        except:
            robots_status = "❌ Erişilemez"

        # Ana sayfalar kontrolü
        pages = ["/", "/main", "/rooms", "/food/menu", "/blog", "/gallery", "/contact"]
        page_statuses = {}

        for page in pages:
            try:
                response = urllib.request.urlopen(f"{site_url}{page}", timeout=10)
                page_statuses[page] = f"✅ HTTP {response.getcode()}"
            except urllib.error.HTTPError as e:
                page_statuses[page] = f"❌ HTTP {e.code}"
            except:
                page_statuses[page] = "❌ Hata"

        # Başarı durumunu kontrol et
        all_success = (sitemap_status.startswith('✅') and
                      robots_status.startswith('✅') and
                      all(status.startswith('✅') for status in page_statuses.values()))

        return render_template('admin/indexing_status.html',
                             sitemap_status=sitemap_status,
                             robots_status=robots_status,
                             page_statuses=page_statuses,
                             site_url=site_url,
                             current_time=get_turkey_time().strftime('%d.%m.%Y %H:%M'),
                             all_success=all_success)

    except Exception as e:
        flash(f'Indexing status kontrolü sırasında hata: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))

# Sitemap route'u kaldırıldı - routes/seo.py'de tanımlanmış olan kullanılacak

@app.route('/test-promotion')
def test_promotion():
    """Promosyon test sayfası"""
    return render_template('test_promotion.html')

@app.route('/test-promotion-new')
def test_promotion_new():
    """Yeni casino çarkı test sayfası"""
    return render_template('test_promotion_new.html')

@app.errorhandler(404)
def page_not_found(e):
    app.logger.warning(f'404 Error: {request.url} - Referrer: {request.referrer} - IP: {request.remote_addr}')
    return redirect(url_for('error_404'))

@app.route('/sitemap.xml', methods=['GET'])
def sitemap():
    # Bu fonksiyon yerine routes/seo.py içindeki sitemap fonksiyonu kullanılacak
    # Yönlendirme yapalım
    from routes.seo import sitemap as seo_sitemap
    return seo_sitemap()

# Admin rotaları
from admin.routes import admin_bp
from routes.admin.settings import admin_settings

app.register_blueprint(admin_bp)
app.register_blueprint(admin_settings, url_prefix='/admin/settings')

@app.route('/admin/visitor-stats-api')
@login_required
def visitor_stats_api():
    try:
        # Bugünün ziyaretçi sayısını al
        today = date.today()
        today_stats = VisitorStats.query.filter_by(date=today).first()
        today_visitors = today_stats.unique_visitors if today_stats else 0
        
        # Bu ayın ziyaretçi sayısını al
        first_day_of_month = today.replace(day=1)
        month_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).filter(
            VisitorStats.date >= first_day_of_month,
            VisitorStats.date <= today
        ).scalar() or 0
        
        # Toplam ziyaretçi sayısını al
        total_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).scalar() or 0
        
        return jsonify({
            'today': int(today_visitors),
            'month': int(month_visitors),
            'total': int(total_visitors)
        })
    except Exception as e:
        app.logger.error(f"Ziyaretçi istatistikleri alınırken hata: {str(e)}")
        return jsonify({
            'today': 0,
            'month': 0,
            'total': 0
        }), 500

@app.route('/api/visitor-stats')
def public_visitor_stats_api():
    try:
        # Referrer kontrolü yap - sadece admin panelinden gelen isteklere izin ver
        referrer = request.headers.get('Referer', '')
        if not referrer or '/admin/' not in referrer:
            return jsonify({'error': 'Unauthorized'}), 403
            
        # Bugünün ziyaretçi sayısını al
        today = date.today()
        today_stats = VisitorStats.query.filter_by(date=today).first()
        today_visitors = today_stats.unique_visitors if today_stats else 0
        
        # Bu ayın ziyaretçi sayısını al
        first_day_of_month = today.replace(day=1)
        month_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).filter(
            VisitorStats.date >= first_day_of_month,
            VisitorStats.date <= today
        ).scalar() or 0
        
        # Toplam ziyaretçi sayısını al
        total_visitors = db.session.query(func.sum(VisitorStats.unique_visitors)).scalar() or 0
        
        return jsonify({
            'today': int(today_visitors),
            'month': int(month_visitors),
            'total': int(total_visitors)
        })
    except Exception as e:
        app.logger.error(f"Ziyaretçi istatistikleri alınırken hata: {str(e)}")
        return jsonify({
            'today': 0,
            'month': 0,
            'total': 0
        }), 500

if __name__ == '__main__':
    app.debug = True  # Debug modu aktif
    app.config['PROPAGATE_EXCEPTIONS'] = True  # Hata yayılımını aktif et
    app.run(host='0.0.0.0', port=5000, threaded=True)
