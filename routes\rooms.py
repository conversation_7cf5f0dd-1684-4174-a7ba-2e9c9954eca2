from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, abort
from flask_login import login_required
from models.room import db, Room, RoomCategory, RoomFeature
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date, timedelta
from slugify import slugify
from flask import g
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload

# Blueprint'i tek bir yerde tanımla
rooms_bp = Blueprint('rooms', __name__, url_prefix='/rooms')

def save_image(file, folder='rooms'):
    """Dosyayı güvenli bir şekilde kaydeder ve dosya adını döndürür"""
    if file and file.filename:
        # Boş dosya kontrolü
        if file.filename == '':
            return None
            
        # Dosya adını güvenli hale getir
        filename = secure_filename(file.filename)
        base, ext = os.path.splitext(filename)
        
        # <PERSON><PERSON><PERSON> bir dosya adı oluştur
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{base}_{timestamp}{ext}"
        
        # Yükleme dizinini oluştur
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads', folder)
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)
        
        # Dosyayı kaydet
        file_path = os.path.join(uploads_dir, filename)
        try:
            file.save(file_path)
            current_app.logger.info(f"Dosya başarıyla kaydedildi: {filename}")
            return filename
        except Exception as e:
            current_app.logger.error(f"Dosya kaydedilirken hata: {str(e)}")
            return None
            
    return None

# Admin routes
@rooms_bp.route('/admin', methods=['GET'])
@login_required
def room_list():
    rooms = Room.get_rooms_with_relations()
    return render_template('admin/rooms/list.html', rooms=rooms)

@rooms_bp.route('/admin/create', methods=['GET', 'POST'])
@login_required
def room_create():
    if request.method == 'POST':
        try:
            room = Room(
                # Türkçe içerik
                title_tr=request.form.get('title_tr'),
                description_tr=request.form.get('description_tr'),
                
                # İngilizce içerik
                title_en=request.form.get('title_en'),
                description_en=request.form.get('description_en'),
                
                # Diğer alanlar
                capacity=request.form.get('capacity', type=int),
                size=request.form.get('size', type=float),
                price=request.form.get('price', type=float),
                currency=request.form.get('currency', 'EUR'),
                booking_url=request.form.get('booking_url'),
                status=request.form.get('status', 'active'),
                category_id=request.form.get('category_id', type=int),
                view_type=request.form.get('view_type')
            )

            # Özellikleri ekle
            feature_ids = request.form.getlist('features[]')
            if feature_ids:
                features = RoomFeature.query.filter(RoomFeature.id.in_(feature_ids)).all()
                room.features = features

            # Görselleri işle
            saved_images = []
            files = request.files.getlist('images')
            for file in files:
                if file and file.filename:
                    filename = save_image(file)
                    if filename:
                        saved_images.append(filename)
            
            if saved_images:
                # featured_image property'sine direkt atama yapmak yerine gallery_images'i ayarlayalım
                # ilk görsel otomatik olarak featured_image olacak
                room.gallery_images = ','.join(saved_images)

            db.session.add(room)
            db.session.commit()
            
            flash('Oda başarıyla oluşturuldu!', 'success')
            return redirect(url_for('rooms.room_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')

    # GET isteği için
    categories = RoomCategory.query.filter_by(status='active').order_by(
        RoomCategory.sort_order).all()
    features = RoomFeature.query.filter_by(status='active').order_by(
        RoomFeature.order).all()
    
    return render_template('admin/rooms/create.html', 
                         categories=categories,
                         features=features,
                         view_types=Room.VIEW_TYPES,
                         datetime=datetime)

@rooms_bp.route('/admin/rooms/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def room_edit(id):
    room = Room.query.get_or_404(id)
    categories = RoomCategory.query.filter_by(status='active').order_by(RoomCategory.sort_order).all()
    features = RoomFeature.query.order_by(RoomFeature.name).all()
    current_language = g.get('language', 'tr')

    if request.method == 'POST':
        try:
            old_title = room.title_tr
            # Türkçe içerik
            room.title_tr = request.form.get('title_tr')
            room.description_tr = request.form.get('description_tr')
            
            # İngilizce içerik
            room.title_en = request.form.get('title_en')
            room.description_en = request.form.get('description_en')
            
            # Diğer alanlar
            room.capacity = request.form.get('capacity', type=int)
            room.size = request.form.get('size', type=float)
            room.price = request.form.get('price', type=float)
            room.currency = request.form.get('currency', 'EUR')
            room.booking_url = request.form.get('booking_url')
            room.status = request.form.get('status', 'active')
            room.category_id = request.form.get('category_id', type=int)
            room.view_type = request.form.get('view_type')

            # Özellikleri güncelle
            feature_ids = request.form.getlist('features[]')
            features = RoomFeature.query.filter(RoomFeature.id.in_(feature_ids)).all()
            room.features = features

            # Yeni görseller ekle
            if 'images' in request.files:
                files = request.files.getlist('images')
                new_images_added = False
                
                # Mevcut görselleri al
                current_images = []
                if room.gallery_images:
                    current_images = [img for img in room.gallery_images.split(',') if img.strip()]
                
                # Yeni görselleri ekle
                for file in files:
                    if file and file.filename:
                        filename = save_image(file)
                        if filename:
                            current_images.append(filename)
                            new_images_added = True
                            current_app.logger.info(f"Yeni görsel eklendi: {filename}")
                
                # Görselleri kaydet
                if current_images:
                    room.gallery_images = ','.join(current_images)
                    # featured_image property'sine direkt atama yapmak yerine, gallery_images'e göre otomatik belirlenir
                else:
                    room.gallery_images = None

            # SLUG GÜNCELLEME: Başlık değiştiyse veya slug boşsa yeni slug üret
            if not room.slug or old_title != room.title_tr:
                base_slug = slugify(room.title_tr)
                slug = base_slug
                counter = 1
                while Room.query.filter(Room.slug == slug, Room.id != room.id).first() is not None:
                    slug = f"{base_slug}-{counter}"
                    counter += 1
                room.slug = slug

            db.session.commit()
            flash('Oda başarıyla güncellendi!', 'success')
            return redirect(url_for('rooms.room_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'Bir hata oluştu: {str(e)}', 'error')

    return render_template('admin/rooms/edit.html', 
                         room=room,
                         categories=categories,
                         features=features,
                         current_language=current_language,
                         view_types=Room.VIEW_TYPES,
                         datetime=datetime)

@rooms_bp.route('/admin/rooms/delete/<int:id>', methods=['POST'])
@login_required
def room_delete(id):
    room = Room.query.get_or_404(id)
    
    try:
        # Galeri resimlerini sil
        if room.gallery_images:
            for image in room.images_list:
                image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'rooms', image)
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                        current_app.logger.info(f"Resim silindi: {image}")
                except Exception as e:
                    current_app.logger.error(f"Resim silinirken hata: {str(e)}")
        
        # İlişkili kayıtları temizle
        room.features = []  # Many-to-many ilişkisini temizle
        
        # Veritabanından odayı sil
        db.session.delete(room)
        db.session.commit()
        flash('Oda ve ilgili tüm resimler başarıyla silindi!', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Oda silme hatası: {str(e)}")
        flash(f'Bir hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('rooms.room_list'))

@rooms_bp.route('/admin/rooms/preview/<int:id>')
@login_required
def room_preview(id):
    try:
        room = Room.query.get_or_404(id)
        return render_template('rooms/preview.html', room=room)
    except Exception as e:
        flash(f'Önizleme yüklenirken bir hata oluştu: {str(e)}', 'error')
        return redirect(url_for('rooms.room_edit', id=id))

@rooms_bp.route('/admin/rooms/remove-image/<int:id>', methods=['POST'])
@login_required
def remove_image(id):
    try:
        room = Room.query.get_or_404(id)
        data = request.get_json()
        image_name = data.get('image')
        
        if image_name:
            # Görseli dosya sisteminden sil
            image_path = os.path.join(current_app.root_path, 'static', 'uploads', 'rooms', image_name)
            if os.path.exists(image_path):
                os.remove(image_path)
            
            # Görseli odanın görsel listesinden kaldır
            if image_name in room.gallery_images.split(','):
                room.gallery_images = ','.join([i for i in room.gallery_images.split(',') if i != image_name])
                db.session.commit()
            
            return jsonify({'success': True, 'message': 'Görsel başarıyla silindi'})
        
        return jsonify({'success': False, 'message': 'Görsel adı belirtilmedi'}), 400
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 400

# Frontend routes
@rooms_bp.route('')
def room_index():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 12
        
        # Aktif odaları kategoriye göre sıralanmış şekilde getir
        query = Room.query.join(RoomCategory).filter(Room.status == 'active').order_by(RoomCategory.sort_order.asc(), Room.id.asc())
        rooms = query.paginate(page=page, per_page=per_page, error_out=False)


        
        current_language = g.get('language', 'tr')
        
        # Kategorileri al
        categories = RoomCategory.query.filter_by(status='active').order_by(RoomCategory.sort_order).all()
        
        print(f"Odalar: {[{'id': r.id, 'title': r.title_tr, 'slug': r.slug} for r in rooms.items]}")
        
        return render_template('rooms/index.html', 
                            rooms=rooms,
                            categories=categories,
                            current_language=current_language)
    except Exception as e:
        print(f"Oda listesi hatası: {str(e)}")
        flash(f"Odalar yüklenirken bir hata oluştu: {str(e)}", "error")
        return render_template('rooms/index.html', 
                            rooms={"items": [], "pages": 0, "page": 1},
                            categories=[],
                            current_language=g.get('language', 'tr'))

rooms_bp.route('/<path:invalid_path>')
def invalid_room_path(invalid_path):
    # Sadece geçersiz slug'ları yakalasın, diğer route'ları etkilemesin
    # Bu route'u en sona koyun
    if '.' in invalid_path or invalid_path.startswith('admin'):
        abort(404)
    # Diğer durumlarda normal slug kontrolü yap
    abort(404)

@rooms_bp.route('/<slug>')
def room_detail(slug):
    room = Room.query.filter_by(slug=slug, status='active').first_or_404()
    return render_template('rooms/detail.html', room=room)

@rooms_bp.route('/category/<slug>')
def rooms_by_category(slug):
    category = RoomCategory.query.filter_by(slug=slug, status='active').first()
    if not category:
        abort(404)
        
    # Sayfalama için sayfa numarasını al
    page = request.args.get('page', 1, type=int)
    per_page = 12
    
    # Bu kategorideki aktif odaları getir
    rooms = Room.query.filter_by(
        category_id=category.id,
        status='active'
    ).order_by(Room.id).paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('rooms/category.html',
                        category=category,
                        rooms=rooms,
                        current_language=g.get('language', 'tr'))

@rooms_bp.route('/admin/rooms/upload-image', methods=['POST'])
@login_required
def upload_image():
    try:
        file = request.files.get('file')
        if file:
            filename = secure_filename(file.filename)
            # Benzersiz dosya adı oluştur
            base, ext = os.path.splitext(filename)
            filename = f"{base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
            
            # Dosyayı kaydet
            file_path = os.path.join('static', 'uploads', 'rooms', filename)
            file.save(file_path)
            
            # URL'i döndür
            return jsonify({
                'location': url_for('static', filename=f'uploads/rooms/{filename}')
            })
            
    except Exception as e:
        return jsonify({'error': str(e)}), 400
    
    return jsonify({'error': 'Dosya yüklenemedi'}), 400

@rooms_bp.route('/admin/room/<int:id>/remove-image', methods=['POST'])
@login_required
def remove_room_image(id):
    try:
        room = Room.query.get_or_404(id)
        image_name = request.json.get('image')
        
        if not image_name:
            return jsonify({'success': False, 'message': 'Resim adı belirtilmedi'})

        if room.remove_image(image_name):
            db.session.commit()
            return jsonify({
                'success': True, 
                'message': 'Resim başarıyla silindi',
                'image': image_name
            })
        else:
            return jsonify({'success': False, 'message': 'Resim silinirken bir hata oluştu'})

    except Exception as e:
        db.session.rollback()
        print(f"Resim silme hatası: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

@rooms_bp.route('/<int:id>')
def room_detail_id(id):
    room = Room.query.get_or_404(id)
    return redirect(url_for('rooms.room_detail', slug=room.slug), code=301) 

@rooms_bp.route('/api/available-rooms', methods=['GET'])
def api_available_rooms():
    """Belirli tarih aralığında müsait odaları döndüren API endpoint'i"""
    try:
        # Tarih parametrelerini al
        check_in_str = request.args.get('check_in')
        check_out_str = request.args.get('check_out')
        guests = request.args.get('guests', type=int)
        category_id = request.args.get('category_id', type=int)
        
        # Parametreleri kontrol et
        if not check_in_str or not check_out_str:
            return jsonify({
                'success': False,
                'message': 'Giriş ve çıkış tarihleri gereklidir'
            }), 400
        
        # Tarihleri parse et
        try:
            check_in = datetime.strptime(check_in_str, '%Y-%m-%d').date()
            check_out = datetime.strptime(check_out_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({
                'success': False,
                'message': 'Geçersiz tarih formatı. YYYY-AA-GG formatında olmalıdır'
            }), 400
        
        # Tarih kontrolü
        if check_in >= check_out:
            return jsonify({
                'success': False,
                'message': 'Çıkış tarihi giriş tarihinden sonra olmalıdır'
            }), 400
            
        # Bugünden önceki tarihleri reddet
        if check_in < date.today():
            return jsonify({
                'success': False,
                'message': 'Geçmiş tarihler için rezervasyon yapılamaz'
            }), 400
        
        # Sorgu oluştur
        query = Room.query.filter_by(status='active')
        
        # Kategori filtresi
        if category_id:
            query = query.filter_by(category_id=category_id)
        
        # Misafir sayısı filtresi
        if guests:
            query = query.filter(Room.capacity >= guests)
        
        # Tüm odaları al
        all_rooms = query.all()
        
        # Müsait odaları filtrele
        available_rooms = []
        for room in all_rooms:
            if room.is_available(check_in, check_out):
                # Oda bilgilerini hazırla
                room_data = {
                    'id': room.id,
                    'name': room.title_tr,
                    'slug': room.slug,
                    'description': room.description_tr,
                    'price': room.price,
                    'currency': room.currency,
                    'max_guests': room.capacity,
                    'image_url': room.featured_image,
                    'category': room.category.name_tr if room.category else 'Kategorisiz',
                    'features': [{'name': f.name, 'icon': f.icon} for f in room.features]
                }
                available_rooms.append(room_data)
        
        return jsonify({
            'success': True,
            'check_in': check_in_str,
            'check_out': check_out_str,
            'rooms': available_rooms,
            'total': len(available_rooms)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Bir hata oluştu: {str(e)}'
        }), 500 

@rooms_bp.route('/search', methods=['GET'])
def search_rooms():
    """Müsait odaları aramak için form sayfası"""
    # Oda kategorilerini al
    categories = RoomCategory.query.filter_by(status='active').order_by(RoomCategory.order).all()
    
    # Bugünün ve yarının tarihlerini al (varsayılan değerler için)
    today = date.today().strftime('%Y-%m-%d')
    tomorrow = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
    
    # Form parametrelerini al (varsa)
    check_in = request.args.get('check_in', today)
    check_out = request.args.get('check_out', tomorrow)
    guests = request.args.get('guests', 1, type=int)
    category_id = request.args.get('category_id', type=int)
    
    # Arama yapıldı mı?
    search_performed = 'check_in' in request.args and 'check_out' in request.args
    
    # Eğer arama yapıldıysa, API'den sonuçları al
    available_rooms = []
    if search_performed:
        try:
            # API'yi çağır
            api_url = url_for('rooms.api_available_rooms', 
                             check_in=check_in, 
                             check_out=check_out,
                             guests=guests,
                             category_id=category_id)
            
            # API yanıtını al (doğrudan fonksiyonu çağırarak)
            from flask import make_response
            api_response = api_available_rooms()
            
            # Eğer API yanıtı başarılıysa, odaları al
            if isinstance(api_response, tuple):
                response_data = api_response[0].get_json()
            else:
                response_data = api_response.get_json()
            
            if response_data.get('success'):
                available_rooms = response_data.get('rooms', [])
            else:
                flash(f'Odalar aranırken bir hata oluştu: {response_data.get("message")}', 'error')
                
        except Exception as e:
            flash(f'Odalar aranırken bir hata oluştu: {str(e)}', 'error')
    
    return render_template('rooms/search.html',
                         categories=categories,
                         check_in=check_in,
                         check_out=check_out,
                         guests=guests,
                         category_id=category_id,
                         search_performed=search_performed,
                         available_rooms=available_rooms) 