# Nginx config - Fixed version
http {
    # Basic settings
    charset utf-8;
    charset_types text/css text/plain text/javascript application/javascript application/json;

    # Gzip compression (moved to http level)
    gzip on;
    gzip_vary on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # Security headers
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-XSS-Protection "1; mode=block" always;

    server {
        listen 80;
        server_name zeppelincappadocia.com www.zeppelincappadocia.com;

        # Static files (separate location block)
        location /static/ {
            alias /home/<USER>/v1/static/;
            expires 1y;
            add_header Cache-Control "public, no-transform, immutable";
        }

        # Main application - Apache + Passenger backend
        location / {
            proxy_pass http://127.0.0.1:80;  # Apache'nin ç<PERSON>ığı port
            proxy_buffering on;
            proxy_buffers 12 12k;
            proxy_redirect off;

            # Essential proxy headers for Apache
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Server $host;
        }
    }
}