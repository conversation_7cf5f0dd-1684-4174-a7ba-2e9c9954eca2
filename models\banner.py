from models import db
from datetime import datetime

class Banner(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200))
    subtitle = db.Column(db.String(200))
    description = db.Column(db.Text)
    image = db.Column(db.String(200))
    video = db.Column(db.String(200))
    video_url = db.Column(db.String(500))
    file_type = db.Column(db.String(20), default='image')  # 'image' or 'video'
    button_text = db.Column(db.String(50))
    button_link = db.Column(db.String(200))
    category = db.Column(db.String(50), default='main')
    order = db.Column(db.Integer, default=0)
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) 

    def __repr__(self):
        return f'<Banner {self.title}>' 