{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> | {{ settings.site_title }}{% endblock %}

{% block content %}
<div class="flex flex-col min-h-screen">
    <!-- <PERSON> -->
    <main class="flex-grow">
        <!-- Hero Slide<PERSON>ü<PERSON>ü -->
        <section class="relative w-full h-[calc(100vw*9/16)] max-h-[1080px] min-h-[480px] overflow-hidden">
            {% if foods %}
                {% for food in foods[:5] %}
                <div class="hero-slider-item absolute inset-0 {% if loop.first %}opacity-100{% else %}opacity-0{% endif %} transition-opacity duration-1000">
                    {% if food.image %}
                        <img src="{{ url_for('static', filename=food.image.replace('\\', '/')) }}" 
                             alt="{{ food.name }}"
                             class="w-full h-full object-cover">
                    {% endif %}
                    
                    <!-- Slide<PERSON> -->
                    <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                        <div class="container mx-auto px-4">
                            <div class="max-w-2xl text-white {% if loop.index is odd %}ml-0{% else %}ml-auto text-right{% endif %}">
                                <span class="text-gold font-great-vibes text-5xl md:text-6xl" data-translate="culinary_journey">Lezzet Yolculuğu</span>
                                <h2 class="text-4xl md:text-6xl font-light mt-6 leading-tight">{{ food.name }}</h2>
                                <p class="mt-4 text-lg text-white/80">{{ food.description|truncate(100) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Slider Kontrolleri -->
                <div class="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
                    {% for food in foods[:5] %}
                    <button class="hero-slider-dot w-3 h-3 rounded-full bg-white/30 hover:bg-white/50 transition-all duration-300 {% if loop.first %}!bg-white{% endif %}"
                            data-index="{{ loop.index0 }}"></button>
                    {% endfor %}
                </div>

                <!-- Slider Ok Kontrolleri -->
                <button class="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black/20 hover:bg-black/40 text-white flex items-center justify-center transition-all group focus:outline-none" id="prevSlide">
                    <i class="fas fa-chevron-left group-hover:scale-110 transition-transform"></i>
                </button>
                <button class="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black/20 hover:bg-black/40 text-white flex items-center justify-center transition-all group focus:outline-none" id="nextSlide">
                    <i class="fas fa-chevron-right group-hover:scale-110 transition-transform"></i>
                </button>
            {% endif %}
        </section>

        <!-- Yemek Menüsü Bölümü -->
        <section class="bg-[#F5F5F5] py-16 rounded-3xl mx-4 my-8">
            <div class="container mx-auto px-4">
                <!-- Başlık Alanı -->
                <div class="text-center mb-10">
                    <span class="text-gold font-great-vibes text-5xl" data-translate="culinary_journey">Lezzet Yolculuğu</span>
                    <div class="flex items-center justify-center mt-6">
                        <div class="h-[2px] w-12 bg-gold"></div>
                        <div class="mx-4 text-gold text-2xl">⚜</div>
                        <div class="h-[2px] w-12 bg-gold"></div>
                    </div>
                    <p class="mt-4 text-gray-600 max-w-xl mx-auto" data-translate="menu_description">
                        Şeflerimizin özenle hazırladığı eşsiz lezzetleri keşfedin
                    </p>
                </div>

                <!-- Kategori Filtreleme -->
                

                <!-- Yemek Listesi - Modern Card Tasarımı -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    {% for food in foods %}
                    {% if food.active %}
                    <div class="food-item group" data-category="{{ food.category.name if food.category else 'all' }}">
                        <div class="bg-white rounded-3xl overflow-hidden group hover:shadow-2xl hover:shadow-gold/30 transition-all duration-700 transform hover:-translate-y-2">
                            <!-- Yemek Görseli -->
                            <div class="relative overflow-hidden rounded-t-3xl">
                                {% if food.image %}
                                <img src="{{ url_for('static', filename=food.image.replace('\\', '/')) }}"
                                     alt="{{ food.name }}"
                                     class="w-full h-[240px] object-cover transform group-hover:scale-105 transition-transform duration-1000">
                                {% else %}
                                <div class="w-full h-[240px] bg-gradient-to-br from-gold/20 to-gold/10 flex items-center justify-center">
                                    <i class="fas fa-utensils text-gold text-3xl"></i>
                                </div>
                                {% endif %}

                                <!-- Hover Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                                    <div class="absolute bottom-4 left-4 right-4">
                                        <div class="flex items-center justify-center space-x-4 text-white/80">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-clock text-gold"></i>
                                                <span class="text-sm">15-20 dk</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-star text-gold"></i>
                                                <span class="text-sm">Özel</span>
                                            </div>
                                            {% if food.category %}
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-tag text-gold"></i>
                                                <span class="text-sm">{{ food.category.name }}</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Yemek Bilgileri -->
                            <div class="p-5 bg-white">
                                <div class="text-center">
                                    <h3 class="text-2xl font-great-vibes text-gray-800 mb-2">{{ food.name }}</h3>

                                    {% if food.description %}
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                        {{ food.description|truncate(80) }}
                                    </p>
                                    {% endif %}

                                    <!-- Sipariş Butonu -->
                                    <button onclick="addToCart('{{ food.id }}')"
                                            class="w-full inline-flex items-center justify-center bg-gold/10 hover:bg-gold hover:text-white px-4 py-2.5 rounded-full text-gold transition-all duration-300 transform hover:scale-105 border border-gold/20 hover:border-gold group/btn {% if not session.get('is_hotel_network') %}opacity-50 cursor-not-allowed{% endif %}"
                                            {% if not session.get('is_hotel_network') %}disabled{% endif %}>
                                        {% if session.get('is_hotel_network') %}
                                            <span class="text-sm font-medium tracking-wide" data-translate="add_to_cart">SEPETE EKLE</span>
                                        {% else %}
                                            <span class="text-sm font-medium tracking-wide" data-translate="hotel_guests_only">SADECE OTEL MİSAFİRLERİ</span>
                                        {% endif %}
                                        <i class="fas fa-shopping-cart ml-2 transform group-hover/btn:scale-110 transition-transform duration-300"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </section>
    </main>

    <!-- Sabit Sepet İkonu -->
    <div class="fixed bottom-8 right-8 z-50">
        {% if session.get('is_hotel_network') %}
            <a href="{{ url_for('food.cart') }}" 
               class="flex items-center justify-center w-14 h-14 bg-gold text-white rounded-full shadow-lg hover:bg-gold/90 transition-all group">
                <i class="fas fa-shopping-cart text-xl group-hover:scale-110 transition-transform"></i>
                {% if session.get('cart') %}
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-medium">
                        {{ session.get('cart')|length }}
                    </span>
                {% endif %}
            </a>
        {% endif %}
    </div>

    <!-- Flash Mesajları -->
    <div class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2 max-w-md">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="bg-{{ 'red' if category == 'error' else 'green' }}-500/90 text-white px-6 py-3 rounded-lg shadow-lg animate-fade-in-down w-full">
                        <div class="flex items-center">
                            <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' }} mr-3"></i>
                            <span>{{ message }}</span>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
</div>

<style>
/* Modern Food Card Styles */
.food-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center bottom;
}

.food-item:hover {
    transform: translateY(-8px) scale(1.02);
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive Grid - 4'lü Layout */
@media (min-width: 1024px) {
    .grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 640px) and (max-width: 1023px) {
    .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 639px) {
    .grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Kart boyutları optimize */
.food-item .bg-white {
    min-height: auto;
}

.food-item img, .food-item .w-full.h-\[280px\] {
    height: 240px;
}

/* Button Hover Effects */
.group\/btn:hover .fas {
    transform: scale(1.1);
}

/* Card Shadow Effects */
.food-item .bg-white {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.food-item:hover .bg-white {
    box-shadow: 0 25px 50px -12px rgba(198, 168, 125, 0.25);
}

@keyframes fade-in-down {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-down {
    animation: fade-in-down 0.3s ease-out;
}
</style>

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Slider Fonksiyonu
    function initHeroSlider() {
        const slides = document.querySelectorAll('.hero-slider-item');
        const dots = document.querySelectorAll('.hero-slider-dot');
        const prevButton = document.getElementById('prevSlide');
        const nextButton = document.getElementById('nextSlide');
        
        if (slides.length <= 1) return;

        let currentSlide = 0;
        let slideInterval;
        
        function showSlide(index) {
            slides.forEach(slide => slide.style.opacity = '0');
            dots.forEach(dot => dot.classList.remove('!bg-white'));
            
            slides[index].style.opacity = '1';
            dots[index].classList.add('!bg-white');
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        function startSlideShow() {
            if (slideInterval) {
                clearInterval(slideInterval);
            }
            slideInterval = setInterval(nextSlide, 5000);
        }

        // Ok kontrolleri için event listener'lar
        prevButton.addEventListener('click', () => {
            prevSlide();
            startSlideShow();
        });

        nextButton.addEventListener('click', () => {
            nextSlide();
            startSlideShow();
        });

        // Dot kontrolleri için event listener'lar
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
                startSlideShow();
            });
        });

        // Otomatik geçişi başlat
        startSlideShow();

        // Mouse hover durumunda otomatik geçişi durdur
        slides.forEach(slide => {
            slide.addEventListener('mouseenter', () => clearInterval(slideInterval));
            slide.addEventListener('mouseleave', startSlideShow);
        });
    }

    // Kategori Filtreleme
    const categoryButtons = document.querySelectorAll('.category-btn');
    const foodItems = document.querySelectorAll('.food-item');

    categoryButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Aktif sınıfı kaldır
            categoryButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-gold', 'text-white');
                btn.classList.add('border-gold/20', 'text-gray-700');
            });

            // Seçilen butona aktif sınıfı ekle
            button.classList.remove('border-gold/20', 'text-gray-700');
            button.classList.add('active', 'bg-gold', 'text-white');

            const category = button.dataset.category;

            // Yemekleri filtrele
            foodItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                    setTimeout(() => item.style.opacity = '1', 50);
                } else {
                    item.style.opacity = '0';
                    setTimeout(() => item.style.display = 'none', 300);
                }
            });
        });
    });

    // Slider'ı başlat
    initHeroSlider();
});

// Sepete ekleme fonksiyonu
function addToCart(foodId) {
    // Hotel network durumunu session'dan kontrol et
    var isHotelNetwork = {{ session.get('is_hotel_network', false)|tojson }};
    
    if (!isHotelNetwork) {
        alert('Bu işlem sadece otel misafirleri için kullanılabilir.');
        return false;
    }

    fetch('{{ url_for("food.add_to_cart") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `food_id=${foodId}&quantity=1`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'Bir hata oluştu.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Bir hata oluştu.');
    });
    
    return false; // Event'i durdur
}

// Sayfa yüklendiğinde kontrolleri yap
document.addEventListener('DOMContentLoaded', function() {
    var isHotelNetwork = {{ session.get('is_hotel_network', false)|tojson }};
    
    // Tüm sepete ekle butonlarını devre dışı bırak
    if (!isHotelNetwork) {
        document.querySelectorAll('button[onclick^="addToCart"]').forEach(button => {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
        });
    }
});
</script>
{% endblock %}
{% endblock %} 