{% extends 'admin/base.html' %}

{% block breadcrumb %}<PERSON><PERSON>{% endblock %}
{% block page_title %}<PERSON><PERSON>{% endblock %}
{% block page_subtitle %}<PERSON><PERSON> öğesinin bilgilerini güncelleyin{% endblock %}

{% block admin_content %}
<div class="p-6">
    <form method="POST" enctype="multipart/form-data" class="space-y-6">
        <div class="grid grid-cols-1 gap-6">
            <!-- Başlık -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Başlık</label>
                <input type="text" name="title" value="{{ gallery.title }}" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>

            <!-- Açıklama -->
            <div>
                <label class="block text-sm font-medium text-gray-700">A<PERSON>ıklama</label>
                <textarea name="description" rows="3"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ gallery.description }}</textarea>
            </div>

            <!-- Kategori -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Kategori</label>
                <select name="category" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="hotel" {% if gallery.category == 'hotel' %}selected{% endif %}>Otel</option>
                    <option value="restaurant" {% if gallery.category == 'restaurant' %}selected{% endif %}>Restoran</option>
                    <option value="spa" {% if gallery.category == 'spa' %}selected{% endif %}>SPA</option>
                    <option value="room" {% if gallery.category == 'room' %}selected{% endif %}>Odalar</option>
                    <option value="other" {% if gallery.category == 'other' %}selected{% endif %}>Diğer</option>
                </select>
            </div>

            <!-- Sıralama Numarası -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Sıralama Numarası</label>
                <input type="number" name="order" value="{{ gallery.order }}" required
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <p class="mt-2 text-sm text-gray-500">Küçük numaralar önce görünür. Örnek: 1, 2, 3...</p>
            </div>

            <!-- Mevcut Resim -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Mevcut Resim</label>
                {% if gallery.image %}
                <div class="mt-2">
                    <img src="{{ url_for('static', filename='uploads/gallery/' + gallery.image) }}"
                         alt="{{ gallery.title }}"
                         class="h-32 w-auto object-cover rounded-lg"
                         onerror="this.src='data:image/svg+xml;charset=UTF-8,<svg width=\'200\' height=\'200\' xmlns=\'http://www.w3.org/2000/svg\'><rect width=\'200\' height=\'200\' fill=\'%23f3f4f6\'/><text x=\'50%\' y=\'50%\' font-size=\'16\' text-anchor=\'middle\' fill=\'%236b7280\'>Resim Bulunamadı</text></svg>'">
                </div>
                {% endif %}
            </div>

            <!-- Yeni Resim -->
            <div>
                <label class="block text-sm font-medium text-gray-700">Yeni Resim</label>
                <div class="mt-1 flex items-center">
                    <input type="file" name="image" accept="image/*"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                </div>
                <p class="mt-2 text-sm text-gray-500">Yeni bir resim seçmezseniz mevcut resim korunacaktır.</p>
            </div>

            <!-- Durum -->
            <div>
                <label class="flex items-center">
                    <input type="checkbox" name="active" {% if gallery.active %}checked{% endif %}
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <span class="ml-2 text-sm text-gray-700">Aktif</span>
                </label>
            </div>
        </div>

        <!-- Butonlar -->
        <div class="flex justify-end space-x-3">
            <a href="{{ url_for('gallery.admin_gallery_list') }}"
               class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                İptal
            </a>
            <button type="submit"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                Güncelle
            </button>
        </div>
    </form>
</div>
{% endblock %} 