from datetime import datetime, date
from models.setting import db
from slugify import slugify
import os
from flask import current_app
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, func

# Circular import'u ön<PERSON>ek için
from models.room_category import RoomCategory
from models.room_feature import RoomFeature
from flask import url_for

# Ara tablo tanımı - en üste taşıyoruz
room_features = db.Table('room_features',
    db.Column('room_id', db.Integer, db.Foreign<PERSON>ey('room.id')),
    db.Column('feature_id', db.Integer, db.Foreign<PERSON>ey('room_feature.id'))
)

class Room(db.Model):
    __tablename__ = 'room'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Türkçe içerik
    title_tr = db.Column(db.String(200), nullable=False)
    description_tr = db.Column(db.Text)
    
    # İngilizce içerik
    title_en = db.Column(db.String(200), nullable=False)
    description_en = db.Column(db.Text)
    
    # Ortak alanlar
    slug = db.Column(db.String(200), unique=True)
    category_id = db.Column(db.Integer, db.ForeignKey('room_category.id'))
    capacity = db.Column(db.Integer)
    size = db.Column(db.Float)
    price = db.Column(db.Float)
    currency = db.Column(db.String(3), default='EUR')  # Para birimi (varsayılan EURO)
    gallery_images = db.Column(db.Text)  # Virgülle ayrılmış resim dosya adları
    video_url = db.Column(db.String(500))  # Video URL'i için
    video_thumbnail = db.Column(db.String(200))  # Video thumbnail'i için
    video_duration = db.Column(db.Integer)  # Video süresi (saniye)
    view_type = db.Column(db.String(50))
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    booking_url = db.Column(db.String(255))

    # İlişkiler
    category = db.relationship('RoomCategory', backref='rooms')
    features = db.relationship('RoomFeature', secondary=room_features, backref='rooms')
    
    # Varsayılan oda görseli
    DEFAULT_IMAGE = '/static/images/standart-rooms.png'

    VIEW_TYPES = {
        'city': 'Şehir Manzarası',
        'sea': 'Deniz Manzarası',
        'garden': 'Bahçe Manzarası',
        'mountain': 'Dağ Manzarası',
        'pool': 'Havuz Manzarası',
        'forest': 'Orman Manzarası',
        'lake': 'Göl Manzarası'
    }

    def get_view_type_display(self):
        return self.VIEW_TYPES.get(self.view_type, self.view_type)

    def get_similar_rooms(self, limit=3):
        """Benzer odaları getir"""
        return Room.query.filter(
            Room.id != self.id,  # Kendisi hariç
            Room.status == 'active',  # Sadece aktif odalar
            Room.category_id == self.category_id  # Aynı kategorideki odalar
        ).order_by(
            Room.created_at.desc()
        ).limit(limit).all()

    def __init__(self, *args, **kwargs):
        super(Room, self).__init__(*args, **kwargs)
        if self.title_tr:
            self.generate_slug()

    def generate_slug(self):
        """Başlıktan slug oluştur"""
        if not self.slug:
            base_slug = slugify(self.title_tr)
            slug = base_slug
            counter = 1
            while Room.query.filter_by(slug=slug).first() is not None:
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug

    def __repr__(self):
        return f'<Room {self.title_tr}>'

    def get_title(self, lang='tr'):
        """Dile göre başlık getir"""
        return getattr(self, f'title_{lang}')

    def get_description(self, lang='tr'):
        """Dile göre açıklama getir"""
        return getattr(self, f'description_{lang}')

    def remove_image(self, image_name):
        """Belirtilen resmi galeri listesinden kaldır"""
        if not self.gallery_images:
            return False
        
        images = self.images_list
        if image_name in images:
            images.remove(image_name)
            self.gallery_images = ','.join(images)
            return True
        return False

    @property
    def images_list(self):
        """Galeri resimlerini liste olarak döndür"""
        if self.gallery_images:
            return [img.strip() for img in self.gallery_images.split(',') if img.strip()]
        return []

    @property
    def all_images(self):
        """Tüm resimleri liste olarak döndür"""
        return self.images_list
        
    @property
    def featured_image(self):
        """İlk galeri resmini veya varsayılan resmi döndür"""
        images = self.images_list
        if images and len(images) > 0:
            # İlk resmin tam URL'ini döndür
            return url_for('static', filename=f'uploads/rooms/{images[0]}')
        return self.DEFAULT_IMAGE
        
    def get_formatted_price(self, currency='EUR'):
        """Para birimi ile birlikte formatlanmış fiyatı döndürür"""
        if not self.price:
            return "Lütfen sorunuz"
        
        # Orijinal para birimi ile istenen para birimi aynıysa doğrudan formatla
        if self.currency == currency:
            if currency == 'TRY':
                return f"{self.price:.2f} ₺"
            elif currency == 'USD':
                return f"${self.price:.2f}"
            elif currency == 'EUR':
                return f"€{self.price:.2f}"
            else:
                return f"{self.price:.2f} {currency}"
        
        # Para birimi dönüşümü gerekiyorsa
        try:
            from services.currency_service import CurrencyService
            converted = CurrencyService.convert_currency(self.price, self.currency, currency)
            
            if converted is not None:
                if currency == 'TRY':
                    return f"{converted:.2f} ₺"
                elif currency == 'USD':
                    return f"${converted:.2f}"
                elif currency == 'EUR':
                    return f"€{converted:.2f}"
                else:
                    return f"{converted:.2f} {currency}"
        except:
            pass
        
        # Dönüşüm başarısız olursa orijinal para birimini döndür
        if self.currency == 'TRY':
            return f"{self.price:.2f} ₺"
        elif self.currency == 'USD':
            return f"${self.price:.2f}"
        elif self.currency == 'EUR':
            return f"€{self.price:.2f}"
        else:
            return f"{self.price:.2f} {self.currency}"
            
    def is_available(self, check_in, check_out):
        """Belirtilen tarih aralığında oda müsait mi kontrol eder"""
        from models.reservation import Reservation
        
        # Oda aktif değilse müsait değildir
        if self.status != 'active':
            print(f"Oda {self.id} aktif değil, müsait değil")
            return False
        
        # Tarih kontrolü
        if check_in >= check_out:
            print(f"Geçersiz tarih aralığı: {check_in} - {check_out}")
            return False
        
        # Rezervasyon çakışması kontrolü
        reservation_count = Reservation.query.filter(
            Reservation.room_id == self.id,
            Reservation.status.in_(['pending', 'confirmed']),
            or_(
                # Giriş tarihi varolan rezervasyonun içinde
                and_(
                    check_in >= Reservation.check_in,
                    check_in < Reservation.check_out
                ),
                # Çıkış tarihi varolan rezervasyonun içinde
                and_(
                    check_out > Reservation.check_in,
                    check_out <= Reservation.check_out
                ),
                # Yeni rezervasyon varolan rezervasyonu kapsıyor
                and_(
                    check_in <= Reservation.check_in,
                    check_out >= Reservation.check_out
                )
            )
        ).count()
        
        if reservation_count > 0:
            print(f"Oda {self.id} için {check_in} - {check_out} tarihleri arasında {reservation_count} çakışan rezervasyon var")
            return False
        
        print(f"Oda {self.id} için {check_in} - {check_out} tarihleri müsait")
        return True
        
    def get_unavailable_dates(self, start_date=None, end_date=None):
        """Müsait olmayan tarihlerin listesini döndürür"""
        from models.reservation import Reservation
        from datetime import timedelta
        
        if not start_date:
            start_date = date.today()
        if not end_date:
            end_date = date.today() + timedelta(days=365)  # Bir yıl sonra
        
        # Mevcut rezervasyonları sorgula
        reservations = Reservation.query.filter(
            Reservation.room_id == self.id,
            Reservation.status.in_(['pending', 'confirmed']),
            Reservation.check_in < end_date,
            Reservation.check_out > start_date
        ).all()
        
        # Rezervasyonlardan müsait olmayan tarihleri çıkar
        unavailable_dates = []
        for reservation in reservations:
            current_date = max(start_date, reservation.check_in)
            end = min(end_date, reservation.check_out)
            
            while current_date < end:
                unavailable_dates.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
                
        return unavailable_dates

    @classmethod
    def get_rooms_with_relations(cls):
        """N+1 sorgu problemini çözmek için eager loading kullanır"""
        return cls.query.options(
            joinedload(cls.category),
            joinedload(cls.features)
        ).all()

    @classmethod
    def get_active_rooms(cls, page=1, per_page=12):
        """Aktif odaları kategori ID'sine göre sıralı şekilde getir"""
        try:
            rooms = cls.query.options(
                joinedload(cls.category)
            ).filter_by(status='active').join(
                RoomCategory, cls.category_id == RoomCategory.id
            ).order_by(
                RoomCategory.sort_order.asc(),  # Önce kategori sıralamasına göre
                cls.category_id.asc(),          # Sonra kategori ID'sine göre
                cls.created_at.desc()           # Son olarak oluşturulma tarihine göre
            ).paginate(page=page, per_page=per_page)
            print(f"Aktif odalar: {len(rooms.items)} adet")
            print(f"Odalar: {[{'id': r.id, 'title': r.title_tr, 'category_id': r.category_id, 'category': r.category.name_tr if r.category else 'Kategori Yok'} for r in rooms.items]}")
            return rooms
        except Exception as e:
            print(f"Aktif odaları getirme hatası: {str(e)}")
            # Boş bir sayfa döndür
            return cls.query.filter(cls.id < 0).paginate(page=1, per_page=1)