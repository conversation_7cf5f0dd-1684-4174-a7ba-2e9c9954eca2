import os
from datetime import datetime, timezone, timedelta
import re
import uuid
import ipaddress
import geoip2.database
import geoip2.errors

# Türkiye saat dilimi (UTC+3)
TURKEY_TZ = timezone(timedelta(hours=3))
 
def get_turkey_time():
    """Türkiye saatini döndür"""
    return datetime.now(TURKEY_TZ)

def get_country_from_ip(ip_address):
    """IP adresinden ülke bilgisini çıkar"""
    try:
        # GeoLite2 veritabanı dosyasının yolu
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'GeoLite2-Country.mmdb')
        
        # Eğer veritabanı yoksa indirme talimatı ver
        if not os.path.exists(db_path):
            # Veritabanı klasörünü oluştur
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            return {
                'country_code': 'XX',
                'country_name': 'Unknown',
                'error': 'GeoLite2 database not found. Please download it from MaxMind.'
            }
        
        # IP adresini temizle
        if ip_address and ',' in ip_address:
            ip_address = ip_address.split(',')[0].strip()
            
        # Özel IP adresleri için kontrol
        if not ip_address or ip_address == '127.0.0.1' or ip_address.startswith('192.168.') or ip_address.startswith('10.'):
            return {
                'country_code': 'TR',  # Yerel IP'ler için varsayılan olarak Türkiye
                'country_name': 'Turkey'
            }
            
        # GeoIP2 veritabanından ülke bilgisini al
        with geoip2.database.Reader(db_path) as reader:
            response = reader.country(ip_address)
            return {
                'country_code': response.country.iso_code or 'XX',
                'country_name': response.country.name or 'Unknown'
            }
    except geoip2.errors.AddressNotFoundError:
        # IP adresi veritabanında bulunamadı
        return {
            'country_code': 'XX',
            'country_name': 'Unknown',
            'error': 'IP address not found in database'
        }
    except Exception as e:
        # Diğer hatalar
        print(f"Error getting country from IP: {str(e)}")
        return {
            'country_code': 'XX',
            'country_name': 'Unknown',
            'error': str(e)
        }

def generate_unique_filename(original_filename):
    """Benzersiz dosya adı oluştur"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename, ext = os.path.splitext(original_filename)
    
    # Dosya adındaki özel karakterleri temizle
    clean_filename = re.sub(r'[^a-zA-Z0-9_-]', '', filename)
    
    # Temiz dosya adı boşsa, rastgele bir isim oluştur
    if not clean_filename:
        clean_filename = uuid.uuid4().hex[:8]
        
    return f"{timestamp}_{clean_filename}{ext}" 