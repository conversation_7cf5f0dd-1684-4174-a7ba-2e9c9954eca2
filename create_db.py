#!/usr/bin/env python3
"""
Veritabanı Tabloları Oluşturma Scripti

Bu script, SQLAlchemy modellerini kullanarak veritabanı tablolarını oluşturur.
"""

from app import app, db
from models.banner import Banner
from models.setting import Setting
from models.slider import Slider
from models.room import Room
from models.room_category import RoomCategory
from models.room_feature import RoomFeature
from models.gallery import Gallery
from models.blog import BlogPost
from models.page import Page
from models.activity import Activity
from models.food import Food, FoodCategory
from models.reservation import Reservation
from models.promotion import Promotion
from models.user import User
from models.language import Language
from models.menu import Menu, SidebarMenu
from models.whatsapp import WhatsApp
from models.ip_settings import IPSettings
from models.keyword_analytics import KeywordAnalytics
from models.sponsor import Sponsor
from models.seo import SEO
from models.under_construction import UnderConstruction
from werkzeug.security import generate_password_hash
import datetime

def create_tables():
    """Tüm tabloları oluşturur"""
    with app.app_context():
        db.create_all()
        print("✅ Veritabanı tabloları başarıyla oluşturuldu!")

def create_admin_user():
    """Varsayılan admin kullanıcısı oluşturur"""
    with app.app_context():
        if User.query.filter_by(email="<EMAIL>").first() is None:
            admin = User(
                username="admin",
                email="<EMAIL>",
                is_admin=True,
                created_at=datetime.datetime.now()
            )
            admin.set_password("admin123")
            db.session.add(admin)
            db.session.commit()
            print("✅ Varsayılan admin kullanıcısı oluşturuldu!")
        else:
            print("⚠️ Admin kullanıcısı zaten var.")

def create_default_settings():
    """Varsayılan site ayarlarını oluşturur"""
    with app.app_context():
        # Site temel ayarları
        Setting.set_setting('site_title', 'Zeppelin Hotel')
        Setting.set_setting('site_description', 'Zeppelin Hotel - Cappadocia')
        Setting.set_setting('site_keywords', 'hotel, cappadocia, accommodation, rooms')
        Setting.set_setting('contact_email', '<EMAIL>')
        Setting.set_setting('contact_phone', '+90 555 123 45 67')
        Setting.set_setting('contact_address', 'Göreme, Cappadocia, Turkey')
        Setting.set_setting('footer_text', '© 2025 Zeppelin Hotel. All rights reserved.')
        Setting.set_setting('reservation_link', 'rooms/search')
        
        # TinyMCE API anahtarı
        Setting.set_setting('tinymce_api_key', 'lxsh57vn6ovownaneyfk0oa5v7o09tppji3wzc2mtl1yv1fx')
        
        print("✅ Varsayılan site ayarları oluşturuldu!")

def create_default_languages():
    """Varsayılan dilleri oluşturur"""
    with app.app_context():
        languages = [
            {'key': 'welcome', 'tr': 'Hoş Geldiniz', 'en': 'Welcome', 'page': 'home'},
            {'key': 'about', 'tr': 'Hakkımızda', 'en': 'About Us', 'page': 'menu'}
        ]
        
        for lang_data in languages:
            if not Language.query.filter_by(key=lang_data['key']).first():
                language = Language(**lang_data)
                db.session.add(language)
        
        db.session.commit()
        print("✅ Varsayılan diller oluşturuldu!")

def main():
    """Ana işlev"""
    print("\n🚀 Veritabanı Kurulumu Başlıyor...")
    
    # Tabloları oluştur
    create_tables()
    
    # Admin kullanıcısı oluştur
    create_admin_user()
    
    # Varsayılan ayarları oluştur
    create_default_settings()
    
    # Varsayılan dilleri oluştur
    create_default_languages()
    
    print("\n✨ Veritabanı kurulumu tamamlandı!")
    print("🔑 Admin kullanıcısı: <EMAIL> / admin123")

if __name__ == "__main__":
    main() 