# -*- coding: utf-8 -*-
import os
import sys
from datetime import datetime  # datetime modül<PERSON>nü ekleyelim

# CloudLinux Passenger için Python yolu
INTERP = "/home/<USER>/virtualenv/domains/zeppelincappadocia.com/public_html/3.10/bin/python"
if sys.executable != INTERP:
    os.execl(INTERP, INTERP, *sys.argv)

# Uygulama dizinini ayarla
cwd = os.getcwd()
sys.path.append(cwd)

# UTF-8 encoding ayarı
import locale
locale.setlocale(locale.LC_ALL, 'tr_TR.UTF-8')
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# SQLite için UTF-8 ayarı
import sqlite3
sqlite3.connect(':memory:').execute('PRAGMA encoding = "UTF-8"')

# Loglama konfigürasyonu
import logging
LOG_DIR = os.path.join(cwd, 'logs')
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

logging.basicConfig(
    filename=os.path.join(LOG_DIR, 'app.log'),
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Flask uygulamasını import et
try:
    from app import app as flask_app  # Flask app'i farklı isimle import et
    logging.info("Flask uygulaması başarıyla import edildi")
except Exception as e:
    logging.error(f"Uygulama import hatası: {str(e)}", exc_info=True)
    raise

def log_request_info(environ):
    """Request bilgilerini logla"""
    return {
        'path': environ.get('PATH_INFO', ''),
        'method': environ.get('REQUEST_METHOD', ''),
        'query': environ.get('QUERY_STRING', ''),
        'remote_addr': environ.get('REMOTE_ADDR', ''),
        'user_agent': environ.get('HTTP_USER_AGENT', '')
    }

# WSGI uygulamasını tanımla
def application(environ, start_response):
    try:
        # Request bilgilerini logla
        req_info = log_request_info(environ)
        logging.info(f"Yeni istek: {req_info}")
        
        # Sitemap.xml ve robots.txt için özel işleme
        if environ.get('PATH_INFO', '') in ['/sitemap.xml', '/robots.txt']:
            return flask_app(environ, start_response)

        # Diğer istekler için normal işleme
        return flask_app(environ, start_response)
    
    except Exception as e:
        # Detaylı hata loglaması
        logging.error(
            f"Uygulama hatası:\n"
            f"Request: {log_request_info(environ)}\n"
            f"Hata: {str(e)}",
            exc_info=True
        )
        
        # Hata sayfası göster
        status = '500 Internal Server Error'
        response_headers = [('Content-type', 'text/html; charset=utf-8')]
        start_response(status, response_headers)
        
        error_page = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Sunucu Hatası</title>
            <style>
                body {{ font-family: Arial, sans-serif; padding: 20px; }}
                .error-box {{ 
                    background: #fff3f3; 
                    border: 1px solid #ffa7a7;
                    padding: 20px;
                    border-radius: 5px;
                }}
                .error-id {{
                    color: #666;
                    font-size: 0.8em;
                }}
            </style>
        </head>
        <body>
            <div class="error-box">
                <h1>Üzgünüz, bir hata oluştu.</h1>
                <p>Sistem yöneticileri bilgilendirildi.</p>
                <p class="error-id">Hata ID: {datetime.now().strftime('%Y%m%d%H%M%S')}</p>
            </div>
        </body>
        </html>
        """
        return [error_page.encode('utf-8')]

# Başlangıç loglaması
logging.info("Uygulama başlatıldı")
