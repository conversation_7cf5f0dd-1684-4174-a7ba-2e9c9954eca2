{% extends "admin/base.html" %}

{% block title %}Ülke Bazlı Ziyaretçi İstatistikleri{% endblock %}

{% block head %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .stats-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        background-color: #fff;
    }
    .country-flag {
        width: 24px;
        height: 16px;
        margin-right: 8px;
        border-radius: 2px;
        vertical-align: middle;
    }
    .progress {
        height: 8px;
        margin-top: 5px;
    }
    .country-row {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .country-row:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="container-fluid px-4">
    <h1 class="mt-4"><PERSON><PERSON><PERSON> Bazlı Ziyaretçi İstatistikleri</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('server_bp.server_info') }}">Sunucu Durumu</a></li>
        <li class="breadcrumb-item active">Ülke İstatistikleri</li>
    </ol>
    
    <!-- Özet Kartları -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5 class="card-title">Toplam Ziyaretçi</h5>
                    <h2 class="display-4">{{ total_visitors }}</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">Detaylar</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h5 class="card-title">Farklı Ülke</h5>
                    <h2 class="display-4">{{ country_count }}</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#">Detaylar</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Grafikler -->
    <div class="row">
        <!-- Pasta Grafiği -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Ülke Dağılımı
                </div>
                <div class="card-body">
                    <canvas id="countryPieChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Çubuk Grafiği -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    En Çok Ziyaret Eden Ülkeler
                </div>
                <div class="card-body">
                    <canvas id="countryBarChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Zaman Serisi Grafiği -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-line me-1"></i>
                    Günlük Ziyaretçi İstatistikleri
                </div>
                <div class="card-body">
                    <canvas id="dailyVisitorsChart" width="100%" height="30"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Ülke Tablosu -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-globe me-1"></i>
                    Ülke Detayları
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="countryTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Ülke</th>
                                    <th>Ziyaretçi Sayısı</th>
                                    <th>Yüzde</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for country in country_stats %}
                                <tr>
                                    <td>
                                        <img src="https://flagcdn.com/w20/{{ country[0].lower() }}.png" 
                                             class="country-flag" alt="{{ country[1] }} flag">
                                        {{ country[1] }}
                                    </td>
                                    <td>{{ country[2] }}</td>
                                    <td>{{ "%.2f"|format(country[2] / total_visitors * 100) if total_visitors else 0 }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Veri aktarımı için gizli div -->
<div id="chartData" 
     data-country-stats="{{ country_stats|tojson }}"
     data-daily-data="{{ daily_data|tojson }}"
     style="display: none;">
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
// Sayfa yüklendiğinde çalışacak kodlar
document.addEventListener('DOMContentLoaded', function() {
    // Veri div'inden verileri al
    var dataElement = document.getElementById('chartData');
    var countryStats = JSON.parse(dataElement.getAttribute('data-country-stats'));
    var dailyData = JSON.parse(dataElement.getAttribute('data-daily-data'));
    
    // Ülke verilerini düzenle
    var countryData = [];
    for (var i = 0; i < countryStats.length; i++) {
        countryData.push({
            code: countryStats[i][0],
            name: countryStats[i][1],
            visitors: countryStats[i][2]
        });
    }
    
    // En çok ziyaret eden 10 ülke
    var top10Countries = countryData.slice(0, 10);
    
    // Diğer ülkeleri birleştir
    var otherVisitors = 0;
    if (countryData.length > 10) {
        for (var i = 10; i < countryData.length; i++) {
            otherVisitors += countryData[i].visitors;
        }
    }
    
    // Pasta grafiği için veri hazırlama
    var pieLabels = [];
    var pieData = [];
    
    for (var i = 0; i < top10Countries.length; i++) {
        pieLabels.push(top10Countries[i].name);
        pieData.push(top10Countries[i].visitors);
    }
    
    if (otherVisitors > 0) {
        pieLabels.push('Diğer');
        pieData.push(otherVisitors);
    }
    
    // Pasta grafiği
    var pieCtx = document.getElementById('countryPieChart').getContext('2d');
    var pieChart = new Chart(pieCtx, {
        type: 'pie',
        data: {
            labels: pieLabels,
            datasets: [{
                data: pieData,
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#5a5c69', '#858796', '#6f42c1', '#fd7e14', '#20c9a6',
                    '#adb5bd' // Diğer için
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var label = context.label || '';
                            var value = context.raw || 0;
                            var total = context.chart.data.datasets[0].data.reduce(function(a, b) { return a + b; }, 0);
                            var percentage = ((value / total) * 100).toFixed(2);
                            return label + ": " + value + " (" + percentage + "%)";
                        }
                    }
                }
            }
        }
    });
    
    // Çubuk grafiği için veri hazırlama
    var barLabels = [];
    var barData = [];
    
    for (var i = 0; i < top10Countries.length; i++) {
        barLabels.push(top10Countries[i].name);
        barData.push(top10Countries[i].visitors);
    }
    
    // Çubuk grafiği
    var barCtx = document.getElementById('countryBarChart').getContext('2d');
    var barChart = new Chart(barCtx, {
        type: 'bar',
        data: {
            labels: barLabels,
            datasets: [{
                label: 'Ziyaretçi Sayısı',
                data: barData,
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Zaman serisi grafiği için veri hazırlama
    var lineLabels = [];
    var lineVisitors = [];
    var linePageViews = [];
    
    for (var i = 0; i < dailyData.length; i++) {
        lineLabels.push(dailyData[i].date);
        lineVisitors.push(dailyData[i].visitors);
        linePageViews.push(dailyData[i].page_views);
    }
    
    // Zaman serisi grafiği
    var lineCtx = document.getElementById('dailyVisitorsChart').getContext('2d');
    var lineChart = new Chart(lineCtx, {
        type: 'line',
        data: {
            labels: lineLabels,
            datasets: [{
                label: 'Ziyaretçi Sayısı',
                data: lineVisitors,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true
            }, {
                label: 'Sayfa Görüntüleme',
                data: linePageViews,
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // DataTables
    if (typeof $.fn !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
        $('#countryTable').DataTable({
            order: [[1, 'desc']]
        });
    }
});
</script>
{% endblock %} 