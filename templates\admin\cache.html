{% extends "admin/base.html" %}

{% block page_title %}<PERSON><PERSON>{% endblock %}
{% block breadcrumb %}Cache Yönetimi{% endblock %}

{% block admin_content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-memory text-primary me-2"></i>
            Cache Yönetimi
        </h1>
        <button onclick="refreshCacheInfo()" class="btn btn-outline-primary">
            <i class="fas fa-sync-alt me-1"></i>
            Yenile
        </button>
    </div>

    <!-- C<PERSON>tistikleri -->
    <div class="row mb-4">
        <!-- Flask Cache -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Flask Cache
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="flask-cache-status">
                                {{ cache_info.flask_cache.status if cache_info.flask_cache else 'Yükleniyor...' }}
                            </div>
                            <div class="text-xs text-gray-600" id="flask-cache-type">
                                {{ cache_info.flask_cache.type if cache_info.flask_cache else '' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session Cache -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Session Cache
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="session-cache-files">
                                {{ cache_info.session_cache.files if cache_info.session_cache else '0' }} dosya
                            </div>
                            <div class="text-xs text-gray-600" id="session-cache-size">
                                {{ cache_info.session_cache.size if cache_info.session_cache else '0 MB' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Static Cache -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Static Dosyalar
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="static-cache-files">
                                {{ cache_info.static_cache.files if cache_info.static_cache else '0' }} dosya
                            </div>
                            <div class="text-xs text-gray-600" id="static-cache-size">
                                {{ cache_info.static_cache.size if cache_info.static_cache else '0 MB' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log Dosyaları -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Log Dosyaları
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="logs-files">
                                {{ cache_info.logs.files if cache_info.logs else '0' }} dosya
                            </div>
                            <div class="text-xs text-gray-600" id="logs-size">
                                {{ cache_info.logs.size if cache_info.logs else '0 MB' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sistem Bellek Bilgisi -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-memory me-2"></i>
                        Sistem Bellek Kullanımı
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Toplam Bellek</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-total">
                                    {{ cache_info.system.memory_total if cache_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Kullanılan</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-used">
                                    {{ cache_info.system.memory_used if cache_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Kullanım Oranı</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-percent">
                                    {{ cache_info.system.memory_percent if cache_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Müsait</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="memory-available">
                                    {{ cache_info.system.memory_available if cache_info.system else 'Yükleniyor...' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Temizleme Butonları -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-broom me-2"></i>
                        Cache Temizleme İşlemleri
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button onclick="clearCache('flask')" class="btn btn-primary btn-block">
                                <i class="fas fa-server me-1"></i>
                                Flask Cache Temizle
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button onclick="clearCache('session')" class="btn btn-success btn-block">
                                <i class="fas fa-users me-1"></i>
                                Session Temizle
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button onclick="clearCache('static')" class="btn btn-info btn-block">
                                <i class="fas fa-file me-1"></i>
                                Geçici Dosyalar
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button onclick="clearCache('logs')" class="btn btn-warning btn-block">
                                <i class="fas fa-clipboard-list me-1"></i>
                                Log Temizle
                            </button>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12 text-center">
                            <button onclick="clearCache('all')" class="btn btn-danger btn-lg">
                                <i class="fas fa-broom me-2"></i>
                                TÜM CACHE'İ TEMİZLE
                            </button>
                            <div class="text-muted mt-2">
                                <small>Bu işlem tüm cache türlerini temizleyecektir.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearCache(type) {
    if (type === 'all') {
        if (!confirm('Tüm cache temizlenecek. Emin misiniz?')) {
            return;
        }
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Temizleniyor...';
    button.disabled = true;
    
    fetch('/admin/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({type: type})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            refreshCacheInfo();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Bir hata oluştu: ' + error);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function refreshCacheInfo() {
    fetch('/admin/cache/info')
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showAlert('error', data.error);
            return;
        }
        
        // Flask Cache
        if (data.flask_cache) {
            document.getElementById('flask-cache-status').textContent = data.flask_cache.status;
            document.getElementById('flask-cache-type').textContent = data.flask_cache.type;
        }
        
        // Session Cache
        if (data.session_cache) {
            document.getElementById('session-cache-files').textContent = data.session_cache.files + ' dosya';
            document.getElementById('session-cache-size').textContent = data.session_cache.size;
        }
        
        // Static Cache
        if (data.static_cache) {
            document.getElementById('static-cache-files').textContent = data.static_cache.files + ' dosya';
            document.getElementById('static-cache-size').textContent = data.static_cache.size;
        }
        
        // Logs
        if (data.logs) {
            document.getElementById('logs-files').textContent = data.logs.files + ' dosya';
            document.getElementById('logs-size').textContent = data.logs.size;
        }
        
        // System
        if (data.system) {
            document.getElementById('memory-total').textContent = data.system.memory_total;
            document.getElementById('memory-used').textContent = data.system.memory_used;
            document.getElementById('memory-percent').textContent = data.system.memory_percent;
            document.getElementById('memory-available').textContent = data.system.memory_available;
        }
    })
    .catch(error => {
        showAlert('error', 'Cache bilgileri yüklenirken hata oluştu: ' + error);
    });
}

function showAlert(type, message) {
    // Admin base template'ine uygun flash message oluştur
    const iconClass = type === 'success' ? 'fas fa-check text-green-600' : 'fas fa-exclamation text-red-600';
    const bgClass = type === 'success' ? 'bg-green-100' : 'bg-red-100';

    const alertHtml = `
        <div class="flash-message bg-white border border-gray-200 shadow-lg px-4 py-3 rounded-lg flex items-center justify-between min-w-80 max-w-md">
            <div class="flex items-center">
                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center mr-3">
                    <i class="${iconClass} text-sm"></i>
                </div>
                <span class="text-gray-800 text-sm font-medium">${message}</span>
            </div>
            <button class="close-flash ml-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded hover:bg-gray-100">
                <i class="fas fa-times text-xs"></i>
            </button>
        </div>
    `;

    // Fixed position container oluştur veya mevcut olanı kullan
    let container = document.querySelector('.flash-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'flash-container fixed top-4 right-4 z-50 space-y-3';
        document.body.appendChild(container);
    }

    container.insertAdjacentHTML('beforeend', alertHtml);

    const flashMessage = container.lastElementChild;

    // Close button event
    flashMessage.querySelector('.close-flash').addEventListener('click', () => {
        flashMessage.style.opacity = '0';
        flashMessage.style.transform = 'translateX(100%)';
        setTimeout(() => flashMessage.remove(), 300);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (flashMessage.parentNode) {
            flashMessage.style.opacity = '0';
            flashMessage.style.transform = 'translateX(100%)';
            setTimeout(() => flashMessage.remove(), 300);
        }
    }, 5000);
}

// Sayfa yüklendiğinde cache bilgilerini yenile
document.addEventListener('DOMContentLoaded', function() {
    refreshCacheInfo();
    
    // Her 30 saniyede bir otomatik yenile
    setInterval(refreshCacheInfo, 30000);
});
</script>
{% endblock %}
